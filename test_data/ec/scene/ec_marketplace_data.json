{"assert": {"expected_result": true, "message_id": "10000"}, "login_charlie": {"email": "<EMAIL>", "password": "123456", "phone": 1112223334, "referral_id": 0, "ep_partner": ""}, "scene_config": {"login_scene": {"email": "<EMAIL>", "password": "123456", "phone": 9315512539, "referral_id": 0, "ep_partner": ""}, "addr": {"addr_address": "47467 Fremont Blvd", "addr_apt": "", "addr_city": "Fremont", "addr_country": "2", "addr_firstname": "test", "addr_lastname": "QA", "addr_state": "41", "address": "47467 Fremont Blvd, Fremont, California, 98011, United States", "addr_zipcode": 98011}}, "signup": {"email": "<EMAIL>", "password": "A1234567", "phone": 5555555555, "referral_id": 0, "ep_partner": ""}, "login": {"email": "<EMAIL>", "password": "123", "phone": 12312312123, "referral_id": 0, "ep_partner": ""}, "user": {"user_id": 7694015}, "zipcode": {"CA": 98011}, "group_order": {"group_key": "gb_442961226453381"}, "seller_info": {"vendor_id": "7646", "ids": [6887, 7855, 7319, 6943, 6867], "vendor_list": ["7646", "7319"], "title": "面包公爵", "show_product_detail": "True"}, "origin_list": {"recommend": 0, "japan": 10, "korea": 11, "usa": 5, "others": -2}, "dataobject_key_global": {"source_key": "cm_mkpl_seller_line_sale", "dataobject_key_treding": "ds_mkpl_seller_line_395459", "ds_main_banner_recommend": "ds_main_banner_recommend_417874", "0": "ds_mkpl_seller_list_395461", "10": "ds_mkpl_seller_list_395462", "11": "ds_mkpl_seller_list_395463", "5": "ds_mkpl_seller_list_395464", "-2": "ds_mkpl_seller_list_395465"}, "biz_type": ["new", "trending", "sale", "more"], "dataobject_key": ["ds_item_seller_new", "ds_item_seller_trending", "ds_item_seller_sale", "ds_item_seller_more", "ds_mkpl_lightning"], "common": {"lang": "en", "date": "2023-07-23"}, "waterfall_category": {"default": ["feature", "bakery", "snack", "care", "grocery", "instant", "beverages", "seasoning", "dried", "canned", "frozen"], "es": ["breadtortilla", "snack", "care", "grocery", "beverages", "pantry", "frozen"], "in": ["snack", "care", "grocery", "beverages", "instant", "canned", "grainspulses", "spicesseasonings", "frozen", "green", "fruits"], "ph": ["bakerydesserts", "snacks", "care", "grocery", "beverages", "condimentssauces", "seasoningsmixes", "driedseafood", "canned", "dried", "frozen", "cookmeatseafood"], "ko": ["bakery", "snacks", "care", "grocery", "kimchi", "<PERSON><PERSON>", "cookmeals", "beverages", "seasonings", "pantry", "frozen"], "category_library_type": ["store_es", "store_in", "store_ph", "store_ko", "lang_es", "lang_ko", "default"], "category_type": ["in", "es", "ko", "ph"], "device_id": "A_2278AE22-AB86-4454-93A4-178DFD11DFF2", "user_id": "8092321"}, "topX": {"topselling": "top_selling", "toptrending": "top_trending", "dataobject_key": "ds_item_mkpl_top_1656039"}, "fbw_bakery": {"share_tittle_en": "Weee! Local Fresh Bakery | Gourmet bakery goods, delivered", "share_desc_en": "From the oven to your table", "header_title_en": "Local Fresh Bakery", "header_title_en_v2": "Local Fresh\nbakery", "header_sub_title_en": "Introducing", "header_desc_en": "Fresh from your favorite bakery stores", "text_array": ["Freshly baked everyday", "Same in-store price, no markups", "No extra delivery fee with your next grocery", "Shop multiple bakeries in the same order", "Order any items from different bakeries with no limits"], "text_array_v2": ["No delivery fee with your grocery order", "Same in-store price, no markups"]}, "fbw_freshdeli": {"share_tittle_en": "Weee! local fresh gourmet | restaurant food delivered", "share_desc_en": "In-store prices, no markups", "header_title_en": "Local Fresh Gourmet", "header_title_en_v2": "Local Fresh\ngourmet", "header_sub_title_en": "Introducing", "header_desc_en": "Fresh from your favorite bakery stores", "text_array": ["Freshly baked everyday", "Same in-store price, no markups", "No extra delivery fee with your next grocery", "Shop multiple bakeries in the same order", "Order any items from different bakeries with no limits"], "text_array_v2": ["No delivery fee with your grocery order", "Same in-store price, no markups"]}}