import datetime
import os.path
from typing import Union, List


def write_debug_log_on_jen<PERSON>(file_name: str, content: Union[str, List[str]]) -> None:
    if not os.path.exists("./logs"):
        os.mkdir("./logs")
    try:
        with open(file_name, "w", encoding="utf-8") as fp:
            fp.write("\n" + str(datetime.datetime.now()) + "<========>" + content + "\n")
    except Exception as e:
        print("write file failed." + str(e))
