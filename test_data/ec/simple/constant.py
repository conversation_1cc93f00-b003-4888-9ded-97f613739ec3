# -*- coding:utf-8 -*-
import random

# login
email = "<EMAIL>"
password = "A1234567"
login_params = {
    "email": email,
    "password": password,
    "referral_id": 0,
    "ep_partner": ""
}
signup_params = {
    "email": f'{random.randint(10 ** 9, (10 ** 10) - 1)}@aitest.com',
    "password": password,
    "referral_id": 0,
    "ep_partner": ""
}
# ios header
# header_init = {
#     "authorization": "",
#     "zipcode": 98011,
#     "Content-Type": "application/json;charset=UTF-8",
#     "app_version": "17.2",
#     "system-lang": "zh-CN",
#     "lang": "zh",
#     "device-id": "2D1DFC5C-CE51-4E79-969E-014FA8753C65",
#     "version": "v3",
#     "weee-session-token": "875773",
#     "B-Cookie": "880582",
#     "platform": "ios",
#     "user-agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1"
# }
# h5 header
header_init = {
        # "accept": "application/json, text/plain, */*",
        "Content-Type": "application/json;charset=UTF-8",
        "app-version": "null",
        "Authorization": "",
        "B-Cookie": "",
        "Lang": "en",
        "Platform": "h5",
        # "user-agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1",
        "User-Agent": "WeeeApp",
        "weee-session-token": "",
        "Weee-Store": "cn",
        "Zipcode": "98011",
        "Weee-Zipcode": "98011",
        "special-tag": "ec_automation"
    }


# restaurant portal
rtg_portal_param = {"account": "lei.chen", "password": "lei999873", "realm_key": "marketplace"}
rtg_portal_headers = {
    'Accept': 'application/json, text/plain, */*',
    'Content-Type': 'application/json'
}
