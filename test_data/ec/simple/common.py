import copy
import json
import random

from weeeTest import jmespath, log, weeeConfig

import weeeTest
from weeeTest.token.authorization import Authorization
from weeeTest.token.email_login import EmailLogin
from weeeTest.token.email_sigup import EmailSignup
from weeeTest.token.session import Session

from test_data.ec.simple.writejenkinslog import write_debug_log_on_jenkins
from test_dir.api.ec.central_portal.auth import Auth

init_header = {
    "Content-Type": "application/json;charset=UTF-8",
    "app-version": "null",
    "Authorization": "",
    "B-Cookie": "",
    "Lang": "en",
    "Platform": "h5",
    "User-Agent": "WeeeApp",
    "weee-session-token": "",
    "Weee-Store": "cn",
    "Zipcode": "98011",
    "Weee-Zipcode": "98011",
    "special-tag": "ec_automation"
}


class Header(weeeTest.TestCase):

    @staticmethod
    def anony_header():
        try:
            anony_header = copy.deepcopy(init_header)
            anony = Authorization().anon_auth()
            anony_token = jmespath(anony, "object.token")
            anony_header['Authorization'] = 'Bearer ' + anony_token
            session_res = Session().anon_session(anony_header)
            anony_session = jmespath(session_res, "object.weee_session_token")
            anony_header['Weee-Session-Token'] = str(anony_session)
            anony_header['B-Cookie'] = str(anony_session)
            log.info("anony_header===>", anony_header)
            return anony_header
        except Exception as e:
            log.info("第一次获取anony_header失败: " + str(e))
            try:
                anony_header = copy.deepcopy(init_header)
                anony = Authorization().anon_auth()
                anony_token = jmespath(anony, "object.token")
                anony_header['Authorization'] = 'Bearer ' + anony_token
                session_res = Session().anon_session(anony_header)
                anony_session = jmespath(session_res, "object.weee_session_token")
                anony_header['Weee-Session-Token'] = str(anony_session)
                anony_header['B-Cookie'] = str(anony_session)
                log.info("anony_header===>", anony_header)
                return anony_header
            except Exception as e:
                log.info("第二次获取anony_header失败: " + str(e))

    @staticmethod
    def login_header(email: str = "<EMAIL>",
                     password: str = "A1234567"):
        log.info(f">>>>>>>>>>> begin to get ec login header for {email} <<<<<<<<<<<<")
        login_header = copy.deepcopy(init_header)
        login = None
        try:
            login = EmailLogin().email_login(headers=Header.anony_header(), email=email, password=password)
        except Exception as e:
            log.info("登陆失败" + str(e))

        try:
            token = jmespath(login, "object.token")
            login_header['Authorization'] = 'Bearer ' + token
            session_res = Session().anon_session(login_header)
            login_session = jmespath(session_res, "object.weee_session_token")
            # log.info(anony_session)
            login_header['Weee-Session-Token'] = str(login_session)
            login_header['B-Cookie'] = str(login_session)

            return login_header
        except Exception as e:
            log.info(f"第一次获取login_header失败, email is {email}, password is {password}" + str(e))
            try:
                login = EmailLogin().email_login(headers=Header.anony_header(), email=email, password=password)
            except Exception as e:
                log.info("登陆失败" + str(e))

            try:
                token = jmespath(login, "object.token")
                login_header['Authorization'] = 'Bearer ' + token
                session_res = Session().anon_session(login_header)
                login_session = jmespath(session_res, "object.weee_session_token")
                # log.info(anony_session)
                login_header['Weee-Session-Token'] = str(login_session)
                login_header['B-Cookie'] = str(login_session)

                return login_header
            except Exception as e:
                log.info(f"第二次获取login_header失败, email is {email}, password is {password}" + str(e))

    @staticmethod
    def signup_header(email: str = f"{random.randint(10 ** 9, (10 ** 10) - 1)}@autest.com",
                      password: str = "A1234567"):
        signup_header = copy.deepcopy(init_header)
        try:
            signup = EmailSignup().email_signup(headers=Header.anony_header(), email=email, password=password)
            token = signup.get("object").get("token")
            signup_header['Authorization'] = 'Bearer ' + token
            session_res = Session().anon_session(signup_header)
            login_session = jmespath(session_res, "object.weee_session_token")
            signup_header['Weee-Session-Token'] = str(login_session)
            signup_header['B-Cookie'] = str(login_session)
            return signup_header
        except Exception as e:
            log.info("获取signup_header失败" + str(e))

    @staticmethod
    def portal_sales_header(user_id: int = 10923004, password: str = "A1234567", login_platform: str = "WMS"):
        """
        获取sales portal端的header
        """
        portal_header = copy.deepcopy(init_header)
        auth_login = None
        try:
            """ 后台登录"""
            auth_login = Auth().central_login(headers=Header.anony_header(), user_id=user_id, password=password,
                                              login_platform=login_platform)
        except Exception as e:
            log.info("登陆失败" + str(e))

        try:
            token = jmespath(auth_login, "object.token")
            portal_header['authorization'] = 'Bearer ' + token
            session_header = portal_header
            session_res = Session().anon_session(session_header)
            login_session = jmespath(session_res, "object.weee_session_token")
            # log.info(anony_session)
            portal_header['weee-session-token'] = str(login_session)
            portal_header['b-cookie'] = str(login_session)

            # log.info(f'>>>|login_header  {self.header}')
            return portal_header
        except Exception as e:
            log.info("获取token失败" + str(e))
            write_debug_log_on_jenkins("./logs/fail_token.txt", "\nlogin_respons: \n" + json.dumps(
                auth_login) + " account: " + str(user_id) + " password: " + password + "\n"
                                       + " anony_header: " + json.dumps(Header.anony_header()))
            return {}



