# -*- coding: utf-8 -*-
"""
<AUTHOR>  Zhongyuan.Xu
@Version        :  V1.0.0
------------------------------------
@Description    :
@CreateTime     :  2023/12/7
@Software       :  PyCharm
------------------------------------
"""
import datetime
import uuid

import pytest
import weeeTest
from weeeTest import log

from test_data.ec.simple.common import Header
from test_data.ec.simple.uploadfiles import upload_multi_files
from test_dir.api.ec.ec_so.preorder.update_preorderline import UpdatePreOrderLine
from test_dir.api.ec.ec_social.community.community import Community
from test_dir.api.ec.ec_social.event.event import Event
from test_dir.api.ec.ec_social.post_info.post_info import PostInfo
from test_dir.api.ec.ec_social.recommend_post_info.recommend_portal import RecommendPortal
from test_dir.api.ec.ec_social.recommend_post_info.recommend_post_info import RecommendPostInfo
from test_dir.api.ec.ec_social.review_info.review_info import ReviewInfo
from test_dir.api_case.ec.common.comm_check_products_with_cart import CommCheckProductsWithCart
from test_dir.api_case.ec.common.comm_cms import CommCheckFunction
from test_dir.api_case.ec.common.common_check import CommonCheck
from test_dir.api_case.ec.common.remove_all_products_in_cart import RemoveAllProductsInCart


class TestCommunity(weeeTest.TestCase):
    @classmethod
    def setup_class(cls):
        cls.cm = Community()

    @weeeTest.mark.list('Regression', 'Social', 'Smoke', 'qa')
    def test_to_review_data_within_one_month(self, ec_login_header):
        """获取account页面 to review近一个月的订单商品数"""
        self.cm.get_to_reviews_from_account(ec_login_header)
        my_orders = self.response["object"]["sections"]
        my_orders_data = list(filter(lambda f: f["section_type"] == "order", my_orders))[0]["data"]["items"]
        # 1. 获得Me页面上to_review的数量(int)
        to_review_num = list(filter(lambda f: f["item_name"] == "To review", my_orders_data))[0]["item_value"]
        # 2. 获取to_review 列表页面的product数量，与me页面对比
        self.cm.get_to_reviews_from_detail(ec_login_header)
        to_review_num_in_detail = self.response["object"]["product_total"]
        # 校验2个页面的数据相等
        assert to_review_num == str(to_review_num_in_detail)

    @weeeTest.mark.list('Social')
    # @weeeTest.mark.skip("每次上传文件（视频文件），对服务器空间造成压力")
    def test_upload_for_to_review(self, ec_login_header):
        data = {
            "requestData": """{
                "data": [
                    {
                        "files": ["1.mp4"],
                        "subType": "video"
                    },
                    {
                        "files": ["2.jpeg"],
                        "subType": "thumbnail"
                    }
                ],
                "bizType": "social"
            }"""
        }

        files = [
            ("file", ("1.mp4", open("uploadimage/1.mp4", "rb"))),
            ("file", ("2.jpeg", open("uploadimage/2.jpeg", "rb")))
        ]

        Community().upload_file_v2_mix(headers=ec_login_header, data=data, files=files)
        print("====>", self.response)

    @weeeTest.mark.list('Social', 'tb1')
    def test_community_show(self, ec_login_header):
        """生成post"""
        # 由于上传视频，所以只在tb1运行
        # step 1. 获取post review所需要的ref_url
        files = [
            ("file", ("1.mp4", open("uploadimage/1.mp4", "rb"))),
            ("file", ("2.jpeg", open("uploadimage/2.jpeg", "rb")))
        ]
        res = upload_multi_files(
            url="https://api.tb1.sayweee.net/resource/v2/upload/mix",
            headers=ec_login_header,
            data={
                "requestData": """{
                "data": [
                    {
                        "files": ["1.mp4"],
                        "subType": "video"
                    },
                    {
                        "files": ["2.jpeg"],
                        "subType": "thumbnail"
                    }
                ],
                "bizType": "social"
            }"""
            },
            files=files
        )

        assert len(res.json()["object"]) == 2
        assert res.json()["message_id"] == '10000'

        # step 2. 获取商品Id [!!!必须清空header，只保留authorization，否则接口报错]
        # temp = headers_b["authorization"]
        # headers_b.clear()
        # headers_b["authorization"] = temp
        # ApiFavorites().my_favorite_product_list_v2(headers=headers_b, filter_sub_category="")

        product_list = [item["id"] for item in self.response["object"]["products"]]

        # step 3. post review
        # self.cm.post_show(headers_b, **{
        #     "product_ids": str(random.choice(product_list)),
        #     "ref_url": res.json()["object"][0]["url"] if "mp4" in res.json()["object"][0]["url"] else
        #     res.json()["object"][1]["url"]
        # })
        # 发布成功，则生成postId
        assert self.response["object"]["id"] > 0

        # 3. 后台审理 （由于base_url不同，所以需要单独的接口）
        # 目前正在咨询如何拿到token，登陆需要2次验证，自动化无法获取token
        # 结论：与素芬沟通，后台的先不做

    @pytest.mark.parametrize("keyword",
        ["fresh bakery", "Matcha Season", "kimchi", "eggs", "mango", "rice", "tofu", "ramen", "mushroom", "shrimp",
         "kimbap", "garlic", "chicken"])
    @weeeTest.mark.list('Regression', 'Social', 'product')
    def test_social_query_item(self, keyword, ec_login_header):
        """
        从social接口进入，验证搜索功能
        """
        # 1. 接索结果分为：video, account, tags
        query_result = PostInfo().query_items_in_social(
            ec_login_header,
            {
                "keyword": keyword,
                "start_id": None
            }
        )
        result_type = [item['type'] for item in query_result['object']['list']]
        assert query_result['object'][
            'list'], f"social首页未搜索到{keyword}的相关视频与分享，接口返回结果为：{query_result}"
        available_list = [item for item in query_result['object']['list'] if item['status'] == 'P']
        # 2. 点击进入详情页
        for index, item in enumerate(available_list):
            query_item_detail = self.get(url="", special_url=item['link'], headers=ec_login_header)
            assert query_item_detail.status_code == 200 and str(
                item['id']) in query_item_detail.text, f"访问详情页失败，link为：{item['link']}, item为{item}"

            if index == 0:
                if item["type"] == 'video':
                    PostInfo().praise_post(ec_login_header, item['id'])
                    assert self.response['result'], f"热门视频点赞失败，接口返回结果为：{self.response}, item={item}"
                if item["type"] == 'review':
                    ReviewInfo().praise_review(
                        headers=ec_login_header,
                        reviewId=str(item['id']),
                        status="A"
                    )
                    assert self.response['result'], f"type=review点赞失败，接口返回结果为：{self.response}, item={item}"
            # 详情页底部的推荐
            if 'video' in item['link']:
                RecommendPostInfo().query_post_detail_recommend_info(
                    {"post_id": item['id']},
                    ec_login_header
                )
                assert isinstance(self.response['object'], list) and self.response[
                    'result'] is True, f"接口返回为：{self.response}, item为：{item}, index为：{index}"

    @weeeTest.mark.list('Regression', 'Social', 'Smoke', 'product', 'qa')
    def test_social_main_page_activity(self, ec_jiufen_header):
        """ social 首页活动"""
        # 1. 进入活动列表
        social_activity = Event().social_event_channel(ec_jiufen_header, {"channel": True})
        assert social_activity['object']['list'], f'social首页没有活动，接口返回结果为：{social_activity}'
        # 2. 进入活动详情页
        available_social_activity = [item for item in social_activity['object']['list'] if item['status'] == 'P']
        for index, item in enumerate(available_social_activity):
            activity_detail = self.get(url="", special_url=item['url'], headers=ec_jiufen_header)
            assert item['title'] in activity_detail.text, f"活动详情页没有标题，接口返回结果为：{item}"

            activity_popular = Event().social_channel_detail_items(ec_jiufen_header, item['id'],
                                                                   {"sort": "popular"})
            assert activity_popular['object'][
                'list'], f"活动没有热门视频，接口返回结果为：{activity_popular}, item为{item}"

            # 3. 点赞热门视频
            popular_video_ids = [item['id'] for item in activity_popular['object']['list']]
            for v_id in popular_video_ids:
                if index == 0:
                    PostInfo().praise_post(ec_jiufen_header, v_id)
                    assert self.response['result'], f"热门视频点赞失败，接口返回结果为：{self.response}"
                    # 只点一次，否则触发防刷机制
                    break

            # 4. 点赞最新视频
            activity_latest = Event().social_channel_detail_items(ec_jiufen_header, item['id'],
                                                                  {"sort": "latest"})
            latest_video_ids = [item['id'] for item in activity_latest['object']['list']]
            for v_id in latest_video_ids:
                if index == 0:
                    PostInfo().praise_post(ec_jiufen_header, v_id)
                    assert self.response['result'], f"最新视频点赞失败，接口返回结果为：{self.response}"
                    # 只点一次，否则触发防刷机制
                    break

    @weeeTest.mark.list('Social')
    def test_to_review(self, ec_login_header):
        """ 从待晒单页面去晒单 """
        # 移至weekly运行
        need_review_orders = self.cm.get_to_reviews_from_detail(ec_login_header)
        # 并不是每次都有待晒的单，所以不用assert断言
        if need_review_orders['object']['list']:
            need_review_list = [[order['order_id'], item['id']] for order in need_review_orders['object']['list'] for
                                item in
                                order['products']]
            # 只能晒一单，否则可以触发拦截机制
            self.cm.social_review(
                ec_login_header,
                {
                    "image_urls": ["https://img06.weeecdn.com/social/image/825/001/1B0A13E770CFF4FB.png"],
                    "order_id": need_review_list[0][0],
                    "product_id": need_review_list[0][1],
                    "comment": "这个东西不错，吃得很好",
                    "rating": 5,
                    "source": "post_review_my_orders"
                }
            )
            assert self.response['result'] is True and self.response[
                'message_id'] == '10000', f"晒单失败，接口返回结果为：{self.response}"
        else:
            log.info("没有待晒单的数据")
            # pytest.skip("没有待晒单的数据")

    @weeeTest.mark.list('Regression', 'Social', 'Smoke', 'product', 'qa')
    def test_show_user_posts(self, ec_login_header):
        """查看我的晒单"""
        reviewed_orders = self.cm.query_user_review_list(
            ec_login_header,
            {
                "type": "review",
                "status": "all",
                "page_source": "my_review"
            }
        )
        # 不能保证已晒单列表有数据
        if reviewed_orders['object']['list']:
            # 进入商品详情页
            view_links = [[item['product']['name'], item['product']['view_link']] for item in
                          reviewed_orders['object']['list']]
            for name, link in view_links:
                # self.get(url="", special_url=link, headers=RequestHeader.ec_login_header)
                # assert name in self.response
                CommCheckFunction().comm_check_link(link, headers=ec_login_header)

            products = [product["product"] for product in reviewed_orders['object']['list'] if
                        product["product"]['sold_status'] == 'available']
            for product in products:
                # 加购晒单的商品
                UpdatePreOrderLine().porder_items_v2(
                    headers=ec_login_header,
                    product_id=product['id'],
                    quantity=product['min_order_quantity']
                )
                # 判断加购成功
                assert self.response["result"] is True and len(
                    self.response["object"]["updateItems"]) > 0, f"product id is {product['id']}"
                CommCheckProductsWithCart().check_product_exists_in_cart(
                    headers=ec_login_header,
                    cart_domain="grocery",
                    product_id=product['id']
                )
                # 清空购物车
                RemoveAllProductsInCart().remove_all_products_in_grocery_cart(headers=ec_login_header)
        else:
            log.info("已晒单列表无数据")
            pytest.skip("已晒单列表无数据")

    @pytest.mark.parametrize("page_num", [1, 2, 3])
    @weeeTest.mark.list('Regression', 'Social', 'Smoke', 'product', 'qa')
    def test_social_home_page_check(self, page_num, ec_login_header):
        """social首页数据验证"""
        # 清空购物车
        RemoveAllProductsInCart().remove_all_products_in_grocery_cart(headers=ec_login_header)
        # 首页推荐
        social_recommend = RecommendPortal().social_recommended(headers=ec_login_header,
                                                                recommend_session=str(uuid.uuid4()),
                                                                page_num=page_num

                                                                )
        assert social_recommend['object']['contents'], f"social首页推荐没有数据，接口返回为：{social_recommend}"

        for i, item in enumerate(social_recommend['object']['contents']):
            if item['type'] == "normal_content_video":
                if i % 3 == 0:
                    CommCheckFunction().comm_check_link(item['data']['normal_content_video']['link'], headers=ec_login_header)
                # 加购normal_content_video商品
                product = item['data']['normal_content_video']['product'] if item['data']['normal_content_video'][
                    'product'] else {}
                if product.get("id") and product.get("sold_status") == 'available':
                    UpdatePreOrderLine().porder_items_v3(
                        headers=ec_login_header,
                        product_id=product.get("id"),
                        quantity=product.get('min_order_quantity')
                    )
                    assert self.response["result"] is True and len(
                        self.response["object"][
                            "updateItems"]) > 0, f"product id is {item[0]}, response is {self.response}"
                    CommCheckProductsWithCart().check_product_exists_in_cart(
                        headers=ec_login_header,
                        cart_domain="grocery",
                        product_id=product.get("id")
                    )
                else:
                    log.info("normal_content_video的商品不可购买")

            if item['type'] == "recipe_content_video":
                if i % 3 == 0:
                    CommCheckFunction().comm_check_link(item['data']['recipe_content_video']['link'], headers=ec_login_header)

                # 加购recipe_content_video商品
                recipe_content_video_product = item['data']['recipe_content_video']['product'] if \
                    item['data']['recipe_content_video']['product'] else {}
                if recipe_content_video_product.get("id") and recipe_content_video_product.get(
                        "sold_status") == 'available':
                    UpdatePreOrderLine().porder_items_v3(
                        headers=ec_login_header,
                        product_id=recipe_content_video_product.get("id"),
                        quantity=recipe_content_video_product.get('min_order_quantity')
                    )
                    assert self.response["result"] is True and len(
                        self.response["object"][
                            "updateItems"]) > 0, f"product id is {item[0]}, response is {self.response}"
                    CommCheckProductsWithCart().check_product_exists_in_cart(
                        headers=ec_login_header,
                        cart_domain="grocery",
                        product_id=recipe_content_video_product.get("id")
                    )
                else:
                    log.info("recipe_content_video的商品不可购买")

        # 首页category top验证
        category_top = RecommendPortal().social_home_top(
            ec_login_header,
            {
                "lang": "en",
                "date": str(datetime.date.today()),
                "dataobject_key": "ds_banner_array"
            }
        )
        assert category_top['object']['data'], f"social首页没有返回top category, 接口返回结果为{category_top}"
        category_keys = [item["key"] for item in category_top['object']['data']]
        category_titles = [item["title"] for item in category_top['object']['data']]
        assert CommonCheck.list_check(["topic", "recipe", "event"], category_keys), f"接口返回结果为{category_top}"
        assert CommonCheck.list_check(["Topics", "Recipes", "Events"], category_titles), f"接口返回结果为{category_top}"

        # top category链接验证 (数据验证会在新的用例进行)
        for j, item in enumerate(category_top['object']['data']):
            if j % 3 == 0:
                CommCheckFunction().comm_check_link(item['link_url'], headers=ec_login_header)

    # recipe_category = Community().get_social_category_list(headers=RequestHeader.ec_login_header)
    # sub_category = [s['key'] for c in recipe_category['object'] if c['key'] == 'recipe' for s in c['sub_category']]

    headers = Header.login_header()
    log.debug(f"test_community.py headers: {headers}")
    sub_category = Community().get_social_sub_category_list(headers=headers)

    # @pytest.mark.parametrize('key',
    #                          ["new", "chinese", "japanese", "korean", "vietnamese", "filipino", "hispanic", "indian",
    #                           "thai", "fusion"])
    # @pytest.mark.parametrize('page', [1, 2])
    # @weeeTest.mark.list('Regression-skip', 'Social-skip', 'Smoke-skip', 'product-skip')
    # def test_social_recipes_check(self, key, page):
    #     """social recipes页验证"""
    #     # 已被下面动态用例覆盖
    #     # 1. 获取首页各种类型的recipes，类型见参数key
    #     self.cm.ec_category_post(
    #         headers=RequestHeader.ec_login_header,
    #         data={
    #             "key": key,
    #             "page": page,
    #             "limit": 10,
    #             "start_id": -1
    #         }
    #     )
    #     assert self.response['object']['list'], f"recipes接口返回为空，key={key}, 结果为：{self.response}"
    #     available_list = [item for item in self.response['object']['list'] if
    #                       item['status'] == 'P' or item['status'] == 'T']
    #     # 2. 每个Post详情页验证
    #     for k, item in enumerate(available_list):
    #         if k % 3 == 0:
    #             CommCheckFunction().comm_check_link(item['link']), f"key={key}"
    #         RecommendPostInfo().query_post_detail_recommend_info(
    #             headers=RequestHeader.ec_login_header,
    #             data={"post_id": item['id']}
    #         )
    #         assert isinstance(self.response['object'], list) and self.response[
    #             'result'] is True, f"接口返回为：{self.response}, item为：{item}"

    @pytest.mark.parametrize('status', [None, "O", "U", "P"])
    @pytest.mark.parametrize('page', [1, 2])
    @weeeTest.mark.list('Regression', 'Social', 'Smoke', 'product', 'event_check')
    def test_social_event_check(self, status, page, ec_login_header):
        """social首页活动校验"""
        # None：全部活动，O:ongoing, U:upcoming, P:past
        events = Event().social_event_channel(
            headers=ec_login_header,
            data={
                "status": status,
                "limit": 10,
                "page": page
            }
        )
        assert CommonCheck.list_check(['link_url', 'list', 'page', 'total'], events['object'].keys())
        if status is None or status == 'P':
            assert events['object']['list'], f"status={status}的活动未返回任何数据，请检查数据，接口返回结果为：{events}"

        if status == "O" and events['object']['list']:
            for item in events['object']['list']:
                CommCheckFunction().comm_check_link(item['url'], headers=ec_login_header)
                # 热门视频校验
                popular = Event().social_channel_detail_items(
                    headers=ec_login_header,
                    activity_id=item['id'],
                    data={
                        "sort": "popular"
                    }
                )
                assert popular['object']['list'], f"正在进行的活动没有热门视频，接口返回数据为：{popular}, item={item}"
                popular_available = [item for item in popular['object']['list'] if item['status'] == 'P']
                for j, p_item in enumerate(popular_available):
                    if j % 3 == 0:
                        CommCheckFunction().comm_check_link(p_item['link'], headers=ec_login_header)
                # 最新视频校验
                latest = Event().social_channel_detail_items(
                    headers=ec_login_header,
                    activity_id=item['id'],
                    data={
                        "sort": "latest"
                    }
                )
                assert latest['object']['list'], f"正在进行的活动没有热门视频，接口返回数据为：{latest}"
                latest_available = [item for item in latest['object']['list'] if
                                    item['status'] == 'P' or item['status'] == 'A' or item['status'] == 'T']
                for t, l_item in enumerate(latest_available):
                    if t % 3 == 0:
                        CommCheckFunction().comm_check_link(l_item['link'], headers=ec_login_header)

    @staticmethod
    def _create_test_cases_name(params: dict):
        return [(f"test_social_recipes_{k}_{v}", v) for k in params.keys() for v in params.get(k)]

    test_cases_for_sub_categories = _create_test_cases_name(sub_category)

    @pytest.mark.parametrize("test_name, sub_category_name", test_cases_for_sub_categories)
    @weeeTest.mark.list("social", "Smoke", "single", "Regression", "qa")
    def test_dynamic(self, test_name, sub_category_name, ec_login_header):
        # 参数过多，会导致jenkins重启
        self.cm.ec_category_post(
            headers=ec_login_header,
            data={
                "key": sub_category_name,
                "page": 1,
                "limit": 10,
                "start_id": -1
            }
        )
        assert self.response['object']['list'], f"recipes接口返回为空，key={sub_category_name}, 结果为：{self.response}"
        available_list = [item for item in self.response['object']['list'] if
                          item['status'] == 'P' or item['status'] == 'T']

        # 2. 每个Post详情页验证
        for p, item in enumerate(available_list):
            if p % 3 == 0:
                CommCheckFunction().comm_check_link(item['link'], headers=ec_login_header), f"key={sub_category_name}"
            RecommendPostInfo().query_post_detail_recommend_info(
                headers=ec_login_header,
                data={"post_id": item['id']}
            )
            assert isinstance(self.response['object'], list) and self.response[
                'result'] is True, f"接口返回为：{self.response}, item为：{item}"
