# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_user.py
@Description    :
@CreateTime     :  2023/5/16 15:23
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 15:23
"""

import weeeTest

from test_dir.api.ec.ec_customer.signup_rest.email_sigup import EmailSignup


class TestSignUpPhone(weeeTest.TestCase):

    # 通过数据库参数化执行

    @weeeTest.data.file(file_name='ec_customer_data.json', return_type='dict')
    @weeeTest.mark.list('Regression-skip', 'Social', "Transaction", "tb1")
    @weeeTest.mark.skip("还没写")
    def test_signup_by_phone(self, *args, ec_anony_header):
        """注册-邮箱注册流程验证-非邀请好友方式"""
        # 注册用户用你不迁移到线上
        EmailSignup().email_signup(headers=ec_anony_header)
        assert self.response["result"] is True


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net', env='tb1')
