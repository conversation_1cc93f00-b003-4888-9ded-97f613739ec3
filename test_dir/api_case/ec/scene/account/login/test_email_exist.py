# -*- coding: utf-8 -*-
"""
<AUTHOR>  sufen xu
@Version        :  V1.0.0
------------------------------------
@Description    :  
@CreateTime     :  2023/7/19
@Software       :  PyCharm
------------------------------------
"""

import weeeTest

from test_dir.api_case.ec.common.common_check import CommonCheck


class TestUser(weeeTest.TestCase):
    @weeeTest.mark.list('Regression', 'Smoke', 'Social')
    def test_login(self, ec_login_header):
        """登录-老用户邮箱登录"""
        assert ec_login_header["Zipcode"] == '98011'
        CommonCheck.list_check(['Content-Type', 'Authorization', 'Platform', 'User-Agent', 'Weee-Session-Token'], ec_login_header.keys())



