# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_user.py
@Description    :
@CreateTime     :  2023/5/16 15:23
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 15:23
"""

import weeeTest
from test_dir.api.ec.ec_customer.login_rest.email_login import EmailLogin


class TestEmailLoginSimple(weeeTest.TestCase):

    # 通过数据库参数化执行

    @weeeTest.data.file(file_name='ec_customer_data.json', return_type='dict')
    @weeeTest.mark.list('Regression', 'Smoke',  'Transaction')
    def test_email_login_by_email(self, *args, ec_anony_header):
        """登录-非邀请好友方式-邮箱登录流程验证"""

        EmailLogin().email_login(headers=ec_anony_header, email=args[0]["login"]["email"],
                                 password=args[0]["login"]["password"])
        assert self.response["result"] is True, f"self.response为：{self.response}"
        assert isinstance(self.response['object']['token'], str), f"self.response['object']['token']为：{self.response['object']['token']}"
        assert isinstance(self.response['object']['user_id'], str) and self.response['object']['user_id'] != '', f"self.response为：{self.response}"

    @weeeTest.data.file(file_name='ec_customer_data.json', return_type='dict')
    @weeeTest.mark.list('Regression', 'Smoke',  'Transaction')
    def test_email_login_by_email_ref(self, *args, ec_anony_header):
        """登录-邮箱登录流程验证-邀请好友方式"""
        EmailLogin().email_login(headers=ec_anony_header, email=args[0]["login"]["email"],
                                 password=args[0]["login"]["password"],
                                 referral_id=args[0]["login"]["referral_id"],
                                 referrer_id=args[0]["login"]["referral_id"])
        assert self.response["result"] is True, f"self.response为：{self.response}"
        assert isinstance(self.response['object']['token'], str), f"self.response['object']['token']为：{self.response['object']['token']}"
        assert isinstance(self.response['object']['user_id'], str) and self.response['object']['user_id'] != '', f"self.response为：{self.response}"

    @weeeTest.data.file(file_name='ec_customer_data.json', return_type='dict')
    @weeeTest.mark.list('B2B','Regression', 'Smoke',  'Transaction')
    def test_b2b_email_login_by_email(self, *args, ec_anony_header):
        """登录-非邀请好友方式-邮箱登录流程验证"""

        EmailLogin().email_login(headers=ec_anony_header, email=args[0]["login"]["email"],
                                 password=args[0]["login"]["password"])
        assert self.response["result"] is True, f"self.response为：{self.response}"
        assert isinstance(self.response['object']['token'], str), f"self.response['object']['token']为：{self.response['object']['token']}"
        assert isinstance(self.response['object']['user_id'], str) and self.response['object']['user_id'] != '', f"self.response为：{self.response}"


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net', env='tb1')
