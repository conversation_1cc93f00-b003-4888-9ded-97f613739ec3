"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@Description    :
@CreateTime     :  2024/3/27
@Software       :  PyCharm
------------------------------------
"""
import random

import pytest
import weeeTest
from test_dir.api.ec.ec_customer.points.points import Points



class TestMePoint(weeeTest.TestCase):
    @pytest.mark.parametrize("status", ["active", "inactive"])
    @weeeTest.mark.list('test_me_point_page_info', 'Regression', 'Smoke',  'Transaction')
    def test_me_point_page_info(self, status, ec_login_header):
        """我的积分页面信息验证"""
        point_page = Points().points_transaction_list(headers=ec_login_header, type=status)
        assert point_page['result'] is True, f"point_page_info为：{point_page}"
        result_map = point_page['object']['result_map']
        if point_page['object']['total_count'] > 0:
            # Choose a random key from the dictionary
            random_key = random.choice(list(result_map.keys()))

            # Retrieve the value associated with the random key
            random_value = result_map[random_key]
            for item in random_value:
                assert item["comment"] is not None, f"point_page_info为：{point_page}"
                assert item["points"] is not None, f"point_page_info为：{point_page}"
                assert item["type_label"] is not None, f"point_page_info为：{point_page}"
                assert item["type_id"] is not None, f"point_page_info为：{point_page}"

