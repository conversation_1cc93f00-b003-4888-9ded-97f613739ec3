# -*- coding: utf-8 -*-
"""
<AUTHOR>  yue.wang
@Version        :  V1.0.0
------------------------------------
@Description    :  
@CreateTime     :  2023/12/1
@Software       :  PyCharm
------------------------------------
"""
import weeeTest
import random
import json

from test_data.ec.simple.common import Header
from test_dir.api.ec.ec_customer.signup_rest.edu_signup import EduSignup
from qa_config.ec_sql import EcDB


class TestEduSignUpSimple(weeeTest.TestCase):
    email3 = ''
    @weeeTest.mark.list('Transaction')
    def test_edu_signup(self,*args):
        """注册并验证edu用户获得35-15的优惠券"""
        #注册edu用户
        headers = Header().anony_header
        #print(headers)
        email = f'{random.randint(10 ** 9, (10 ** 10) - 1)}@osu.edu'
        signup = EduSignup().edu_signup(headers=headers,email=email,password="A1234567")
        #token = signup["object"]["token"]
        #print(token)
        user_id = signup["object"]["user_id"]
        #print(user_id)
        email1 = email
        TestEduSignUpSimple.email3 = email1
        #print(TestEduSignUpSimple.email3)

    @weeeTest.mark.list('Transaction')
    def test_getcode(self,*args):
        #登录edu用户获取登录header
        headers = Header().login_header(email=TestEduSignUpSimple.email3,
                                        password="A1234567")
        #发送验证code
        EduSignup().get_educode(headers=headers)
        #获取code
        code = json.loads(EcDB().get_edu_verify_code(TestEduSignUpSimple.email3))[0]["code"]
        #edu验证
        rec = EduSignup().edu_verify(headers=headers,key=code)
        #获取student35-15的优惠券
        rec1 = EduSignup().coupon_list(headers=headers,status="A")
        coupon_list = rec1["object"]["coupon_list"]
        #print(coupon_list)
        title = []
        for list in coupon_list:
            if list["type"] == "D":
                title.append(list["title"])
                break
        print(title)


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net', env='tb1')