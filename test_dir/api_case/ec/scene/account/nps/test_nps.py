# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_item.py
@Description    :
@CreateTime     :  2023/5/16 16:53
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 16:53
"""

import weeeTest

from test_dir.api.ec.ec_customer.nps.nps import NPS
from test_dir.api.ec.ec_so.order_query.list_my_order import ListMyOrder
from test_dir.api.ec.ec_social.review_info.review_info import ReviewInfo


class TestNPS(weeeTest.TestCase):

    @weeeTest.mark.list( 'Transaction', 'tb1')
    def test_account_nps(self, ec_login_header):
        """ Account-NPS问卷调查验证流程 """
        # ACCOUNT page 和订单成功页面的那个简单的评分
        # Account 问卷调查第一轮信息
        nps = NPS().survey_info(headers=ec_login_header, group="a")
        if nps["message_id"] == "10000" and nps["object"] is not None:
            survey = nps["object"]["survey"]
            survey_questions = nps["object"]["survey_questions"]
            task_id = survey["task_id"]
            # 回答第一个问题-评分
            NPS().survey_answer_1(headers=ec_login_header, task_id=task_id, status="P",
                                  ref_type="account_page", answer={"1": {"rating": "9"}})
            # Account 问卷调查第二轮信息
            NPS().survey_info(headers=ec_login_header, group="b")
            # 回答第二轮系列问题
            NPS().survey_answer_2(headers=ec_login_header, task_id=task_id, status="P",
                                  ref_type="account_page",
                                  answer={"2": {"option_ids": "1"},
                                          "3": {"option_ids": "7"},
                                          "4": {"option_ids": "14"},
                                          "5": {"option_ids": "17"},
                                          "6": {"option_ids": "24"},
                                          "7": {"option_ids": "32"},
                                          "8": {"option_ids": "39"},
                                          "9": {"option_ids": "50"}
                                          }
                                  )

        else:
            print("该用户不满足问卷调查条件")

    @weeeTest.mark.list('order_list', 'Transaction', 'tb1')
    def test_order_list_nps(self, ec_login_header):
        """ Order-订单评价验证流程 """
        # ACCOUNT page 和订单成功页面的那个简单的评分
        # 查看待晒单是否有数据
        to_review = ListMyOrder().list_my_order_v2(headers=ec_login_header, filter_status="6")
        if to_review["object"]["total"] > 0:
            myorders = to_review["object"]["myOrders"]
            for order_id in myorders:

                ReviewInfo().review_order(headers=ec_login_header, order_ids=order_id["id"])
                # Account 问卷调查第一轮信息
                nps = NPS().survey_info(headers=ec_login_header, group="a")
                if nps["message_id"] == "10000" and nps["object"] is not None:
                    survey = nps["object"]["survey"]
                    survey_questions = nps["object"]["survey_questions"]
                    task_id = survey["task_id"]
                    # 评价订单
                    NPS().survey_answer_1(headers=ec_login_header, task_id=task_id, status="F",
                                          ref_type="order_list",
                                          answer={"16": {"rating": 5, "option_ids": "", "comment": ""},
                                                  "17": {"rating": 5, "option_ids": "", "comment": ""},
                                                  "18": {"rating": 5, "option_ids": "", "comment": ""}
                                                  }
                                          )
                    # Account 问卷调查第二轮信息
                    NPS().survey_info(headers=ec_login_header, group="b")
                    # 回答第二轮系列问题
                    NPS().survey_answer_2(headers=ec_login_header, task_id=task_id)

                else:
                    print("该用户不满足问卷调查条件")


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net')
