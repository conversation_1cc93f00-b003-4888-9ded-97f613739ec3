# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_user.py
@Description    :
@CreateTime     :  2023/5/16 15:23
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 15:23
"""
import json

import pytest
import weeeTest
from weeeTest import log, jmespath

from test_dir.api.ec.ec_item.my_favorite.api_favorites import ApiFavorites
from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder
from test_dir.api_case.ec.common.categorycheck import CategoryCheck
from test_dir.api_case.ec.common.common_check import CommonCheck


class TestFavorites(weeeTest.TestCase):

    @weeeTest.data.file(file_name='ec_item_data.json', return_type='dict')
    @weeeTest.mark.list('test_favorites', 'Regression', 'Smoke',  'Transaction')
    def test_favorites(self, *args, ec_login_header):
        """ 我的清单-PC我的收藏相关验证流程 """
        # 获取用户的preorder
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]
        deal_date = porder["delivery_pickup_date"]
        zipcode = porder["zipcode"]
        my_list_pc = self.favorite_product_list(headers=ec_login_header)
        if my_list_pc["object"]["total_count"] > 0:
            # 切换我的搜藏分类
            for item in my_list_pc["object"]["categoryList"]:
                my_list_pc = self.favorite_product_list(headers=ec_login_header,
                                                        categoryNum=item["categoryNum"])
                assert my_list_pc["object"]["total_count"] > 0, f'我的搜藏切换异常{my_list_pc["object"]}'
            # 我的搜藏商品断言
            # 我的搜藏商品断言
            for index, product in enumerate(my_list_pc["object"]["products"]):
                CommonCheck.check_product_info(product=product, category_type="others", source="mylist", filters="others", headers=ec_login_header)

            # CommonCheck().check_products_info(category_type="others", source="mylist",
            #                                   products=my_list_pc["object"]["products"])
            product_ids = [item["id"] for item in my_list_pc["object"]["products"]]
            # 批量取消我的搜藏
            ApiFavorites().favorites_batch_batch(headers=ec_login_header, product_ids=product_ids)

        else:
            # 没有搜藏数据，去搜藏
            log.info("当前用户没有收藏商品")
            # 1. 通过分类接口获取零食商品
            normal_category = CategoryCheck().category_product(headers=ec_login_header,
                                                               zipcode=zipcode, deal_date=deal_date,
                                                               filter_sub_category="snack")

            items = jmespath(normal_category, "object.contents")
            product_list = []
            for index, item in enumerate(items):
                # 通过分类获取type不是carousel并且商品状态不是change_other_day的商品
                if item['type'] != 'carousel' and item["data"]["sold_status"] == "available":
                    product_list.append(item["data"]["id"])
                    ApiFavorites().favorites_set(headers=ec_login_header, target_id=item["data"]["id"])
                    assert self.response['result'] is True and self.response[
                        'object'] is True, f"收藏失败，resp={self.response}, item={item}"
                if index == 10:
                    break

            # 2. 我的收藏商品id集合
            favorites_list = ApiFavorites().favorites_simple(headers=ec_login_header)
            assert favorites_list["result"] == args[0]["assert"][
                "expected_result"], f"favorites_list为：{favorites_list}"
            assert favorites_list["object"]["ids"], f"收藏列表为空，列表为{favorites_list['object']['ids']}"
            # # 3. 根据商品id集合批量取消我的收藏
            # assert product_list, f"收藏列表为空， product_list={product_list}"
            # ApiFavorites().favorites_batch_batch(headers=RequestHeader.ec_login_header, product_ids=product_list)
            # assert self.response["result"] is True, f"self.response为：{self.response}, 取消收藏失败"

    @weeeTest.data.file(file_name='ec_item_data.json', return_type='dict')
    @weeeTest.mark.list('test_favorites', 'Regression', 'Smoke',  'Transaction')
    def test_favorite_v2(self, *args, ec_login_header):
        """ 我的清单-H5我的收藏相关验证流程 """
        # 获取用户的preorder
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]
        deal_date = porder["delivery_pickup_date"]
        zipcode = porder["zipcode"]
        my_list_h5 = self.favorite_product_list_v2(headers=ec_login_header)
        if my_list_h5["object"]["total_count"] > 0:
            # 切换我的搜藏分类
            for item in my_list_h5["object"]["categories"]:
                my_list_h5 = self.favorite_product_list_v2(headers=ec_login_header,
                                                           filter_sub_category=item["catalogue_num"])
                assert my_list_h5["object"]["total_count"] > 0, f'我的搜藏切换异常{my_list_h5["object"]}'
            # 我的搜藏商品断言
            for index, product in enumerate(my_list_h5["object"]["products"]):
                CommonCheck.check_product_info(headers=ec_login_header, product=product, category_type="others", source="mylist", filters="others")
            product_ids = [item["id"] for item in my_list_h5["object"]["products"]]
            # 批量取消我的搜藏
            ApiFavorites().favorites_batch_batch(headers=ec_login_header, product_ids=product_ids)

        else:
            # 没有搜藏数据，去搜藏
            log.info("当前用户没有收藏商品")
            # 1. 通过分类接口获取零食商品
            normal_category = CategoryCheck().category_product(headers=ec_login_header,
                                                               zipcode=zipcode, deal_date=deal_date,
                                                               filter_sub_category="snack")

            items = jmespath(normal_category, "object.contents")
            product_list = []
            for index, item in enumerate(items):
                # 通过分类获取type不是carousel并且商品状态不是change_other_day的商品
                if item['type'] != 'carousel' and item["data"]["sold_status"] == "available":
                    product_list.append(item["data"]["id"])
                    ApiFavorites().favorites_set(headers=ec_login_header, target_id=item["data"]["id"])
                    assert self.response['result'] is True and self.response[
                        'object'] is True, f"收藏失败，resp={self.response}, item={item}"
                if index == 10:
                    break

            # 2. 我的收藏商品id集合
            favorites_list = ApiFavorites().favorites_simple(headers=ec_login_header)
            assert favorites_list["result"] == args[0]["assert"][
                "expected_result"], f"favorites_list为：{favorites_list}"
            assert favorites_list["object"]["ids"], f"收藏列表为空，列表为{favorites_list['object']['ids']}"
            # # 3. 根据商品id集合批量取消我的收藏
            # assert product_list, f"收藏列表为空， product_list={product_list}"
            # ApiFavorites().favorites_batch_batch(headers=RequestHeader.ec_login_header, product_ids=product_list)
            # assert self.response["result"] is True, f"self.response为：{self.response}, 取消收藏失败"
    @weeeTest.data.file(file_name='ec_item_data.json', return_type='dict')

    @weeeTest.data.file(file_name='ec_item_data.json', return_type='dict')
    @weeeTest.mark.list('B2B', 'Regression', 'Smoke', 'Transaction')
    def test_b2b_favorites(self, *args, ec_login_header):
        """ 我的清单-PC我的收藏相关验证流程 """
        # 获取用户的preorder
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]
        deal_date = porder["delivery_pickup_date"]
        zipcode = porder["zipcode"]
        my_list_pc = self.favorite_product_list(headers=ec_login_header)
        if my_list_pc["object"]["total_count"] > 0:
            # 切换我的搜藏分类
            for item in my_list_pc["object"]["categoryList"]:
                my_list_pc = self.favorite_product_list(headers=ec_login_header,
                                                        categoryNum=item["categoryNum"])
                assert my_list_pc["object"]["total_count"] > 0, f'我的搜藏切换异常{my_list_pc["object"]}'
            # 我的搜藏商品断言
            # 我的搜藏商品断言
            for index, product in enumerate(my_list_pc["object"]["products"]):
                CommonCheck.check_product_info(product=product, category_type="others", source="mylist",
                                               filters="others", headers=ec_login_header)

            # CommonCheck().check_products_info(category_type="others", source="mylist",
            #                                   products=my_list_pc["object"]["products"])
            product_ids = [item["id"] for item in my_list_pc["object"]["products"]]
            # 批量取消我的搜藏
            ApiFavorites().favorites_batch_batch(headers=ec_login_header, product_ids=product_ids)

        else:
            # 没有搜藏数据，去搜藏
            log.info("当前用户没有收藏商品")
            # 1. 通过分类接口获取零食商品
            normal_category = CategoryCheck().category_product(headers=ec_login_header,
                                                               zipcode=zipcode, deal_date=deal_date,
                                                               filter_sub_category="snack")

            items = jmespath(normal_category, "object.contents")
            product_list = []
            for index, item in enumerate(items):
                # 通过分类获取type不是carousel并且商品状态不是change_other_day的商品
                if item['type'] != 'carousel' and item["data"]["sold_status"] == "available":
                    product_list.append(item["data"]["id"])
                    ApiFavorites().favorites_set(headers=ec_login_header, target_id=item["data"]["id"])
                    assert self.response['result'] is True and self.response[
                        'object'] is True, f"收藏失败，resp={self.response}, item={item}"
                if index == 10:
                    break

            # 2. 我的收藏商品id集合
            favorites_list = ApiFavorites().favorites_simple(headers=ec_login_header)
            assert favorites_list["result"] == args[0]["assert"][
                "expected_result"], f"favorites_list为：{favorites_list}"
            assert favorites_list["object"]["ids"], f"收藏列表为空，列表为{favorites_list['object']['ids']}"
            # # 3. 根据商品id集合批量取消我的收藏
            # assert product_list, f"收藏列表为空， product_list={product_list}"
            # ApiFavorites().favorites_batch_batch(headers=RequestHeader.ec_login_header, product_ids=product_list)
            # assert self.response["result"] is True, f"self.response为：{self.response}, 取消收藏失败"

    @weeeTest.mark.list('B2B', 'Regression', 'Smoke',  'Transaction')
    def test_b2b_favorite_v2(self, *args, ec_login_header):
        """ 我的清单-H5我的收藏相关验证流程 """
        # 获取用户的preorder
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]
        deal_date = porder["delivery_pickup_date"]
        zipcode = porder["zipcode"]
        my_list_h5 = self.favorite_product_list_v2(headers=ec_login_header)
        if my_list_h5["object"]["total_count"] > 0:
            # 切换我的搜藏分类
            for item in my_list_h5["object"]["categories"]:
                my_list_h5 = self.favorite_product_list_v2(headers=ec_login_header,
                                                           filter_sub_category=item["catalogue_num"])
                assert my_list_h5["object"]["total_count"] > 0, f'我的搜藏切换异常{my_list_h5["object"]}'
            # 我的搜藏商品断言
            for index, product in enumerate(my_list_h5["object"]["products"]):
                CommonCheck.check_product_info(headers=ec_login_header, product=product, category_type="others", source="mylist", filters="others")
            product_ids = [item["id"] for item in my_list_h5["object"]["products"]]
            # 批量取消我的搜藏
            ApiFavorites().favorites_batch_batch(headers=ec_login_header, product_ids=product_ids)

        else:
            # 没有搜藏数据，去搜藏
            log.info("当前用户没有收藏商品")
            # 1. 通过分类接口获取零食商品
            normal_category = CategoryCheck().category_product(headers=ec_login_header,
                                                               zipcode=zipcode, deal_date=deal_date,
                                                               filter_sub_category="snack")

            items = jmespath(normal_category, "object.contents")
            product_list = []
            for index, item in enumerate(items):
                # 通过分类获取type不是carousel并且商品状态不是change_other_day的商品
                if item['type'] != 'carousel' and item["data"]["sold_status"] == "available":
                    product_list.append(item["data"]["id"])
                    ApiFavorites().favorites_set(headers=ec_login_header, target_id=item["data"]["id"])
                    assert self.response['result'] is True and self.response[
                        'object'] is True, f"收藏失败，resp={self.response}, item={item}"
                if index == 10:
                    break

            # 2. 我的收藏商品id集合
            favorites_list = ApiFavorites().favorites_simple(headers=ec_login_header)
            assert favorites_list["result"] == args[0]["assert"][
                "expected_result"], f"favorites_list为：{favorites_list}"
            assert favorites_list["object"]["ids"], f"收藏列表为空，列表为{favorites_list['object']['ids']}"
            # # 3. 根据商品id集合批量取消我的收藏
            # assert product_list, f"收藏列表为空， product_list={product_list}"
            # ApiFavorites().favorites_batch_batch(headers=RequestHeader.ec_login_header, product_ids=product_list)
            # assert self.response["result"] is True, f"self.response为：{self.response}, 取消收藏失败"

    def favorite_product_list(self, headers, categoryNum: str = None):
        """ pc 我的搜藏商品"""
        my_list_pc = ApiFavorites().my_favorite_product_list(headers=headers, categoryNum=categoryNum)
        assert my_list_pc["object"]["title"] is not None, f'我的搜藏返回数据异常：{my_list_pc}'

        return my_list_pc

    def favorite_product_list_v2(self, headers, filter_sub_category: str = None):
        """ H5 我的搜藏商品"""
        my_list_h5 = ApiFavorites().my_favorite_product_list_v2(headers=headers,
                                                                filter_sub_category=filter_sub_category)
        assert my_list_h5["object"]["search_catalogue_num"] == "favorite", f'我的搜藏返回数据异常：{my_list_h5}'
        assert my_list_h5["object"]["search_catalogue_name"] is not None, f'我的搜藏返回数据异常：{my_list_h5}'
        return my_list_h5
