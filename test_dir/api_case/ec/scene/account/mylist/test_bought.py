# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_user.py
@Description    :
@CreateTime     :  2023/5/16 15:23
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 15:23
"""

import requests
import weeeTest

from test_dir.api.ec.ec_item.recommend.api_bought import GetBoughtProducts
from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder
from test_dir.api.ec.ec_so.preorder.update_preorderline import UpdatePreOrderLine


class TestBought(weeeTest.TestCase):
    @weeeTest.mark.list('Regression', 'Smoke',  'Transaction')
    def test_bought_on_search_page(self, ec_login_header):
        """ 我的清单-曾经购买相关验证流程 """
        # 1.获取登录header
        # 清除生鲜购物车
        # RemoveAllProductsInCart().remove_all_products_in_grocery_cart(headers=headers)
        # 曾经购买simple--搜索页面、订单页面
        GetBoughtProducts().get_bought_products(headers=ec_login_header)
        assert self.response["result"] is True, f"self.response为：{self.response}"
        if self.response["object"] is not None:
            mylist = self.response["object"]
            url = mylist["url"]
            assert url.endswith("/account/buy_again"), f"url为{url}"  # "URL不是以/account/buy_again结尾"
            # 点击调整曾经购买页面
            response = requests.get(url)
            if response.status_code == 200:
                print(f"{url} is accessible")
            else:
                print(f"{url} is not accessible")
        else:
            print("曾经购买列表为空")

    @weeeTest.mark.list('Regression', 'Smoke',  'Transaction')
    def test_bought_on_my_list_page(self, ec_login_header):
        # 曾经购买--mylist页面(我的清单)
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]
        GetBoughtProducts().get_bought_products_list(headers=ec_login_header)
        assert self.response["result"] is True, f"self.response为：{self.response}"
        if self.response["object"]["total_count"] > 0:
            mylist = self.response["object"]
            categories = mylist["categories"]
            products = mylist["products"]
            for categorie in categories:
                # 切换分类
                GetBoughtProducts().get_bought_products_list(headers=ec_login_header,
                                                             filter_sub_category=categorie["catalogue_num"])
                break
            for product in products:
                # 加购商品
                UpdatePreOrderLine().porder_items_v3(headers=ec_login_header, product_id=product["id"],
                                                     is_pantry=product["is_pantry"], is_mkpl=product["is_mkpl"],
                                                     date=porder["delivery_pickup_date"], refer_type=product["biz_type"],
                                                     refer_value=product["vender_id"], vender_id=product["vender_id"],
                                                     source="my_list_watch")
                break

        else:
            print("曾经购买列表为空")

    @weeeTest.mark.list('Regression', 'Smoke',  'Transaction')
    def test_bought_cart_page(self, ec_login_header):
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]
        # 曾经购买--购物车页面
        GetBoughtProducts().get_bought_products_cart(headers=ec_login_header)
        assert self.response["result"] is True, f"self.response为：{self.response}"
        if self.response["object"]["total_count"] > 0:
            mylist = self.response["object"]
            categories = mylist["categories"]
            products = mylist["products"]
            for product in products:
                # 加购商品
                UpdatePreOrderLine().porder_items_v3(headers=ec_login_header, product_id=product["id"],
                                                     is_pantry=product["is_pantry"], is_mkpl=product["is_mkpl"],
                                                     date=porder["delivery_pickup_date"], refer_type=product["biz_type"],
                                                     refer_value=product["vender_id"], vender_id=product["vender_id"],
                                                     source="my_list_watch")
                break

        else:
            print("曾经购买列表为空")

        # 曾经购买--community定制
        GetBoughtProducts().get_bought_products_social(headers=ec_login_header)
        assert self.response["result"] is True, f"self.response为：{self.response}"
        assert isinstance(self.response['object']['products'], list)

    @weeeTest.mark.list('B2B','Regression', 'Smoke',  'Transaction')
    def test_b2b_bought_on_my_list_page(self, ec_login_header):
        # 曾经购买--mylist页面(我的清单)
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]
        GetBoughtProducts().get_bought_products_list(headers=ec_login_header)
        assert self.response["result"] is True, f"self.response为：{self.response}"
        if self.response["object"]["total_count"] > 0:
            mylist = self.response["object"]
            categories = mylist["categories"]
            products = mylist["products"]
            for categorie in categories:
                # 切换分类
                GetBoughtProducts().get_bought_products_list(headers=ec_login_header,
                                                             filter_sub_category=categorie["catalogue_num"])
                break
            for product in products:
                # 加购商品
                UpdatePreOrderLine().porder_items_v3(headers=ec_login_header, product_id=product["id"],
                                                     is_pantry=product["is_pantry"], is_mkpl=product["is_mkpl"],
                                                     date=porder["delivery_pickup_date"], refer_type=product["biz_type"],
                                                     refer_value=product["vender_id"], vender_id=product["vender_id"],
                                                     source="my_list_watch")
                break

        else:
            print("曾经购买列表为空")

if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net', env='tb1')
