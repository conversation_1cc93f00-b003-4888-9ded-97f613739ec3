# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_user.py
@Description    :
@CreateTime     :  2023/5/16 15:23
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 15:23
"""
from typing import Any

import pytest
import weeeTest

from test_dir.api.ec.ec_growth.share import Share
from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder
from test_dir.api_case.ec.common.common_check import CommonCheck
from test_dir.api_case.ec.common.set_user_porder import SetUserPorder


class TestShare(weeeTest.TestCase):
    @pytest.fixture(scope='class', autouse=True)
    def setup(self, ec_login_header):
        """
        前置条件 更新账号语言为en

        """
        SetUserPorder().set_user_porder(headers=ec_login_header)

    @weeeTest.mark.list('teat_share', 'Regression', 'Transaction')
    def test_fbw_freshdeli_landing_share(self, ec_login_header):
        """ FBW-餐馆卤味landing页面分享验证流程 """
        share = Share().fresh_deli_share(headers=ec_login_header)
        assert share["result"] is True
        CommonCheck().check_share_content(content=share["object"], source="freshdeli")

    @weeeTest.mark.list('teat_share', 'Regression', 'Transaction')
    def test_fbw_bakery_landing_share(self, ec_login_header):
        """ FBW-每日现做landing页面分享验证流程 """
        share = Share().bakery_share(headers=ec_login_header)
        assert share["result"] is True
        CommonCheck().check_share_content(content=share["object"], source="bakery")

    @weeeTest.mark.list('teat_share', 'Regression', 'Transaction')
    def test_topx_chart_share(self, ec_login_header):
        """ TOPX 排行榜页面分享验证流程 """
        share = Share().topx_ranking_list(headers=ec_login_header)
        assert share["result"] is True
        CommonCheck().check_share_content(content=share["object"], source="topx")

    @weeeTest.mark.list('teat_share', 'Regression', 'Transaction')
    def test_topx_detail_share(self, ec_login_header):
        """ TOPX 排行榜详情页面分享验证流程 """
        share = Share().topx_ranking_detail(headers=ec_login_header, key="b8pjp0annz")
        assert share["result"] is True
        CommonCheck().check_share_content(content=share["object"], source="topx_detail")

    @weeeTest.mark.list('test_pdp_detail_share', 'Regression', 'Transaction')
    def test_pdp_detail_share(self, ec_login_header):
        """ PDP页面分享验证流程 """
        product_id = 76470
        share = Share().share_product(headers=ec_login_header, product_id=product_id)
        assert share["result"] is True
        CommonCheck().check_share_content(content=share["object"], source="pdp",
                                          args={"product_id": product_id})

        # pdp 分享图片
        share_image = Share().product_share_image(headers=ec_login_header, product_id=product_id)
        self.pdp_share_image_assertion(share=share_image["object"])

    @weeeTest.mark.list('teat_share', 'Regression', 'Transaction')
    def test_collection_brand_share(self, ec_login_header):
        """ 品牌合集页面分享验证流程 """
        brand_key = "1YuDBF0y"
        share = Share().collection_brand_share(headers=ec_login_header, brand_key=brand_key)
        assert share["result"] is True
        CommonCheck().check_share_content(content=share["object"], source="collection_brand")

    @weeeTest.mark.list('teat_share', 'Regression', 'Transaction')
    def test_brand_share(self, ec_login_header):
        """ 品牌页面分享验证流程 """
        brand_key = "1YuDBF0y"
        brand_slug = "Sansui"
        share = Share().brand_share(headers=ec_login_header, brand_key=brand_key
                                    )
        assert share["result"] is True
        CommonCheck().check_share_content(content=share["object"], source="brand",
                                          args={"brand_key": brand_key, "brand_slug": brand_slug})

    @weeeTest.mark.list('teat_share', 'Regression', 'Transaction')
    def test_collection_theme_share(self, ec_login_header):
        """ 主题合集页面分享验证流程 """
        tag_id = 1
        share = Share().collection_theme_share(headers=ec_login_header, tag_id=tag_id)
        assert share["result"] is True
        CommonCheck().check_share_content(content=share["object"], source="collection_theme")

    @weeeTest.mark.list('teat_share', 'Regression', 'Transaction')
    def test_waterfall_share(self, ec_login_header):
        """ 全球购waterfall页面分享验证流程 """
        share = Share().cms_page_share(headers=ec_login_header)
        assert share["result"] is True
        CommonCheck().check_share_content(content=share["object"], source="waterfall")

    @weeeTest.mark.list('teat_share', 'Regression', 'Transaction')
    def test_global_plus_share(self, ec_login_header):
        """ 全球购首单优惠券页面分享验证流程 """
        share = Share().global_plus_share(headers=ec_login_header)
        assert share["result"] is True
        CommonCheck().check_share_content(content=share["object"], source="global_plus")

    @weeeTest.mark.list('teat_share', 'Regression', 'Transaction')
    def test_level_coupon_share(self, ec_login_header):
        """ MKPL优惠券分享验证流程 """
        share = Share().level_coupon_share(headers=ec_login_header)
        assert share["result"] is True
        CommonCheck().check_share_content(content=share["object"], source="level_coupon")

    # @weeeTest.data.file(file_name='ec_item_data.json', return_type='dict')
    # @weeeTest.mark.list('teat_share', 'Regression', 'Transaction')
    # def test_raffle_share(self, *args):
    #     """ MKPL转盘抽奖分享验证流程 """
    #     share = Share().raffle_share(headers=RequestHeader.ec_login_header, raffle_id=1)
    #     assert share["result"] is True
    #     CommonCheck().check_share_content(content=share["object"], source="raffle")

    @weeeTest.mark.list('teat_share', 'Regression', 'Transaction')
    def test_vendor_share(self, ec_login_header):
        """ MKPL商家页面分享验证流程 """
        share = Share().vendor_share(headers=ec_login_header)
        assert share["result"] is True
        CommonCheck().check_share_content(content=share["object"], source="vendor")

    @weeeTest.mark.list('teat_share', 'Regression', 'Transaction')
    def test_group_by_share(self, ec_login_header):
        """ MKPL 拼团分享验证流程 """
        # key = "gb_599749746898501"
        share = Share().group_by_share(headers=ec_login_header)
        assert share["result"] is True
        CommonCheck().check_share_content(content=share["object"], source="group_by")

    @weeeTest.mark.list('teat_share', 'Regression', 'Transaction')
    def test_group_by_bill_share(self, ec_login_header):
        """ MKPL 拼团账单分享验证流程 """
        share = Share().group_by_bill_share(headers=ec_login_header)
        assert share["result"] is True
        CommonCheck().check_share_content(content=share["object"], source="group_by_bill")

    @weeeTest.mark.list('teat_share', 'Regression', 'Transaction')
    def test_lighting_deals_share(self, ec_login_header):
        """ 生鲜秒杀分享验证流程 """
        # 获取用户的porder
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]
        share = Share().lighting_share(headers=ec_login_header, deal_id=porder["deal_id"])
        assert share["result"] is True
        CommonCheck().check_share_content(content=share["object"], source="lighting_deals")

    @weeeTest.mark.list('teat_share', 'Regression', 'Transaction')
    def test_mkpl_lighting_share(self, ec_login_header):
        """ MKPL秒杀分享验证流程 """
        # 获取用户的porder
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]
        share = Share().mkpl_lighting_share(headers=ec_login_header, deal_id=porder["deal_id"])
        assert share["result"] is True
        CommonCheck().check_share_content(content=share["object"], source="mkpl_lighting_deals")

    @weeeTest.mark.list('teat_share', 'Regression', 'Transaction')
    def test_topic_share(self, ec_login_header):
        """ 话题分享验证流程 """
        # 获取用户的porder
        share = Share().topic_share(headers=ec_login_header)
        assert share["result"] is True
        CommonCheck().check_share_content(content=share["object"], source="topic")

    @weeeTest.mark.list('teat_share', 'Regression', 'Transaction')
    def test_hero_landing_share(self, ec_login_header):
        """ 英雄商品验证流程 """
        # 获取用户的porder
        share = Share().hero_landing_share(headers=ec_login_header)
        assert share["result"] is True
        CommonCheck().check_share_content(content=share["object"], source="hero")

    @weeeTest.mark.list('teat_share', 'Regression', 'Transaction')
    def test_affiliate_landing_share(self, ec_login_header):
        """ 返利联盟LANDING验证流程 """
        share = Share().affiliate_landing_share(headers=ec_login_header)
        assert share["result"] is True
        CommonCheck().check_share_content(content=share["object"], source="affiliate")

    def pdp_share_image_assertion(self, share: dict | Any):
        assert share["title"] is not None, f'pdp 分享图片信息返回异常，请确认{share}'
        assert share["price"] is not None, f'pdp 分享图片信息返回异常，请确认{share}'
        assert share["qrcode_url"] is not None, f'pdp 分享图片信息返回异常，请确认{share}'
        assert share["product_img_url"] is not None, f'pdp 分享图片信息返回异常，请确认{share}'
        assert share["share_product_image_invite_tip"] is not None, f'pdp 分享图片信息返回异常，请确认{share}'
        assert share["user_img_url"] is not None, f'pdp 分享图片信息返回异常，请确认{share}'
        assert share["user_alias"] is not None, f'pdp 分享图片信息返回异常，请确认{share}'
        assert share["user_img_url"] is not None, f'pdp 分享图片信息返回异常，请确认{share}'
        if share.get("base_price") and share["base_price"] > 0:
            assert share["discount_percentage"] is not None
