"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :  test_add_to_cart.py
@Description    :  
@CreateTime     :  2023/8/10 13:34
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/8/10 13:34
"""
import json
import os

import pytest
import weeeTest
from weeeTest import jmespath

from test_dir.api.ec.ec_customer.account_rest.account_rest import AccountRest
from test_dir.api.ec.ec_item.search_v3.search_by_catalogue_content import SearchByCatalogueContent
from test_dir.api.ec.ec_so.address.apply_user_address_information import ApplyUserAddressInformation
from test_dir.api.ec.ec_so.address.query_user_address_list import QueryUserAddressList
from test_dir.api.ec.ec_so.order.prepare_checkout import PrepareCheckout
from test_dir.api.ec.ec_so.preorder.payment_category import PaymentCategory
from test_dir.api.ec.ec_so.preorder.query_preorder import QueryPreOrder
from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder
from test_dir.api.ec.ec_so.preorder.update_preorderline import UpdatePreOrderLine
from test_dir.api_case.ec.common.comm_check_products_with_cart import CommCheckProductsWithCart
from test_dir.api_case.ec.common.common_check import CommonCheck
from test_dir.api_case.ec.common.remove_all_products_in_cart import RemoveAllProductsInCart
from test_dir.api_case.ec.common.set_user_porder import SetUserPorder


class TestAddToCartMoreThan35(weeeTest.TestCase):
    """
    购物车-不同等级用户购物车加购商品验证运费
    【109711】 所有端结算页-默认小费逻辑
    """
    @weeeTest.mark.list('Regression', 'Transaction', 'product', 'Smoke')
    @weeeTest.mark.skipif(os.getenv("is_Thursday") == "True", reason="周四不执行")
    def test_add_to_cart_between_35_49(self, ec_zhuli_header):
        """生鲜购物车-加购大于35小于49的商品到购物车获取运费信息
           112155 【109711】 所有端结算页-默认小费逻辑
        """
        # 获取用户的porder
        SetUserPorder().set_user_porder(headers=ec_zhuli_header)
        # 获取用户的preorder
        QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_zhuli_header)
        assert self.response["result"] is True
        assert CommonCheck.list_check(['token', 'deal_id', 'items'], self.response['object'].keys())
        porder = self.response["object"]
        deal_date = porder["delivery_pickup_date"]
        zipcode = porder["zipcode"]

        # 清空购物车商品
        RemoveAllProductsInCart().remove_all_products_in_grocery_cart(headers=ec_zhuli_header)

        # 1. 通过分类接口获取零食商品
        normal = SearchByCatalogueContent().search_by_catalogue_content(
            headers=ec_zhuli_header,
            zipcode=zipcode,
            date=deal_date,
            sort="recommend",
            filter_sub_category="snack",
            filters=json.dumps({"delivery": True})
        )
        items = jmespath(normal, "object.contents")
        assert items, f"当前环境下没有category=snack的商品，接口返回为：{normal}，zipcode={zipcode}"
        product_list = []

        for item in items:
            # 通过分类获取type不是carousel并且商品状态不是change_other_day的商品
            if item['type'] != 'carousel' and item["data"]["sold_status"] == "available" and item['data']['is_pantry'] is False:
                product_list.append([item["data"]["id"], item['data']['min_order_quantity'], item['data']['max_order_quantity']])

        assert product_list, f'filter_sub_category=snack的商品不存在，product list为空，{product_list}'

        # 2. 加购商品，使其金额between（35， 49）
        fee = 0.0
        for product in product_list:
            UpdatePreOrderLine().porder_items_v3(
                headers=ec_zhuli_header,
                product_id=product[0],
                quantity=product[1]
            )

            assert self.response['result'] is True and self.response['object'][
                'updateItems'] != [], f"加购失败，self.response is {self.response}"

            CommCheckProductsWithCart().check_product_exists_in_cart(
                headers=ec_zhuli_header,
                cart_domain='grocery',
                product_id=product[0])

            cart_content = QueryPreOrder().query_preorder_v5(headers=ec_zhuli_header)
            # 计算商品金额，不包含运费，必须大于35 and < 49
            fee = [float(section['fee_info']['sub_total_price']) for section in
                   cart_content['object']["sections"]]
            if 35 <= sum(fee) < 49:
                break
        try:
            assert 35 <= sum(fee) < 49, f"第一次加购的商品金额不满足35<sum(fee)< 49，不满足计算运费条件，购物车总金额为{sum(fee)}"
        except Exception as e:
            # 如果商品金额不满足上述关系，再加购1次
            for product in reversed(product_list):
                UpdatePreOrderLine().porder_items_v3(
                    headers=ec_zhuli_header,
                    product_id=product[0],
                    quantity=product[1] * 2
                )

                assert self.response['result'] is True and self.response['object'][
                    'updateItems'] != [], f"加购失败，self.response is {self.response}"

                CommCheckProductsWithCart().check_product_exists_in_cart(
                    headers=ec_zhuli_header,
                    cart_domain='grocery',
                    product_id=product[0])

                cart_content = QueryPreOrder().query_preorder_v5(headers=ec_zhuli_header)
                # # 计算商品金额，不包含运费，必须大于35 and < 49
                fee = [float(section['fee_info']['sub_total_price']) for section in
                       cart_content['object']["sections"]]
                if 35 <= sum(fee) < 49:
                    break
            # assert 35 <= sum(fee) < 49, f"第二次加购的商品金额不满足35<sum(fee)< 49，不满足计算运费条件，购物车总金额为{sum(fee)}"
            if not 35 <= sum(fee) < 49:
                pytest.skip(reason=f'加购的金额不在[35, 49]之间，不符合要求，此用例跳过. sum(fee)={sum(fee)}')

        # 加购满35不满49验证运费--金牌用户免运费，银牌用户5.95， 铜牌用户6.95
        # 查询购物车v5
        cart = QueryPreOrder().query_preorder_v5(headers=ec_zhuli_header)
        final_amount = cart["object"]["final_amount"]
        CommCheckProductsWithCart().check_product_exists_in_cart(
            headers=ec_zhuli_header,
            cart_domain="grocery",
            product_id=product_list[0][0],
            final_amount=final_amount
        )
        # 3. 判断运费一定存在于[4.95, 5.95, 6.95]
        # 判断用户等级，不用等级运费不同
        AccountRest().get_account_rewards_level(headers=ec_zhuli_header)
        account_level = self.response['object']['current_level_label']
        assert account_level in ['Gold', 'Silver', 'Bronze'], f"用户等级不在'Gold', 'Silver', 'Bronze'中，用户等级为：{account_level}"
        # 获取当前购物车运费
        real_shipping_fee = [float(section['fee_info']['shipping_fee']) for section in cart['object']['sections']]
        # 获取当前购物车商品金额，不包含运费，只有一个生鲜购物车
        current_product_amount = CommonCheck.compute_total_price_in_cart_not_include_freight(cart['object']['sections'])
        expect_fee = CommonCheck.check_shipping_fee(account_level, current_product_amount)
        # 根据上面的加购商品，一定是生鲜商品，所以不存在商品在多个购物车的情况，所以运费就是一个生鲜购物车的运费
        # 如果存在多个购物车，这样计算【期望运费】是错误的
        assert expect_fee == sum(real_shipping_fee), f"用户加购大于35小于49但运费不正确, 期望运费为{expect_fee}, 实际运费为：{real_shipping_fee}，用户等级为{account_level}, 当前商品金额为：{current_product_amount}"

        pre = self._get_pre(ec_zhuli_header)
        # 校验小费，1档小费 3$
        self._pre_tip_assertion(pre, 1)

        # 4. 校验服务费
        self._check_service_fee(account_level, current_product_amount, pre)

    @weeeTest.mark.list('Regression', 'Transaction', 'product', 'Smoke')
    def test_add_to_cart_between_49_79(self, ec_zhuli_header):
        """生鲜购物车-加购大于49小于79的商品到购物车获取运费信息
           112155
        """
        # 获取用户的porder
        SetUserPorder().set_user_porder(headers=ec_zhuli_header)
        # 获取用户的preorder
        QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_zhuli_header)
        assert self.response["result"] is True
        assert CommonCheck.list_check(['token', 'deal_id', 'items'], self.response['object'].keys())
        porder = self.response["object"]
        deal_date = porder["delivery_pickup_date"]
        zipcode = porder["zipcode"]

        # 清空购物车商品
        RemoveAllProductsInCart().remove_all_products_in_grocery_cart(headers=ec_zhuli_header)

        # 1. 通过分类接口获取零食商品
        normal = SearchByCatalogueContent().search_by_catalogue_content(
            headers=ec_zhuli_header,
            zipcode=zipcode,
            date=deal_date,
            sort="recommend",
            filter_sub_category="snack",
            filters=json.dumps({"delivery": True})
        )
        items = jmespath(normal, "object.contents")
        assert items, f"当前环境下没有category=snack的商品，接口返回为：{normal}，zipcode={zipcode}"
        product_list = []

        for item in items:
            # 通过分类获取type不是carousel并且商品状态不是change_other_day的商品
            if item['type'] != 'carousel' and item["data"]["sold_status"] == "available" and item['data']['is_pantry'] is False:
                product_list.append([item["data"]["id"], item['data']['min_order_quantity'], item['data']['max_order_quantity']])

        assert product_list, f'filter_sub_category=snack的商品不存在，product list为空，{product_list}'

        # 2. 加购商品，使其金额between（49， 79）
        fee = 0.0
        for product in product_list:
            UpdatePreOrderLine().porder_items_v3(
                headers=ec_zhuli_header,
                product_id=product[0],
                quantity=product[1]
            )

            assert self.response['result'] is True and self.response['object'][
                'updateItems'] != [], f"加购失败，self.response is {self.response}"

            CommCheckProductsWithCart().check_product_exists_in_cart(
                headers=ec_zhuli_header,
                cart_domain='grocery',
                product_id=product[0])

            cart_content = QueryPreOrder().query_preorder_v5(headers=ec_zhuli_header)
            # 计算商品金额，不包含运费，必须大于49 and < 79
            fee = [float(section['fee_info']['sub_total_price']) for section in
                   cart_content['object']["sections"]]
            if 49 <= sum(fee) < 79:
                break
        try:
            assert 49 <= sum(
                fee) < 79, f"第一次加购的商品金额不满足49<sum(fee)<79，不满足计算运费条件，购物车总金额为{sum(fee)}"
        except Exception as e:
            # 如果商品金额不满足上述关系，再加购1次
            for product in reversed(product_list):
                UpdatePreOrderLine().porder_items_v3(
                    headers=ec_zhuli_header,
                    product_id=product[0],
                    quantity=product[1] * 2
                )

                assert self.response['result'] is True and self.response['object'][
                    'updateItems'] != [], f"加购失败，self.response is {self.response}"

                CommCheckProductsWithCart().check_product_exists_in_cart(
                    headers=ec_zhuli_header,
                    cart_domain='grocery',
                    product_id=product[0])

                cart_content = QueryPreOrder().query_preorder_v5(headers=ec_zhuli_header)
                # # 计算商品金额，不包含运费，必须大于49 and < 79
                fee = [float(section['fee_info']['sub_total_price']) for section in
                       cart_content['object']["sections"]]
                if 49 <= sum(fee) < 79:
                    break
            # assert 49 < sum(
            #     fee) <= 79, f"第二次加购的商品金额不满足49<sum(fee)<79，不满足计算运费条件，购物车总金额为{sum(fee)}"
            if not 49 < sum(fee) <= 79:
                pytest.skip(reason=f'加购的金额不在[49, 79]之间，不符合要求，此用例跳过. sum(fee)={sum(fee)}')

        # 加购满35不满49验证运费--金牌用户免运费，银牌用户5.95， 铜牌用户6.95
        # 查询购物车v5
        cart = QueryPreOrder().query_preorder_v5(headers=ec_zhuli_header)
        final_amount = cart["object"]["final_amount"]
        CommCheckProductsWithCart().check_product_exists_in_cart(
            headers=ec_zhuli_header,
            cart_domain="grocery",
            product_id=product_list[0][0],
            final_amount=final_amount
        )
        # 3. 判断运费一定存在于[4.95, 5.95, 6.95]
        # 判断用户等级，不用等级运费不同
        AccountRest().get_account_rewards_level(headers=ec_zhuli_header)
        account_level = self.response['object']['current_level_label']
        assert account_level in ['Gold', 'Silver',
                                 'Bronze'], f"用户等级不在'Gold', 'Silver', 'Bronze'中，用户等级为：{account_level}"
        # 获取当前购物车运费
        real_shipping_fee = [float(section['fee_info']['shipping_fee']) for section in cart['object']['sections']]
        # 获取当前购物车商品金额，不包含运费，只有一个生鲜购物车
        current_product_amount = CommonCheck.compute_total_price_in_cart_not_include_freight(cart['object']['sections'])
        expect_fee = CommonCheck.check_shipping_fee(account_level, current_product_amount)
        # 根据上面的加购商品，一定是生鲜商品，所以不存在商品在多个购物车的情况，所以运费就是一个生鲜购物车的运费
        # 如果存在多个购物车，这样计算【期望运费】是错误的
        assert expect_fee == sum(
            real_shipping_fee), f"用户加购大于49小于79但运费不正确, 期望运费为{expect_fee}, 实际运费为：{real_shipping_fee}，用户等级为{account_level},当前购物车金额为：{current_product_amount}"

        # 获取pre
        pre = self._get_pre(ec_zhuli_header)
        # 4. 校验服务费
        self._check_service_fee(account_level, current_product_amount, pre)

    @weeeTest.mark.list('Regression', 'Transaction', 'product', 'Smoke')
    def test_add_to_cart_gt_79(self, ec_zhuli_header):
        """生鲜购物车-加购大于79的商品到购物车获取运费信息
           112155
        """
        # 获取用户的porder
        SetUserPorder().set_user_porder(headers=ec_zhuli_header)
        # 获取用户的preorder
        QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_zhuli_header)
        assert self.response["result"] is True
        assert CommonCheck.list_check(['token', 'deal_id', 'items'], self.response['object'].keys())
        porder = self.response["object"]
        deal_date = porder["delivery_pickup_date"]
        zipcode = porder["zipcode"]

        # 清空购物车商品
        RemoveAllProductsInCart().remove_all_products_in_grocery_cart(headers=ec_zhuli_header)

        # 1. 通过分类接口获取零食商品
        normal = SearchByCatalogueContent().search_by_catalogue_content(
            headers=ec_zhuli_header,
            zipcode=zipcode,
            date=deal_date,
            sort="recommend",
            filter_sub_category="snack",
            filters=json.dumps({"delivery": True})
        )
        items = jmespath(normal, "object.contents")
        assert items, f"当前环境下没有category=snack的商品，接口返回为：{normal}，zipcode={zipcode}"
        product_list = []

        for item in items:
            # 通过分类获取type不是carousel并且商品状态不是change_other_day的商品
            if item['type'] != 'carousel' and item["data"]["sold_status"] == "available" and item['data']['is_pantry'] is False:
                product_list.append([item["data"]["id"], item['data']['max_order_quantity']])

        assert product_list, f'filter_sub_category=snack的商品不存在，product list为空，{product_list}'

        # 2. 加购商品，使其金额>79
        fee = 0.0
        for product in product_list:
            UpdatePreOrderLine().porder_items_v3(
                headers=ec_zhuli_header,
                product_id=product[0],
                quantity=product[1]
            )

            assert self.response['result'] is True and self.response['object'][
                'updateItems'] != [], f"加购失败，self.response is {self.response}"

            CommCheckProductsWithCart().check_product_exists_in_cart(
                headers=ec_zhuli_header,
                cart_domain='grocery',
                product_id=product[0])

            cart_content = QueryPreOrder().query_preorder_v5(headers=ec_zhuli_header)
            # 计算商品金额，不包含运费，必须大于79
            fee = [float(section['fee_info']['sub_total_price']) for section in
                   cart_content['object']["sections"]]
            if sum(fee) > 79:
                break
        try:
            assert sum(fee) > 79, f"加购的商品金额不满足sum(fee)>79，不满足计算运费条件，购物车总金额为{sum(fee)}"
        except Exception as e:
            # 如果商品金额不满足上述关系，再加购1次
            for product in reversed(product_list):
                UpdatePreOrderLine().porder_items_v3(
                    headers=ec_zhuli_header,
                    product_id=product[0],
                    quantity=product[1]
                )

                assert self.response['result'] is True and self.response['object'][
                    'updateItems'] != [], f"加购失败，self.response is {self.response}"

                CommCheckProductsWithCart().check_product_exists_in_cart(
                    headers=ec_zhuli_header,
                    cart_domain='grocery',
                    product_id=product[0])

                cart_content = QueryPreOrder().query_preorder_v5(headers=ec_zhuli_header)
                # # 计算商品金额，不包含运费，必须大于79
                fee = [float(section['fee_info']['sub_total_price']) for section in
                       cart_content['object']["sections"]]
                if sum(fee) > 79:
                    break
            # assert sum(fee) > 79, f"加购的商品金额不满足sum(fee)>79，不满足计算运费条件，购物车总金额为{sum(fee)}"
            if not sum(fee) > 79:
                pytest.skip(reason=f'加购的金额不大于79，不符合要求，此用例跳过. sum(fee)={sum(fee)}')

        # 加购满35不满49验证运费--金牌用户免运费，银牌用户5.95， 铜牌用户6.95
        # 查询购物车v5
        cart = QueryPreOrder().query_preorder_v5(headers=ec_zhuli_header)
        final_amount = cart["object"]["final_amount"]
        CommCheckProductsWithCart().check_product_exists_in_cart(
            headers=ec_zhuli_header,
            cart_domain="grocery",
            product_id=product_list[0][0],
            final_amount=final_amount
        )
        # 3. 判断运费一定存在于[4.95, 5.95, 6.95]
        # 判断用户等级，不用等级运费不同
        AccountRest().get_account_rewards_level(headers=ec_zhuli_header)
        account_level = self.response['object']['current_level_label']
        assert account_level in ['Gold', 'Silver',
                                 'Bronze'], f"用户等级不在'Gold', 'Silver', 'Bronze'中，用户等级为：{account_level}"
        # 获取当前购物车运费
        real_shipping_fee = [float(section['fee_info']['shipping_fee']) for section in cart['object']['sections']]
        # 获取当前购物车商品金额，不包含运费，只有一个生鲜购物车
        current_product_amount = CommonCheck.compute_total_price_in_cart_not_include_freight(cart['object']['sections'])
        expect_fee = CommonCheck.check_shipping_fee(account_level, current_product_amount)
        # 根据上面的加购商品，一定是生鲜商品，所以不存在商品在多个购物车的情况，所以运费就是一个生鲜购物车的运费
        # 如果存在多个购物车，这样计算【期望运费】是错误的
        assert expect_fee == sum(
            real_shipping_fee), f"用户加购大于79但运费不正确, 期望运费为{expect_fee}, 实际运费为：{real_shipping_fee}，用户等级为{account_level}，当前购物车金额为：{current_product_amount}"

        # 校验小费，为第三档，4$
        pre = self._get_pre(ec_zhuli_header)
        self._pre_tip_assertion(pre, 2)

        # 4. 校验服务费
        self._check_service_fee(account_level, current_product_amount, pre)


    def _check_service_fee(self, account_level, current_product_amount, pre):
        """
        校验服务费
        """
        expect_service_fee = CommonCheck.check_service_fee(account_level, current_product_amount)
        # 获取pre
        # 实际服务费 == 预期服务费
        assert float(pre.get('object').get('fee_info').get('service_fee')) - float(expect_service_fee) < 0.000001, f"pre={pre}, expect_service_fee={expect_service_fee}"

    def _get_pre(self, headers):
        # 应用地址
        address_list = QueryUserAddressList().address_list(headers)
        # 应用地址
        ApplyUserAddressInformation().address_apply_v2(headers, address_list.get("object")[0].get("address_id") or 3511266)
        PaymentCategory().payment_category(headers, "B")
        # 预结算
        pre = PrepareCheckout().prepare_checkout_wechat_or_paypal_v2(
            headers,
            {"delivery_window_id": "", "cart_domain": "grocery"}
        )
        return pre

    def _pre_tip_assertion(self, pre, index_level: int):
        assert len(pre.get('object').get('tip_info').get('options')) == 4, f"pre={pre}"
        assert pre.get('object').get('tip_info').get('options')[0].get('tip') == 2, f"pre={pre}"
        assert pre.get('object').get('tip_info').get('options')[1].get('tip') == 3, f"pre={pre}"
        assert pre.get('object').get('tip_info').get('options')[2].get('tip') == 4, f"pre={pre}"
        assert pre.get('object').get('tip_info').get('options')[3].get('tip') == 0, f"pre={pre}"
        assert pre.get('object').get('tip_info').get('selected_option_index') == index_level, f"pre={pre}"





