"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :  test_add_to_cart_preference.py
@Description    :  
@CreateTime     :  2023/9/11 13:49
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/9/11 13:49
"""
import weeeTest
from test_dir.api.ec.ec_item.recommend.api_bought import GetBoughtProducts
from test_dir.api.ec.ec_so.preorder.update_preorderline import UpdatePreOrderLine


class TestAddToCartBought(weeeTest.TestCase):
    @weeeTest.data.file(file_name='ec_so_data.json', return_type='dict')
    @weeeTest.mark.list('Regression', 'Smoke', 'Transaction')
    def test_add_to_cart_bought(self, *args, ec_zhuli_header):
        """购物车-添加曾经购买的商品"""
        # todo @zhuli  目前测试多次，曾经购买数据为大数据推荐，所以没办法准备数据， line 30分支未到达
        # 获取登录header
        products = GetBoughtProducts().get_bought_products_cart(headers=ec_zhuli_header)[
            "object"]["products"]
        result = [(x["id"]) for x in products]
        if result:
            # 加购购物车-曾经购买的第一个商品
            UpdatePreOrderLine().porder_items_v3(headers=ec_zhuli_header,
                                                 source=args[0]["cart"]["source_bought"],
                                                 product_id=result[0])
            # CommCheckProductsWithCart().check_product_add_to_cart_success(headers=RequestHeader.ec_zhuli_header,
            #                                                               cart_domain=args[0]["cart"][
            #                                                                   "cart_domain"],
            #                                                               product_id=result[0])
            # 判断加购成功
            assert self.response["result"] is True and len(
                self.response["object"]["updateItems"]) > 0, f"product id is {result[0]}, response is {self.response}"
            print("购物车-曾经购买商品加购成功", result[0])
        else:
            print("购物车-曾经购买商品列表为空")


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net', env='tb1')
