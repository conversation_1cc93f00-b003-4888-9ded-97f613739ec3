import json

import weeeTest

from test_dir.api.ec.ec_item.search_v3.search_by_catalogue_content import SearchByCatalogueContent
from test_dir.api.ec.ec_so.preorder.query_preorder import QueryPreOrder
from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder
from test_dir.api.ec.ec_so.preorder.update_preorderline import UpdatePreOrderLine
from test_dir.api.ec.ec_so.save_for_later.cart_to_save_for_later import CartToSaveForLater
from test_dir.api_case.ec.common.remove_all_products_in_cart import RemoveAllProductsInCart
from test_dir.api_case.ec.common.set_user_porder import SetUserPorder


class TestMobileCartV2(weeeTest.TestCase):
    @weeeTest.mark.list('Regression')
    def test_empty_cart_grocery(self, ec_zhuli_header):
        """mobile购物车-空购物车的样式 生鲜"""
        # 获取用户的preorder
        SetUserPorder().set_user_zipcode(ec_zhuli_header, "98011")
        QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_zhuli_header)

        assert self.response["result"] is True
        porder = self.response["object"]
        deal_date = porder["delivery_pickup_date"]
        zipcode = porder["zipcode"]

        # 1. 清空购物车商品
        RemoveAllProductsInCart().remove_all_products_in_grocery_cart(headers=ec_zhuli_header)
        # 2. 查询购物车
        grocery_cart = QueryPreOrder().query_preorder_v5(headers=ec_zhuli_header, cart_domain="grocery")
        assert grocery_cart['object']['save_for_later_response'] is None
        sections = grocery_cart["object"]["sections"]
        assert len(sections) == 0

        pantry = SearchByCatalogueContent().search_by_catalogue_content(
            headers=ec_zhuli_header,
            zipcode=zipcode,
            date=deal_date,
            filter_sub_category="sale",
            filters=json.dumps({"pantry": True})
        )
        assert pantry["object"]["contents"], f"当前环境下没有pantry商品，请检查，接口返回的结果为{pantry['object']}"
        available_pantry_products = [item for item in
                                     pantry["object"]["contents"] if
                                     item['type'] != 'carousel' and item["data"]["sold_status"] == "available"]
        assert available_pantry_products, f"filter_sub_category=new下没有可用的pantry商品，response={pantry}"

        UpdatePreOrderLine().porder_items_v3(
            headers=ec_zhuli_header,
            product_id=available_pantry_products[0]['data']['id'],
            quantity=available_pantry_products[0]['data']['min_order_quantity']
        )
        assert self.response["result"] is True and len(
            self.response["object"]["updateItems"]) > 0, f"product id is {available_pantry_products[0]}, response is {self.response}"
        # 将商品移至稍后再买
        CartToSaveForLater().cart_to_save_for_later(
            headers=ec_zhuli_header,
            product_keys=[available_pantry_products[0]['data']['id']]
        )
        grocery_cart_with_save_for_later = QueryPreOrder().query_preorder_v5(headers=ec_zhuli_header, cart_domain="grocery")
        # 验证购物车有稍后再买模块
        assert grocery_cart_with_save_for_later['object']['display_save_for_later'], f"grocery_cart_with_save_for_later={grocery_cart_with_save_for_later}"
