"""
<AUTHOR>  zhongyuan.xu
@Version        :  V1.0.0
------------------------------------
@File           :  test_global_pulus_to_cart.py
@Description    :
@CreateTime     :  2024/4/28 9:07
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2024/4/28 9:07
"""
import json

import weeeTest

from test_dir.api.ec.ec_item.search_v3.search_by_catalogue_content import SearchByCatalogueContent
from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder
from test_dir.api.ec.ec_so.preorder.update_preorderline import UpdatePreOrderLine
from test_dir.api_case.ec.common.comm_check_products_with_cart import CommCheckProductsWithCart
from test_dir.api_case.ec.common.common_buy_products import CommonBuy
from test_dir.api_case.ec.common.remove_all_products_in_cart import RemoveAllProductsInCart
from test_dir.api_case.ec.common.set_user_porder import SetUserPorder


class TestPantryToCart(weeeTest.TestCase):
    @weeeTest.mark.list('Regression', 'Smoke')
    def test_global_plus_to_cart(self, ec_login_header):
        """购物车-添加global商品到购物车--110203	加购mkpl商品到购物车"""
        "已标注"
        # 获取用户的porder
        SetUserPorder().set_user_porder(headers=ec_login_header)
        # 获取用户的preorder
        QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)
        assert self.response["result"] is True
        porder = self.response["object"]
        deal_date = porder["delivery_pickup_date"]
        zipcode = porder["zipcode"]
        # 清空购物车
        RemoveAllProductsInCart().remove_all_products_in_grocery_cart(headers=ec_login_header)

        global_products = SearchByCatalogueContent().search_by_catalogue_content(
            headers=ec_login_header,
            zipcode=zipcode,
            date=deal_date,
            filter_sub_category="sale",
            filters=json.dumps({"global": True})
        )
        assert global_products["object"]["contents"], f"当前环境下没有pantry商品，请检查，接口返回的结果为{global_products['object']}"
        available_global_products = [[item['data']['id'], item['data']['sales_min_order_quantity']] for item in
                                     global_products["object"]["contents"] if item['type'] != 'carousel' and item["data"]["sold_status"] == "available"]

        # 加购商品
        for index, item in enumerate(available_global_products):
            UpdatePreOrderLine().porder_items_v3(
                headers=ec_login_header,
                product_id=item[0],
                quantity=item[1]
            )
            assert self.response["result"] is True and len(
                self.response["object"]["updateItems"]) > 0, f"product id is {item[0]}, response is {self.response}"
            CommCheckProductsWithCart().check_product_exists_in_cart(
                headers=ec_login_header,
                cart_domain="grocery",
                product_id=item[0]
            )
            if index == 10:
                break

        # 加购Upsell商品
        CommonBuy.buy_upsell_product(ec_login_header)

        # # 使用Paypal支付，产生一个待支付的订单（不产生订单，用户可能投诉）
        # order_info = CommonPayment.pay_with_paypal(RequestHeader.ec_login_header)
        # order_ids = order_info["order_ids"]
        # assert order_ids, f"使用paypal支付，未产生订单号，接口返回结果为：{order_ids}"
        # # 取消待支付订单
        # CancelOrder().cancel_unpaid_order_new(headers=RequestHeader.ec_login_header, order_ids=order_ids)
        # assert self.response['object'] == 'success'
