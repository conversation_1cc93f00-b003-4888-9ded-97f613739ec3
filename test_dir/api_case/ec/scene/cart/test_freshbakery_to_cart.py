import weeeTest

from test_dir.api.ec.ec_item.search_v3.search_by_catalogue_content import SearchByCatalogueContent
from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder
from test_dir.api.ec.ec_so.preorder.update_preorderline import UpdatePreOrderLine
from test_dir.api.ec.ec_so.preorder.update_zipcode import UpdateZipcode
from test_dir.api_case.ec.common.comm_check_products_with_cart import CommCheckProductsWithCart


class TestFreshBakeryToCart(weeeTest.TestCase):

    @weeeTest.mark.list("Regression", "product")
    def test_add_fresh_bakery_to_cart(self, ec_login_header):
        """
        【100622】 验证Global+ FBW Pantry+购物车
        """
        # 获取用户的preorder
        UpdateZipcode().update_zipcode_v3(ec_login_header)
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]
        deal_date = porder["deal_date"]
        zipcode = porder["zipcode"]
        fresh_bakery = SearchByCatalogueContent().search_by_catalogue_content(
            headers=ec_login_header,
            zipcode=zipcode,
            date=deal_date,
            filter_sub_category="freshbakery"
        )

        assert fresh_bakery["object"]["contents"], f"当前环境下没有fresh bakery商品，请检查，接口返回的结果为{fresh_bakery['object']}"
        available_bakery_products = [item['data'] for item in
                                     fresh_bakery["object"]["contents"] if item['type'] != 'carousel' and item["data"]["sold_status"] == "available"]

        # 加购商品
        for index, item in enumerate(available_bakery_products):
            UpdatePreOrderLine().porder_items_v3(
                headers=ec_login_header,
                product_id=item['id'],
                quantity=item['sales_min_order_quantity']
            )
            assert self.response["result"] is True and len(
                self.response["object"]["updateItems"]) > 0, f"product is {item}, response is {self.response}"
            CommCheckProductsWithCart().check_product_exists_in_cart(
                headers=ec_login_header,
                cart_domain="grocery",
                product_id=item['id']
            )

            # 商品从购物车去除
            remove_fresh = UpdatePreOrderLine().porder_items_v3(
                headers=ec_login_header,
                product_id=item['id'],
                quantity=0
            )

            # 判断已删除的商品在购物车中不存在
            CommCheckProductsWithCart().check_product_exists_in_cart(
                headers=ec_login_header,
                cart_domain="grocery",
                product_id=item['id'],
                is_in=False
            )
            if index == 0:
                break

