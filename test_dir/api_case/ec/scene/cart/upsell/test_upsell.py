"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :  test_add_free_trade_in.py
@Description    :  
@CreateTime     :  2023/8/29 11:22
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/8/29 11:22
"""
import copy

import pytest
import requests
import weeeTest

from test_dir.api.ec.ec_item.my_favorite.api_favorites import ApiFavorites
from test_dir.api.ec.ec_item.search_v3.search_by_catalogue_content import SearchByCatalogueContent as SCC
from test_dir.api.ec.ec_so.order.prepare_checkout import PrepareCheckout
from test_dir.api.ec.ec_so.order.upshell import Upshell
from test_dir.api.ec.ec_so.preorder.query_preorder import QueryPreOrder
from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder
from test_dir.api.ec.ec_so.preorder.update_preorderline import UpdatePreOrderLine
from test_dir.api.ec.ec_so.preorder.update_zipcode import UpdateZipcode
from test_dir.api_case.ec.common.comm_check_products_with_cart import CommCheckProductsWithCart
from test_dir.api_case.ec.common.remove_all_products_in_cart import RemoveAllProductsInCart
from test_dir.api_case.ec.common.set_user_porder import SetUserPorder


class TestAddFreeTradeIn(weeeTest.TestCase):
    @weeeTest.mark.list('test_upsell_combine', 'Regression', 'Smoke-debug', 'Transaction')
    def test_upsell_combine(self, ec_login_header):
        """生鲜购物车-upsell验证流程"""
        # # 获取用户的porder
        # SetUserPorder().set_user_porder(headers=RequestHeader.ec_login_header)
        # 获取用户的preorder
        # porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=RequestHeader.ec_login_header)["object"]
        upsell = Upshell().upsell_v2(headers=ec_login_header)
        # upsell不能保证每次都有数据
        if len(upsell["object"]["upsell_list"]) > 0:
            # assert len(upsell["object"]["upsell_list"]) > 0, f'upsell 返回数据异常，请确认{upsell}'
            assert upsell["object"]["title"] is not None, f'upsell 返回数据异常，请确认{upsell}'
            upsell_list = upsell["object"]["upsell_list"]
            for upsell in upsell_list:
                assert len(upsell["items"]) > 0, f'upsell 返回数据异常，请确认{upsell}'
                assert upsell["source"] is not None, f'upsell 返回数据异常，请确认{upsell}'
                assert upsell["title"] is not None, f'upsell 返回数据异常，请确认{upsell}'
                assert upsell["type"] is not None, f'upsell 返回数据异常，请确认{upsell}'
                assert upsell["view_more_link"] is not None, f'upsell 返回数据异常，请确认{upsell}'
                assert upsell["source"] == upsell["type"], f'upsell 返回数据异常，请确认{upsell}'
                if upsell["type"] == "checkout_recently_viewed":
                    # 最近浏览
                    assert "/item/checkout/upsell/view_more?source=checkout_recently_viewed" in upsell[
                        "view_more_link"], f'upsell 返回数据异常，请确认{upsell}'
                elif upsell["type"] == "checkout_fresh_bakery":
                    # 现做面包
                    assert "/item/checkout/upsell/view_more?source=checkout_fresh_bakery" in upsell[
                        "view_more_link"], f'upsell 返回数据异常，请确认{upsell}'
                elif upsell["type"] == "checkout_fresh_deli":
                    # 餐馆卤味
                    assert "/item/checkout/upsell/view_more?source=checkout_fresh_deli" in upsell[
                        "view_more_link"], f'upsell 返回数据异常，请确认{upsell}'
                elif upsell["type"] == "checkout_sale":
                    # 特价精选
                    assert "/item/checkout/upsell/view_more?source=checkout_sale" in upsell[
                        "view_more_link"], f'upsell 返回数据异常，请确认{upsell}'
                elif upsell["type"] == "checkout_complete":
                    # 添加到购物车
                    assert "/item/checkout/upsell/view_more?source=checkout_complete" in upsell[
                        "view_more_link"], f'upsell 返回数据异常，请确认{upsell}'

                # 滑动查看更多
                view_more = Upshell().upshell_view_more(headers=ec_login_header, source=upsell["source"])
                assert view_more["object"] is not None, f'upsell 查看更多数据异常，请确认{view_more}'
                # 断言 upsell 里商品
                # biz type 等接口返回
                # CommonCheck().check_product_info(category_type="others",source="others",products=upsell["items"])

    @weeeTest.mark.list('Regression', 'Smoke')
    def test_upsell_add_to_cart_and_check_price(self, ec_login_header):
        """
        【110181】 upsell-对继续加购页的商品进行加购
        """
        UpdateZipcode().update_zipcode_v3(ec_login_header)
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]

        # 清空购物车
        RemoveAllProductsInCart().remove_all_products_in_grocery_cart(headers=ec_login_header)
        # 1. 通过分类接口获取生鲜类的商品
        normal = SCC().search_by_catalogue_content(
            headers=ec_login_header,
            zipcode=porder["zipcode"],
            date=porder["delivery_pickup_date"],
            filter_sub_category="fruits"
        )
        assert normal["object"][
            "contents"], f"当前环境下没有生鲜商品,filter_sub_category=fruits，请检查，接口返回的结果为{normal['object']}"
        available_grocery_products = [[item['data']['id'], item['data']['sales_min_order_quantity']] for item in
                                      normal["object"]["contents"] if
                                      item['type'] != 'carousel' and item["data"]["sold_status"] == "available"]

        # 加购生鲜商品
        # 加购商品
        for index, item in enumerate(available_grocery_products):
            UpdatePreOrderLine().porder_items_v3(
                headers=ec_login_header,
                product_id=item[0],
                quantity=item[1]
            )
            assert self.response["result"] is True and len(
                self.response["object"]["updateItems"]) > 0, f"product id is {item[0]}, response is {self.response}"
            CommCheckProductsWithCart().check_product_exists_in_cart(
                headers=ec_login_header,
                cart_domain="grocery",
                product_id=item[0]
            )

            if index == 2:
                break

        # 1. 查询Upsell
        upsell = Upshell().upsell_v2(headers=ec_login_header)
        if upsell['object']['upsell_list']:
            available_upsell_products = [product for product in upsell['object']['upsell_list'][0]['items'] if
                                         product['sold_status'] == 'available' and product['id']]
            assert available_upsell_products, f"当前环境下没有可以加购的Upsell商品，请检查，available_upsell_products={available_upsell_products}"

            # 2. 加购upsell
            buy_upsell = UpdatePreOrderLine().porder_items_v3(
                headers=ec_login_header,
                product_id=available_upsell_products[0]['id'],
                quantity=available_upsell_products[0]['min_order_quantity'],
                date=porder['delivery_pickup_date']
            )
            assert buy_upsell.get('object').get('updateItems'), f"加购Upsell失败，buy_upsell={buy_upsell}"
            # 3.查询并校验upsell商品price
            sections = \
                QueryPreOrder().query_preorder_v5(headers=ec_login_header, cart_domain='grocery')["object"][
                    "sections"]
            assert sections, f"当前购物车为空，sections={sections}"
            common_product = [item for section in sections for item in section.get('items') if
                              len(section.get('items')) > 0]

            product_in_cart = None
            for item in common_product:
                if item['product_id'] == available_upsell_products[0]['id']:
                    product_in_cart = item
                    break
            assert product_in_cart, f"购物车中没有加购的upsell商品，common_product={common_product}, available_upsell_products={available_upsell_products}"

            # 3.1 校验upsell加购前和购物车中的price一致
            assert product_in_cart.get('price') in [available_upsell_products[0]['price'],
                                                available_upsell_products[0].get('volume_price')] or product_in_cart.get('unit_price') in [available_upsell_products[0]['price'],
                                                available_upsell_products[0].get('volume_price')] , \
                f"upsell加购前和购物车中的price不一致，product_in_cart={product_in_cart}, available_upsell_products={available_upsell_products}"
        else:
            pytest.skip("upsell数据为空，本用例跳过执行")

    @weeeTest.mark.list('Regression', 'Smoke')
    def test_upsell_fresh_daily_divided_into_two_parts(self, ec_login_header):
        """
        【110152】 upsell页面-fresh daily数据拆成2块
        """
        SetUserPorder().set_user_new_porder(headers=ec_login_header, zipcode="98011", lang="en")
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]

        # 清空购物车
        RemoveAllProductsInCart().remove_all_products_in_grocery_cart(headers=ec_login_header)
        # 1. 通过分类接口获取生鲜类的商品
        normal = SCC().search_by_catalogue_content(
            headers=ec_login_header,
            zipcode=porder["zipcode"],
            date=porder["delivery_pickup_date"],
            filter_sub_category="fruits"
        )
        assert normal["object"][
            "contents"], f"当前环境下没有生鲜商品,filter_sub_category=fruits，请检查，接口返回的结果为{normal['object']}"
        available_grocery_products = [[item['data']['id'], item['data']['sales_min_order_quantity']] for item in
                                      normal["object"]["contents"] if
                                      item['type'] != 'carousel' and item["data"]["sold_status"] == "available"]

        # 加购生鲜商品
        # 加购商品
        for index, item in enumerate(available_grocery_products):
            UpdatePreOrderLine().porder_items_v3(
                headers=ec_login_header,
                product_id=item[0],
                quantity=item[1]
            )
            assert self.response["result"] is True and len(
                self.response["object"]["updateItems"]) > 0, f"product id is {item[0]}, response is {self.response}"
            CommCheckProductsWithCart().check_product_exists_in_cart(
                headers=ec_login_header,
                cart_domain="grocery",
                product_id=item[0]
            )

            if index == 2:
                break

        # 1. 查询Upsell
        upsell = Upshell().upsell_v2(headers=ec_login_header)
        # 北京时间上午运行此脚本时，可能出错，由于deal_date为当天，所以fresh bakery无货而不显示在upsell模块
        if upsell['object']['upsell_list']:
            assert any(item['title'] == 'Fresh Bakery' for item in upsell['object']['upsell_list']), f"upsell={upsell}"
            assert any(item['title'] == 'Fresh Gourmet' for item in upsell['object']['upsell_list']), f"upsell={upsell}"
        else:
            pytest.skip("upsell数据为空，本用例跳过执行")

    @weeeTest.mark.list('Regression', 'Smoke')
    def test_add_to_favourite_upsell_products(self, ec_login_header):
        """
            【100808】 继续加购页商品信息、数据正确，收藏功能验证
        """
        UpdateZipcode().update_zipcode_v3(ec_login_header)
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]

        # 清空购物车
        RemoveAllProductsInCart().remove_all_products_in_grocery_cart(headers=ec_login_header)
        # 1. 通过分类接口获取生鲜类的商品
        normal = SCC().search_by_catalogue_content(
            headers=ec_login_header,
            zipcode=porder["zipcode"],
            date=porder["delivery_pickup_date"],
            filter_sub_category="fruits"
        )
        assert normal["object"][
            "contents"], f"当前环境下没有生鲜商品,filter_sub_category=fruits，请检查，接口返回的结果为{normal['object']}"
        available_grocery_products = [[item['data']['id'], item['data']['sales_min_order_quantity']] for item in
                                      normal["object"]["contents"] if
                                      item['type'] != 'carousel' and item["data"]["sold_status"] == "available"]

        # 加购生鲜商品
        # 加购商品
        for index, item in enumerate(available_grocery_products):
            UpdatePreOrderLine().porder_items_v3(
                headers=ec_login_header,
                product_id=item[0],
                quantity=item[1]
            )
            assert self.response["result"] is True and len(
                self.response["object"]["updateItems"]) > 0, f"product id is {item[0]}, response is {self.response}"
            CommCheckProductsWithCart().check_product_exists_in_cart(
                headers=ec_login_header,
                cart_domain="grocery",
                product_id=item[0]
            )

            if index == 2:
                break

        # 1. 查询Upsell
        upsell = Upshell().upsell_v2(headers=ec_login_header)
        if upsell['object']['upsell_list']:
            available_upsell_products = [product for product in upsell['object']['upsell_list'][0]['items'] if
                                         product['sold_status'] == 'available' and product['id']]
            assert available_upsell_products, f"当前环境下没有可以加购的Upsell商品，请检查，available_upsell_products={available_upsell_products}"
            # 2. 收藏upsell商品
            ApiFavorites().favorites_set(headers=ec_login_header,
                                         target_id=available_upsell_products[0]['id'])
            assert self.response['result'], f"收藏失败，resp={self.response}"
        else:
            pytest.skip("upsell没有数据，无法收藏，跳过此用例执行")

    @weeeTest.mark.list('Regression', 'Smoke')
    def test_no_upsell_data_check(self, ec_login_header):
        """
        【110179】 upsell-upsell的交互
        """
        # 获取用户的preorder
        UpdateZipcode().update_zipcode_v3(ec_login_header)
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]
        # 清空购物车
        RemoveAllProductsInCart().remove_all_products_in_grocery_cart(headers=ec_login_header)
        # 1. 通过分类接口获取酒分类的商品
        alcohol = SCC().search_by_catalogue_content(
            headers=ec_login_header,
            zipcode=porder["zipcode"],
            date=porder["delivery_pickup_date"],
            filter_sub_category="alcohol"
        )
        assert alcohol["object"]["contents"], f"当前环境下没有酒类商品，请检查，接口返回的结果为{alcohol['object']}"
        available_alcohol_products = [[item['data']['id'], item['data']['sales_min_order_quantity']] for item in
                                      alcohol["object"]["contents"] if
                                      item['type'] != 'carousel' and item["data"]["sold_status"] == "available"]

        # 2. 加购酒分类的第一个商品
        UpdatePreOrderLine().porder_items_v3(
            headers=ec_login_header,
            product_id=available_alcohol_products[0][0],
            quantity=available_alcohol_products[0][1]
        )
        # 判断加购成功
        assert self.response["result"] is True and len(
            self.response["object"]["updateItems"]) > 0, f"product id is {available_alcohol_products[0][0]}"
        CommCheckProductsWithCart().check_product_exists_in_cart(
            headers=ec_login_header,
            cart_domain="grocery",
            product_id=available_alcohol_products[0][0]
        )

        # 1. 查询Upsell
        upsell = Upshell().upsell_v2(headers=ec_login_header)
        if len(upsell['object']['upsell_list']) == 0:
            # 没有upsell数据，直接结算
            pre = PrepareCheckout().prepare_checkout_v2_marketplace(headers=ec_login_header)
            assert pre['object']['fee_info']['final_amount'], f"预结算接口返回不正确，pre={pre}"
        else:
            pytest.skip("本条用例为校验upsell无数据的场景，数据不符合，跳过执行")

    @weeeTest.mark.list('Regression', 'Smoke')
    def test_fresh_daily_exists(self, ec_login_header):
        """
        【108189】 Fresh daily模块数据逻辑验证
        """
        # 获取用户的preorder
        UpdateZipcode().update_zipcode_v3(ec_login_header)
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]
        deal_date = porder["deal_date"]
        zipcode = porder["zipcode"]
        fresh_bakery = SCC().search_by_catalogue_content(
            headers=ec_login_header,
            zipcode=zipcode,
            date=deal_date,
            filter_sub_category="freshbakery"
        )

        assert fresh_bakery['object']['contents'], f"{zipcode}下没有fresh bakery数据"

        no_fresh_bakery_header = copy.deepcopy(ec_login_header)

        # 2. 更新用户zipcode
        UpdateZipcode().update_zipcode_v3(no_fresh_bakery_header, zipcode="94586")
        # 3. 变更Header
        no_fresh_bakery_header['Weee-zipcode'] = "94586"
        no_fresh_bakery_header['Zipcode'] = "94586"

        # 2. 更新用户的sales_org_id
        # 切换zipcode就是切换销售组织

        no_fresh_bakery_porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=no_fresh_bakery_header)[
            "object"]
        no_fresh_bakery = SCC().search_by_catalogue_content(
            headers=no_fresh_bakery_header,
            zipcode=no_fresh_bakery_porder['zipcode'],
            date=no_fresh_bakery_porder['deal_date'],
            filter_sub_category="freshbakery"
        )
        assert not no_fresh_bakery['object'][
            'contents'], f"94586地区存在fresh bakery, no_fresh_bakery={no_fresh_bakery}"

        UpdateZipcode().update_zipcode_v3(ec_login_header, zipcode="98011")
        no_fresh_bakery_header['Weee-Zipcode'] = "98011"
        no_fresh_bakery_header['Zipcode'] = "98011"

    @weeeTest.mark.list('Regression', 'Smoke')
    def test_upsell_no_duplicate_products_exits(self, ec_login_header):
        """
        【110153】 upsell页面-数据模块向下去重
        """
        UpdateZipcode().update_zipcode_v3(ec_login_header)
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]

        # 清空购物车
        RemoveAllProductsInCart().remove_all_products_in_grocery_cart(headers=ec_login_header)
        # 1. 通过分类接口获取生鲜类的商品
        normal = SCC().search_by_catalogue_content(
            headers=ec_login_header,
            zipcode=porder["zipcode"],
            date=porder["delivery_pickup_date"],
            filter_sub_category="fruits"
        )
        assert normal["object"][
            "contents"], f"当前环境下没有生鲜商品,filter_sub_category=fruits，请检查，接口返回的结果为{normal['object']}"
        available_grocery_products = [[item['data']['id'], item['data']['sales_min_order_quantity']] for item in
                                      normal["object"]["contents"] if
                                      item['type'] != 'carousel' and item["data"]["sold_status"] == "available"]

        # 加购生鲜商品
        # 加购商品
        for index, item in enumerate(available_grocery_products):
            UpdatePreOrderLine().porder_items_v3(
                headers=ec_login_header,
                product_id=item[0],
                quantity=item[1]
            )
            assert self.response["result"] is True and len(
                self.response["object"]["updateItems"]) > 0, f"product id is {item[0]}, response is {self.response}"
            CommCheckProductsWithCart().check_product_exists_in_cart(
                headers=ec_login_header,
                cart_domain="grocery",
                product_id=item[0]
            )

            if index == 2:
                break

        # 1. 查询Upsell
        upsell = Upshell().upsell_v2(headers=ec_login_header)
        upsell_products = []
        if upsell.get('object').get('upsell_list'):
            for i in upsell['object']['upsell_list']:
                upsell_products_ids = []
                if i.get("items"):
                    for index, j in enumerate(i.get("items")):
                        if j.get("id"):
                            if index < 5:
                                upsell_products_ids.append(j.get("id"))
                            upsell_products.append(j)
                    # 断言没有重复元素
                    assert len(upsell_products_ids) == len(
                        set(upsell_products_ids)), f"upsell_product_ids={upsell_products_ids}"
            # 断言所有商品可售
            assert all(
                k.get("sold_status") == "available" for k in upsell_products), f"upsell_products={upsell_products}"
        else:
            pytest.skip("upsell无数据，本用例跳过")

    @pytest.mark.repeat(1000)
    def hello_test_access_link(self):
        # 仅用于测试，不参与ec的测试。
        for url in [
            "https://www.sayweee.com/product/LULLABUY-Pure-Natural-Mulberry-Silk-Duvet-White-Double-Queen-Size-Total-3kg/2041814",
            "https://www.sayweee.com/en/product/LQQM-Cane-Sugar-Corn-Grain-Juice/106521",
            "https://www.sayweee.com/en/review/video/12965",
            "https://www.sayweee.com/mkpl/bakery/landing"]:
            res = requests.get(url=url)
            assert res.status_code in [200, 302], f"res={res}"
