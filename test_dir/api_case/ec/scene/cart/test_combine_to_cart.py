"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :  test_combine_to_cart.py
@Description    :
@CreateTime     :  2023/9/13 13:07
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/9/13 13:07
"""
import json

import weeeTest

from test_dir.api.ec.ec_item.search_v3.search_by_catalogue_content import SearchByCatalogueContent
from test_dir.api.ec.ec_so.preorder.query_preorder import QueryPreOrder
from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder
from test_dir.api.ec.ec_so.preorder.update_preorderline import UpdatePreOrderLine
from test_dir.api_case.ec.common.comm_check_products_with_cart import CommCheckProductsWithCart
from test_dir.api_case.ec.common.common_buy_products import CommonBuy
from test_dir.api_case.ec.common.common_check import CommonCheck
from test_dir.api_case.ec.common.remove_all_products_in_cart import RemoveAllProductsInCart
from test_dir.api_case.ec.common.set_user_porder import SetUserPorder


class TestCombineToCart(weeeTest.TestCase):
    @weeeTest.mark.list('test_combine_to_cart','Regression', 'Transaction', 'product', "Smoke")
    def test_combine_to_cart(self, ec_zhuli_header):
        """购物车-添加生鲜/酒/mkpl商品到购物车--[AT]验证加购多种类型购物车"""
        # "已标注"
        # 获取用户的porder
        SetUserPorder().set_user_new_porder(headers=ec_zhuli_header, zipcode="98011", lang='en')
        # 获取用户的preorder
        QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_zhuli_header)
        assert self.response["result"] is True
        porder = self.response["object"]
        deal_date = porder["delivery_pickup_date"]
        zipcode = porder["zipcode"]
        # 清空购物车
        RemoveAllProductsInCart().remove_all_products_in_grocery_cart(headers=ec_zhuli_header)

        # 1. 通过分类接口获取特价精选的pantry商品并加购
        pantry = SearchByCatalogueContent().search_by_catalogue_content(
            headers=ec_zhuli_header,
            zipcode=zipcode,
            date=deal_date,
            filter_sub_category="sale",
            filters=json.dumps({"pantry": True})
        )
        assert pantry["object"]["contents"], f"当前环境下没有pantry商品，请检查，接口返回的结果为{pantry['object']}"
        available_pantry_products = [[item['data']['id'], item['data']['sales_min_order_quantity']] for item in
                                     pantry["object"]["contents"] if item['type'] != 'carousel' and item["data"]["sold_status"] == "available"]

        self.add_product_to_cart_and_verify(ec_zhuli_header, available_pantry_products)

        # 2. 通过分类接口获取生鲜商品并加购
        normal = SearchByCatalogueContent().search_by_catalogue_content(
            headers=ec_zhuli_header,
            zipcode=zipcode,
            date=deal_date,
            filter_sub_category="fruits"
        )
        assert normal["object"][
            "contents"], f"当前环境下没有生鲜商品,filter_sub_category=fruits，请检查，接口返回的结果为{normal['object']}"
        available_grocery_products = [[item['data']['id'], item['data']['sales_min_order_quantity']] for item in
                                      normal["object"]["contents"] if
                                      item['type'] != 'carousel' and item["data"]["sold_status"] == "available"]
        self.add_product_to_cart_and_verify(ec_zhuli_header, available_grocery_products)

        # 3. 通过分类接口获取酒分类的商品并加购
        alcohol = SearchByCatalogueContent().search_by_catalogue_content(
            headers=ec_zhuli_header,
            zipcode=zipcode,
            date=deal_date,
            filter_sub_category="alcohol"
        )

        assert alcohol["object"]["contents"], f"当前环境下没有酒类商品，请检查，接口返回的结果为{alcohol['object']}"
        available_alcohol_products = [[item['data']['id'], item['data']['sales_min_order_quantity']] for item in
                                      alcohol["object"]["contents"] if item['type'] != 'carousel'and item["data"]["sold_status"] == "available"]
        self.add_product_to_cart_and_verify(ec_zhuli_header, available_alcohol_products)

        # 4. 通过分类接口获取mkpl分类的商品并加购
        mkpl = SearchByCatalogueContent().search_by_catalogue_content(
            headers=ec_zhuli_header,
            zipcode=zipcode,
            date=deal_date,
            filter_sub_category="sale",
            sort="recommend",
            filters=json.dumps({"global": True})
        )
        assert mkpl["object"]["contents"], f"当前环境下没有酒类商品，请检查，接口返回的结果为{mkpl['object']}"
        available_mkpl_products = [[item['data']['id'], item['data']['sales_min_order_quantity']] for item in
                                      mkpl["object"]["contents"] if item['type'] != 'carousel' and item["data"]["sold_status"] == "available"]

        self.add_product_to_cart_and_verify(ec_zhuli_header, available_mkpl_products)
        cart = QueryPreOrder().query_preorder_v5(headers=ec_zhuli_header)
        assert cart["object"]["sections"], f"购物车为空，购物车具体信息为：{cart}"
        # 5. 获取购物车的商品id, 并判断购物车有生鲜购物车，pantry购物车，seller购物车，注：酒属于生鲜购物车
        cart_ids = [item['cart_id'] for item in cart["object"]["sections"]]
        cart_types = [item['type'] for item in cart["object"]["sections"]]
        assert CommonCheck.list_check(["normal", "pantry"], cart_ids)
        assert CommonCheck.list_check(["normal", "pantry", "seller"], cart_types)

        CommonBuy.buy_upsell_product(ec_zhuli_header)

        # 使用Paypal支付，产生一个待支付的订单（不产生订单，用户可能投诉）
        # order_info = CommonPayment.pay_with_paypal(RequestHeader.ec_zhuli_header)
        # order_ids = order_info["order_ids"]
        # assert order_ids, f"使用paypal支付，未产生订单号，接口返回结果为：{order_ids}"
        # # 取消待支付订单
        # CancelOrder().cancel_unpaid_order_new(headers=RequestHeader.ec_zhuli_header, order_ids=order_ids)
        # assert self.response['object'] == 'success'

    def add_product_to_cart_and_verify(self, header, products: list):
        # 加购商品
        for index, item in enumerate(products):
            UpdatePreOrderLine().porder_items_v3(
                headers=header,
                product_id=item[0],
                quantity=item[1]
            )
            assert self.response["result"] is True and len(
                self.response["object"]["updateItems"]) > 0, f"product id is {item[0]}, response is {self.response}"
            CommCheckProductsWithCart().check_product_exists_in_cart(
                headers=header,
                cart_domain="grocery",
                product_id=item[0]
            )
            if index == 2:
                break



