"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :  test_add_to_cart_preference.py
@Description    :  
@CreateTime     :  2023/9/11 13:49
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/9/11 13:49
"""
import weeeTest

from test_dir.api.ec.ec_item.recommend.get_preference_products import GetPreferenceProducts
from test_dir.api.ec.ec_so.preorder.update_preorderline import UpdatePreOrderLine
from test_dir.api_case.ec.common.comm_check_products_with_cart import CommCheckProductsWithCart


class TestAddToCartPreference(weeeTest.TestCase):
    @weeeTest.data.file(file_name='ec_so_data.json', return_type='dict')
    @weeeTest.mark.list('Regression', 'Smoke',  'Transaction')
    def test_add_to_cart_preference(self, *args, ec_zhuli_header):
        """购物车-添加为你推荐的商品"""
        # 获取登录header
        GetPreferenceProducts().get_preference_products_cart(headers=ec_zhuli_header)
        assert self.response["result"] is True
        # 断言购物车下方猜你喜欢必须要有数据
        assert self.response["object"]["total_count"] > 0 and len(
            self.response['object']['products']) > 0, f"为你推荐无商品，请检查，self.response['object']是：{self.response['object']}"

        products = self.response["object"]["products"]
        result = [(x["id"]) for x in products]
        # 加购购物车-为你推荐商品的第一个商品
        UpdatePreOrderLine().porder_items_v3(
            headers=ec_zhuli_header,
            source=args[0]["cart"]["source_preference"],
            product_id=result[0]
        )
        assert self.response["result"] is True and self.response['object'][
            'updateItems'] != [], f"self.response.object.updateItems是{self.response['object']['updateItems']}"
        CommCheckProductsWithCart().check_product_add_to_cart_success(
            headers=ec_zhuli_header,
            cart_domain=args[0]["cart"]["cart_domain"],
            product_id=result[0]
        )

