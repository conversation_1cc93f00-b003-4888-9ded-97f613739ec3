"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :  test_preference_products_cart.py
@Description    :  
@CreateTime     :  2023/9/11 13:05
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/9/11 13:05
"""
import weeeTest
from test_dir.api.ec.ec_item.recommend.get_preference_products import GetPreferenceProducts


class TestPreferenceProductsCart(weeeTest.TestCase):
    @weeeTest.mark.list('Regression', 'Smoke',  'Transaction')
    def test_preference_products_cart(self, ec_zhuli_header):
        """购物车-获取购物车猜你喜欢的商品"""
        # 获取登录header
        # todo @zhuli 与test_add_to_cart_preference重复
        GetPreferenceProducts().get_preference_products_cart(headers=ec_zhuli_header)
        assert self.response["result"] is True
        assert self.response["object"]["products"], f"当前环境没有猜你喜欢的商品，请检查环境，接口返回的数据为：{self.response['object']}"

        result = [(x["id"]) for x in self.response["object"]["products"]]
        assert result, f"当前环境没有猜你喜欢的商品，请检查环境，接口返回的数据为：{result}"



