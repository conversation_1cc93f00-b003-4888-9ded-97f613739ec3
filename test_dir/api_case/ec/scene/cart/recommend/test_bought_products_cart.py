"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :  test_preference_products_cart.py
@Description    :  
@CreateTime     :  2023/9/11 13:05
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/9/11 13:05
"""
import weeeTest
from test_dir.api.ec.ec_item.recommend.api_bought import GetBoughtProducts


class TestBoughtProductsCart(weeeTest.TestCase):
    @weeeTest.mark.list('Regression', 'Smoke',  'Transaction')
    def test_bought_products_cart(self, ec_zhuli_header):
        """购物车-获取曾经购买商品数据"""
        # 获取登录header
        GetBoughtProducts().get_bought_products_cart(headers=ec_zhuli_header)
        assert self.response["result"] is True
        # todo @zhuli 以下分支未到达 self.response["object"]["total_count"]一直 == 0
        if self.response["object"]["total_count"] > 0:
            products = self.response["object"]["products"]
            result = [(x["id"]) for x in products]
            print("购物车曾经购买商品列表：", result)


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net', env='tb1')
