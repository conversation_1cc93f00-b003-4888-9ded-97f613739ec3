"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :  test_save_for_later_cart.py
@Description    :  
@CreateTime     :  2023/9/13 13:07
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/9/13 13:07
"""
import datetime
import json
import weeeTest
from test_dir.api.ec.ec_item.search_v3.search_by_catalogue_content import SearchByCatalogueContent
from test_dir.api.ec.ec_so.preorder.query_preorder import QueryPreOrder
from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder
from test_dir.api.ec.ec_so.preorder.update_preorderline import UpdatePreOrderLine
from test_dir.api.ec.ec_so.save_for_later.cart_to_save_for_later import CartToSaveForLater
from test_dir.api.ec.ec_so.save_for_later.save_for_later_item_move_to_cart import SaveForLaterItemMoveToCart
from test_dir.api_case.ec.common.comm_check_products_with_cart import CommCheckProductsWithCart
from test_dir.api_case.ec.common.remove_all_products_in_cart import RemoveAllProductsInCart
from test_dir.api_case.ec.common.set_user_porder import SetUserPorder
from weeeTest import log, jmespath


class TestSaveForLaterCart(weeeTest.TestCase):
    @weeeTest.data.file(file_name='ec_so_data.json', return_type='dict')
    @weeeTest.mark.list('B2B','Regression', 'Smoke')
    def test_save_for_later_to_cart(self, *args, ec_zhuli_header):
        """购物车-从购物车添加到稍后再买--【110169】【110170】 PC/mobile购物车-对购物车稍后再买验证"""
        "已标注"
        # 获取用户的porder
        SetUserPorder().set_user_porder(headers=ec_zhuli_header)
        # 获取用户的preorder
        QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_zhuli_header)
        assert self.response["result"] is True
        assert self.response['object']['user_id'] == 9158213
        porder = self.response["object"]
        deal_date = porder["delivery_pickup_date"]
        zipcode = porder["zipcode"]
        RemoveAllProductsInCart().remove_all_products_in_grocery_cart(headers=ec_zhuli_header)
        # 1. 通过分类接口获取零食商品
        normal = SearchByCatalogueContent().search_by_catalogue_content(
            headers=ec_zhuli_header,
            zipcode=zipcode,
            date=deal_date,
            filter_sub_category="snack",
            filters=json.dumps({"delivery": True}),
            sort="recommend"
        )
        items = jmespath(normal, "object.contents")
        product_list = []
        for item in items:
            # 通过分类获取type不是carousel并且商品状态不是change_other_day的商品
            if item["type"] != "carousel" and item["data"]["sold_status"] != "change_other_day":
                product_list.append([item["data"]["id"], item['data']['min_order_quantity']])
        assert product_list, f"category=snack, delivery=True, sort=recommend的可售商品不存在，product_list为{product_list}"
        if product_list:
            # 2. 加购零食分类返回的商品， 最多加4个
            for index, product_id in enumerate(product_list):
                UpdatePreOrderLine().porder_items_v3(
                    headers=ec_zhuli_header,
                    product_id=product_id[0],
                    quantity=product_id[1]
                )
                CommCheckProductsWithCart().check_product_exists_in_cart(
                    headers=ec_zhuli_header,
                    cart_domain='grocery',
                    product_id=product_id[0]
                )
                if index == 3:
                    break
        else:
            print("没有可售的生鲜商品,加购配置表里的商品")
            UpdatePreOrderLine().porder_items_v3(
                headers=ec_zhuli_header,
                product_id=args[0]["cart"]["product_id"],
                quantity=args[0]["cart"]["quantity"]
            )

        cart_for_save = QueryPreOrder().query_preorder_v5(headers=ec_zhuli_header, cart_domain='grocery')
        # section下cart
        carts = jmespath(cart_for_save, 'object.sections')
        # 3. 将商品从购物车加入稍后再买
        for cart in carts:
            # 取activity_info不为空的 normal生鲜购物车
            if cart['cart_id'] == 'normal' and cart['activity_info']:
                # 遍历购物车activity_info，存在id(不为null)的获取活动商品id跳出循环
                for activity in cart['activity_info']:
                    if activity['id']:
                        # item_list = jmespath(activity, 'items[*].product_id')
                        item_list = [item['product_id']
                                     for item in activity['items'] if item['price_type'] == 'normal']
                        if item_list:
                            print("生鲜购物车回活动目录下面的生鲜商品id", item_list)
                            CartToSaveForLater().cart_to_save_for_later(
                                headers=ec_zhuli_header,
                                product_keys=[item_list[0]]
                            )
                            assert self.response['object']["save_for_later_response"]["items"][0][
                                       "product_id"] == item_list[0], \
                                f"save_for_later_response.product_id is {self.response['object']['save_for_later_response']['items'][0]['product_id']}, product_id is {item_list[0]}"
                        else:
                            product_id = carts[0]["items"][0]["product_id"]
                            CartToSaveForLater().cart_to_save_for_later(
                                headers=ec_zhuli_header,
                                product_keys=[product_id]
                            )
                            assert self.response['object']["save_for_later_response"]["items"][0][
                                       "product_id"] == product_id, \
                                f"save_for_later_response.product_id is {self.response['object']['save_for_later_response']['items'][0]['product_id']}, product_id is {product_id}"
                        break
                    else:
                        # 这个地方曾经失败过，加个断言
                        assert carts, f"购物车为空，数据为：cart={carts}"
                        product_id = carts[0]["items"][0]["product_id"]
                        CartToSaveForLater().cart_to_save_for_later(
                            headers=ec_zhuli_header,
                            product_keys=[product_id]
                        )
                        assert self.response['object']["save_for_later_response"]["items"][0][
                                   "product_id"] == product_id, f"save_for_later_response.product_id is {self.response['object']['save_for_later_response']['items'][0]['product_id']}, product_id is {product_id}"
                        CommCheckProductsWithCart().check_product_add_to_saveforlater_success(
                            headers=ec_zhuli_header,
                            cart_domain="grocery",
                            product_id=product_id)

            else:
                log.info("cart的信息为：", cart)

    @weeeTest.data.file(file_name='ec_so_data.json', return_type='dict')
    @weeeTest.mark.list('B2B',"Regression", "Smoke")
    def test_cart_to_save_for_later(self, *args, ec_zhuli_header):
        """购物车-从稍后再买加回购物车"""
        "已标注"
        # # 获取用户的porder
        SetUserPorder().set_user_porder(headers=ec_zhuli_header)
        # 获取用户的preorder
        QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_zhuli_header)
        assert self.response["result"] is True

        # 如果稍后再买数组是空的就加购生鲜商品到购物车，通过购物车加购商品到稍后再买，再从稍后再买加购会购物车
        save_for_later = SaveForLaterItemMoveToCart().save4later_v2(headers=ec_zhuli_header)["object"]["items"]
        # 1. 如果第一个用例执行成功，save_for_later不会为None，与第一个用例有依赖
        if save_for_later:
            result = [(x["product_id"]) for x in save_for_later]
            product_id = result[0]
            SaveForLaterItemMoveToCart().save_for_later_item_move_to_cart(
                headers=ec_zhuli_header,
                product_keys=[product_id]
            )
            CommCheckProductsWithCart().check_product_add_to_cart_success(
                headers=ec_zhuli_header,
                cart_domain=args[0]["cart"]["cart_domain"],
                product_id=product_id
            )
        else:
            log.info("购物车稍后再买为空")

    @weeeTest.data.file(file_name='ec_so_data.json', return_type='dict')
    @weeeTest.mark.list('B2B','Regression-skip', 'tb1')
    def test_save4later_to_delete(self, *args, ec_zhuli_header):
        """对稍后再买的失效商品进行操作（修改送货日，设置通知）"""
        # 获取登录header2
        # 获取用户的porder
        SetUserPorder().set_user_porder(headers=ec_zhuli_header)
        # 获取用户的preorder
        QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_zhuli_header)
        assert self.response["result"] is True
        porder = self.response["object"]
        sales_org_id = porder["sales_org_id"]
        deal_date = porder["delivery_pickup_date"]
        delivery_date = porder["delivery_pickup_date"]
        zipcode = porder["zipcode"]
        cart = QueryPreOrder().query_preorder_v5(headers=ec_zhuli_header,
                                                 cart_domain=args[0]["cart"]["cart_domain"])
        save_for_later_items = jmespath(cart, 'object.save_for_later_response.items')
        if save_for_later_items is None:
            print("稍后再买无商品")
        else:
            product_lst = []
            # 查找需要切换送货日的商品点击切换送货日
            for item in save_for_later_items:
                if item["reason_type"] == 'change_date':
                    product_lst.append(item["product_id"])
                    if not product_lst:
                        log.info("稍后再买中没有失效的商品")
                    else:
                        log.info("打印失效商品", product_lst)
                        # 调用可用的日期接口
                        # datelist = GetValidDeliveryDates().so_delivery_date_item(headers=headers,
                        #                                                          product_id=product_lst)
                        # # 切换到第一个可用日期
                        # PorderDate().porder_date(headers=headers,
                        #                          delivery_pickup_date=datelist["object"]["delivery"][0]["date"])

                    assert self.response['object']['result'] is True


