"""
<AUTHOR>  zhongyuan.xu
@Version        :  V1.0.0
------------------------------------
@File           :  test_add_mof_to_cart.py
@Description    :  
@CreateTime     :  2024/8/8 16:52
@Software       :  PyCharm
------------------------------------
@ModifyTime     :
"""

import copy
import json

import pytest
import weeeTest
from test_dir.api.ec.ec_customer.account_rest.account_rest import AccountRest
from test_dir.api.ec.ec_item.search_v3.search_by_catalogue_content import SearchByCatalogueContent
from test_dir.api.ec.ec_mkt.activity.activity_trade_in import ActivityTradeIn
from test_dir.api.ec.ec_so.preorder.query_preorder import QueryPreOrder
from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder
from test_dir.api.ec.ec_so.preorder.update_preorderline import UpdatePreOrderLine
from test_dir.api.ec.ec_so.preorder.update_zipcode import UpdateZipcode
from test_dir.api_case.ec.common.comm_check_products_with_cart import CommCheckProductsWithCart
from test_dir.api_case.ec.common.common_check import CommonCheck
from test_dir.api_case.ec.common.remove_all_products_in_cart import RemoveAllProductsInCart


class TestAddMOFToCart(weeeTest.TestCase):
    @pytest.fixture(scope="function", autouse=True)
    def update_zipcode(self, ec_zhuli_header):
        yield
        UpdateZipcode().update_zipcode_v3(headers=ec_zhuli_header, zipcode="98011")
        ec_zhuli_header['Zipcode'] = '98011'
        ec_zhuli_header['Weee-Zipcode'] = '98011'

    @weeeTest.mark.list('not_regression')
    def test_verify_cart_style(self, ec_zhuli_header):
        """
        【110201】 MO/MOF购物车样式验证 非regression
        """
        # 1. 更新用户的zipcode到45229
        _header = copy.deepcopy(ec_zhuli_header)
        UpdateZipcode().update_zipcode_v3(headers=_header, zipcode="45229")
        _header['Zipcode'] = "45229"
        _header['Weee-Zipcode'] = "45229"
        # 1.1 获取用户的preorder
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=_header)["object"]
        deal_date = porder["delivery_pickup_date"]
        zipcode = porder["zipcode"]

        # 1.2 清空购物车
        RemoveAllProductsInCart().remove_all_products_in_grocery_cart(headers=_header)

        frozen = SearchByCatalogueContent().search_by_catalogue_content(
            headers=_header,
            zipcode=zipcode,
            date=deal_date,
            filter_sub_category="frozen"
        )
        assert frozen["object"]["contents"], f"当前环境下没有frozen商品,filter_sub_category=frozen，请检查，接口返回的结果为{frozen['object']}"
        available_frozen_products = [item["data"] for item in frozen["object"]["contents"]
                                     if item['type'] == 'product' and item['data']["sold_status"] == "available"]

        # 3. 加购商品
        self._add_to_cart(headers=_header, available_products=available_frozen_products)

        pantry = SearchByCatalogueContent().search_by_catalogue_content(
            headers=_header,
            zipcode=zipcode,
            date=deal_date,
            filter_sub_category="sale",
            filters=json.dumps({"pantry": True})
        )

        assert pantry["object"]["contents"], f"当前环境下没有pantry商品，请检查，接口返回的结果为{pantry['object']}"
        available_pantry_products = [item.get('data') for item in
                                     pantry["object"]["contents"] if item['type'] != 'carousel' and item["data"]["sold_status"] == "available"]

        self._add_to_cart(headers=_header, available_products=available_pantry_products)

        cart = QueryPreOrder().query_preorder_v5(headers=_header, cart_domain="grocery")
        # 共有2个购物车, 1个normal,1个pantry
        sections = cart.get('object').get('sections')
        assert len(sections) == 2, f"cart={cart}"
        for section in sections:
            match section.get('cart_id'):
                case "normal":
                    # normal购物车验证
                    assert CommonCheck.list_check(['cart_id', 'type', 'quantity', 'shipping_info', 'fee_info'], section.keys()), f"section={section}"
                case "pantry":
                    # pantry购物车验证
                    assert CommonCheck.list_check(['cart_id', 'type', 'quantity', 'shipping_info', 'fee_info'], section.keys()), f"section={section}"
                    assert section.get('shipping_info').get('shipping_type_desc') == 'Pantry+', f"section={section}"
                    assert section.get('shipping_info').get('shipping_desc') == 'Shipping via FedEx, UPS, etc.', f"section={section}"
                case _:
                    raise Exception("没有匹配结果，请检查购物车类型！")


    def _add_to_cart(self, headers, available_products, _index=0):
        if available_products:
            for index, item in enumerate(available_products):
                UpdatePreOrderLine().porder_items_v3(
                    headers=headers,
                    product_id=item['id'],
                    quantity=item['min_order_quantity']
                )
                assert self.response["result"] is True and len(
                    self.response["object"]["updateItems"]) > 0, f"product id is {item}, response is {self.response}"
                CommCheckProductsWithCart().check_product_exists_in_cart(
                    headers=headers,
                    cart_domain="grocery",
                    product_id=item['id']
                )

                if index == _index:
                    break
        else:
            raise Exception("可用商品列表为空，没有商品可以加购")

    # @weeeTest.mark.list('Regression', 'Transaction', 'product', 'Smoke')
    # mof需求变动，待恢复
    def test_add_mof_to_cart_h2(self, ec_zhuli_header):
        """
        [110202]MOF购物车流程验证  已标注
        【110201】 MO/MOF购物车样式验证 非regression
        """

        # 1. 更新用户的zipcode到00501
        mof_header = copy.deepcopy(ec_zhuli_header)
        UpdateZipcode().update_zipcode_v3(headers=mof_header, zipcode="00501")
        mof_header['Zipcode'] = "00501"
        mof_header['Weee-Zipcode'] = "00501"
        # 1.1 获取用户的preorder
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=mof_header)["object"]
        deal_date = porder["delivery_pickup_date"]
        zipcode = porder["zipcode"]

        # 1.2 清空购物车
        RemoveAllProductsInCart().remove_all_products_in_grocery_cart(headers=mof_header)

        # 2. 查询zipcode=00501的frozen商品
        frozen = SearchByCatalogueContent().search_by_catalogue_content(
            headers=mof_header,
            zipcode=zipcode,
            date=deal_date,
            filter_sub_category="frozen"
        )
        assert frozen["object"]["contents"], f"当前环境下没有frozen商品,filter_sub_category=frozen，请检查，接口返回的结果为{frozen['object']}"
        available_frozen_products = [item["data"] for item in frozen["object"]["contents"]
                                     if item['type'] == 'product' and item['data']["sold_status"] == "available"
                                     and item['data']['is_colding_package']]

        # 3. 加购商品
        self._add_to_cart(headers=mof_header, available_products=available_frozen_products)

        # 4. 查询用户等级及计算运费
        AccountRest().get_account_rewards_level(headers=mof_header)
        account_level = self.response['object']['current_level_label']
        assert account_level in ['Gold', 'Silver',
                                 'Bronze'], f"用户等级不在'Gold', 'Silver', 'Bronze'中，用户等级为：{account_level}"
        cart = QueryPreOrder().query_preorder_v5(headers=mof_header, cart_domain="grocery")
        sections = cart["object"]["sections"]
        assert sections, f"购物车不存在任何类型的购物车，sections={sections}"
        normal_cart = [cart for cart in sections if cart['cart_id'] == 'normal']
        assert len(normal_cart) == 1, f"normal_cart={normal_cart}"

        # 4.1 断言mof购物车样式：【110201】 MO/MOF购物车样式验证
        assert normal_cart[0].get('shipping_info').get('shipping_type_desc') == 'Direct mail', f"normal_cart={normal_cart}"
        assert normal_cart[0].get('shipping_info').get('shipping_desc') == 'Shipping via FedEx, UPS, etc.', f"normal_cart={normal_cart}"
        assert normal_cart[0].get('shipping_info').get('cold_package_fee'), f"normal_cart={normal_cart}"


        item_total = normal_cart[0]['fee_info']['sub_total_price']
        if float(item_total) < 35:
            assert normal_cart[0]['fee_info']['shipping_fee'] == '5.99', f"normal_cart={normal_cart}"
            # 冷链费用可能会改动，以前是9.99，现在改为4.99，后又改回9.99,没有准确的预期值
            assert normal_cart[0]['shipping_info']['cold_package_fee'] == '6.99', f"normal_cart={normal_cart}"

        # 5. 展示换购商品，但不可以加购
        trade_in_grocery = ActivityTradeIn().activity_trade_in(
            headers=mof_header,
            type="normal",
            deal_id=porder["deal_id"],
            delivery_sale_org_id=porder["sales_org_id"]
        )
        assert (trade_in_grocery["result"] is True and
                trade_in_grocery["object"]["total_count"] > 0), f"没有grocery换购商品，trade_in_grocery is: {trade_in_grocery}"

        trade_in_products = [product for product in trade_in_grocery['object']['products']]
        for index, trade_in_product in enumerate(trade_in_products):
            UpdatePreOrderLine().porder_items_v3_tradin(
                headers=ec_zhuli_header,
                product_id=trade_in_product['id'],
                quantity=trade_in_product['min_order_quantity']
            )
            # 判断加购成功
            assert self.response['result'] is True and self.response['object']['updateItems'] != [], \
                f"加购grocery换购商品失败，self.response is {self.response}, trade_in_product={trade_in_product}"

            # 判断购物车不存在加购的商品
            CommCheckProductsWithCart().check_product_exists_in_cart(
                headers=ec_zhuli_header,
                cart_domain='grocery',
                product_id=trade_in_product['id'],
                is_in=False
            )

            if index == 0:
                break

    # @weeeTest.mark.list('Regression', 'Transaction', 'product', 'Smoke')
    # mof需求变动，待恢复
    def test_add_mof_to_cart_amount_more_than_68_h2(self, ec_zhuli_header):
        """
        [110202]MOF购物车流程验证
        """
        "已标注"
        # 1. 更新用户的zipcode到00501
        mof_header = copy.deepcopy(ec_zhuli_header)
        UpdateZipcode().update_zipcode_v3(headers=mof_header, zipcode="00501")
        mof_header['Zipcode'] = "00501"
        mof_header['Weee-Zipcode'] = "00501"
        # 1.1 获取用户的preorder
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=mof_header)["object"]
        deal_date = porder["delivery_pickup_date"]
        zipcode = porder["zipcode"]

        # 1.2 清空购物车
        RemoveAllProductsInCart().remove_all_products_in_grocery_cart(headers=mof_header)

        # 2. 查询zipcode=00501的frozen商品
        frozen = SearchByCatalogueContent().search_by_catalogue_content(
            headers=mof_header,
            zipcode=zipcode,
            date=deal_date,
            filter_sub_category="frozen"
        )
        assert frozen["object"][
            "contents"], f"当前环境下没有frozen商品,filter_sub_category=frozen，请检查，接口返回的结果为{frozen['object']}"
        available_frozen_products = [item["data"] for item in frozen["object"]["contents"]
                                     if item['type'] == 'product' and item['data']["sold_status"] == "available"
                                     and item['data']['is_colding_package'] is True]

        # 3. 加购商品
        for index, item in enumerate(available_frozen_products):
            UpdatePreOrderLine().porder_items_v3(
                headers=mof_header,
                product_id=item['id'],
                quantity=item['max_order_quantity']
            )
            assert self.response["result"] is True and len(
                self.response["object"]["updateItems"]) > 0, f"product id is {item}, response is {self.response}"
            CommCheckProductsWithCart().check_product_exists_in_cart(
                headers=mof_header,
                cart_domain="grocery",
                product_id=item['id']
            )

            if index % 2 == 0:
                sections = QueryPreOrder().query_preorder_v5(headers=mof_header, cart_domain="grocery")["object"]["sections"]
                normal_cart = [cart for cart in sections if cart['cart_id'] == 'normal']
                item_total = normal_cart[0]['fee_info']['sub_total_price']
                if float(item_total) > 68:
                    break

        sections = QueryPreOrder().query_preorder_v5(headers=mof_header, cart_domain="grocery")["object"]["sections"]
        normal_cart = [cart for cart in sections if cart['cart_id'] == 'normal']
        item_total = normal_cart[0]['fee_info']['sub_total_price']

        # 断言加购已满68
        assert float(item_total) > 68, f"frozen商品加购未满68，item_total={item_total}"

        # 4. 展示换购商品，可以加购
        trade_in_grocery = ActivityTradeIn().activity_trade_in(
            headers=mof_header,
            type="normal",
            deal_id=porder["deal_id"],
            delivery_sale_org_id=porder["sales_org_id"]
        )
        assert (trade_in_grocery["result"] is True and trade_in_grocery["object"]["total_count"] > 0), \
            f"没有grocery换购商品，trade_in_grocery is: {trade_in_grocery}"

        trade_in_products = [product for product in trade_in_grocery['object']['products']]
        for index, trade_in_product in enumerate(trade_in_products):
            UpdatePreOrderLine().porder_items_v3_tradin(
                headers=ec_zhuli_header,
                product_id=trade_in_product['id'],
                quantity=trade_in_product['min_order_quantity']
            )
            # 判断加购成功
            assert self.response['result'] is True and self.response['object']['updateItems'] != [], \
                f"加购grocery换购商品失败，self.response is {self.response}, trade_in_product={trade_in_product}"

            # 判断购物车存在加购的商品
            CommCheckProductsWithCart().check_product_exists_in_cart(
                headers=ec_zhuli_header,
                cart_domain='grocery',
                product_id=trade_in_product['id'],
            )

            if index == 0:
                break









