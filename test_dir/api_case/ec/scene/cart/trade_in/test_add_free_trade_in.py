"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :  test_add_free_trade_in.py
@Description    :  
@CreateTime     :  2023/8/29 11:22
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/8/29 11:22
"""

import pytest
import weeeTest
import json

from weeeTest import log

from test_data.ec.simple.common import Header
from test_dir.api.ec.ec_customer.account_rest.account_rest import AccountRest
from test_dir.api.ec.ec_item.search_v3.search_by_catalogue_content import SearchByCatalogueContent
from test_dir.api.ec.ec_mkt.activity.activity_trade_in import ActivityTradeIn
from test_dir.api.ec.ec_so.preorder.query_preorder import QueryPreOrder
from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder
from test_dir.api.ec.ec_so.preorder.query_zipcode_by_region_id import QueryZipcodeByRegionId
from test_dir.api.ec.ec_so.preorder.update_preorderline import UpdatePreOrderLine
from test_dir.api_case.ec.common.comm_check_products_with_cart import CommCheckProductsWithCart
from test_dir.api_case.ec.common.comm_cms import CommCheckFunction
from test_dir.api_case.ec.common.common_check import CommonCheck
from test_dir.api_case.ec.common.get_root_dir import get_project_dir
from test_dir.api_case.ec.common.remove_all_products_in_cart import RemoveAllProductsInCart
from test_dir.api_case.ec.common.set_user_porder import SetUserPorder


class TestAddFreeTradeIn(weeeTest.TestCase):
    @weeeTest.mark.list('Regression', 'Smoke', 'test_add_free_trade_in', 'Transaction')
    def test_add_free_trade_in(self, ec_zhuli_header):

        # free_shipping_price = 35.00
        """生鲜购物车-加购满68可以添加换购  加购金额大于$68 110187	mobile购物车-换购流程验证
          【110196】 正-验证购物满68换购
        """
        # 切换到zipcode 98011 并获取用户的preorder
        porder = SetUserPorder().set_user_zipcode(headers=ec_zhuli_header, zipcode="98011")
        # 清空购物车商品
        RemoveAllProductsInCart().remove_all_products_in_grocery_cart(headers=ec_zhuli_header)
        # 判断用户等级，不用等级运费不同
        account_level = AccountRest().get_account_rewards_level(headers=ec_zhuli_header)
        assert account_level
        free_shipping_price = account_level["object"]["threshold"]
        trade_in_price = 68.00

        # 1. 查询是可用商品
        catalogue_content = CommCheckFunction().comm_catalogue_content(headers=ec_zhuli_header,
                                                                       date=porder["delivery_pickup_date"],
                                                                       zipcode=porder["zipcode"],
                                                                       filter_sub_category="new",
                                                                       sort="price_asc",
                                                                       filters="delivery_type_local")
        # 2. 加购商品，直至满68
        # 指定商品销售状态为可销售

        content_items = [
            content for content in catalogue_content["object"]["contents"]
            if content["type"] != "carousel" and content["data"]["sold_status"] == "available"
               and content["data"]["is_presale"] is False
        ]

        # 查询购物车
        preorder_v5 = self.query_preorder_v5_with_retry(headers=ec_zhuli_header,
                                                        cart_domain="grocery", products=content_items,
                                                        delivery_pickup_date=porder["delivery_pickup_date"]
                                                        )
        self.assert_cart_not_empty(preorder_v5)

        # 检查是否需要继续加购
        for section in preorder_v5["object"]["sections"]:
            if section["cart_id"] == "normal":

                sub_total_price = float(section["fee_info"]["sub_total_price"])

                # 如果大于68 满足换购
                if sub_total_price > trade_in_price:
                    # 检查购物车换购信息
                    self.check_cart_activity_info(activity_info=section["activity_info"], cart_type="normal",
                                                  sub_total_price=sub_total_price,
                                                  free_shipping_price=free_shipping_price,
                                                  trade_in_price=trade_in_price)
                    # 3. 查询换购商品
                    trade_in_grocery = ActivityTradeIn().activity_trade_in(
                        headers=ec_zhuli_header, type="normal",
                        deal_id=porder["deal_id"],
                        delivery_sale_org_id=porder["sales_org_id"]
                    )
                    assert trade_in_grocery["result"] is True and trade_in_grocery["object"][
                        "total_count"] > 0, f"没有grocery换购商品，trade_in_grocery is: {trade_in_grocery}"
                    # 校验换购页面商品信息
                    for index, product in enumerate(trade_in_grocery["object"]["products"]):
                        CommonCheck().check_product_info(product=product, category_type="others",
                                                         source="trade_in", filters="others", headers=ec_zhuli_header)
                        # CommonCheck().check_products_info(category_type="trade_in", source="trade_in",
                        #                                   products=trade_in_grocery["object"]["products"])

                    # 4. 加购换购商品
                    trade_in_ids = [[product['id'], product['min_order_quantity']] for product in
                                    trade_in_grocery['object']['products']]
                    for index, trade_id in enumerate(trade_in_ids):
                        UpdatePreOrderLine().porder_items_v3_tradin(
                            headers=ec_zhuli_header,
                            product_id=trade_id[0],
                            quantity=trade_id[1]
                        )

                        # 判断加购成功
                        assert self.response['result'] is True and self.response['object'][
                            'updateItems'] != [], f"加购grocery换购商品失败，self.response is {self.response}"
                        # 判断购物车是否存在加购的商品
                        CommCheckProductsWithCart().check_product_exists_in_cart(
                            headers=ec_zhuli_header,
                            cart_domain='grocery',
                            product_id=trade_id[0]
                        )
                        if index == 5:
                            break

                elif trade_in_price > sub_total_price >= free_shipping_price:

                    # 检查购物车换购信息
                    self.check_cart_activity_info(activity_info=section["activity_info"], cart_type="normal",
                                                  sub_total_price=sub_total_price,
                                                  free_shipping_price=free_shipping_price,
                                                  trade_in_price=trade_in_price)

                    # 点击去凑单
                    self.add_products_to_cart_and_verify(products=content_items,
                                                         delivery_pickup_date=porder["delivery_pickup_date"], headers=ec_zhuli_header)
                elif free_shipping_price > sub_total_price > 0.00:
                    # 购物金额大于0 小于免运费金额时，检查购物车换购信息
                    self.check_cart_activity_info(activity_info=section["activity_info"], cart_type="normal",
                                                  sub_total_price=sub_total_price,
                                                  free_shipping_price=free_shipping_price,
                                                  trade_in_price=trade_in_price)
                    # 点击继续加购
                    self.add_products_to_cart_and_verify(products=content_items,
                                                         delivery_pickup_date=porder["delivery_pickup_date"], headers=ec_zhuli_header)

    with open(get_project_dir() + "/test_data/lizhu2_token.json", 'r', encoding='utf-8') as f:
        zhuli_header = json.load(f)

    @weeeTest.mark.list('Regression', 'Smoke',  'Transaction')
    @pytest.mark.parametrize("empty_cart", [[zhuli_header, "grocery"]], indirect=True)
    def test_add_free_pantry_trade_in(self, empty_cart, ec_zhuli_header):
        """Pantry物车-加购满68可以添加换购  加购金额大于$68"""
        # 切换到销售组织=3下，拿到pantry 的deal_id
        QueryZipcodeByRegionId().query_zipcode_by_region_id(headers=ec_zhuli_header, zipcode="98011")
        # 获取用户在99348下的preorder
        pantry_porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_zhuli_header)["object"]
        pantry_deal_id = pantry_porder["deal_id"]

        # 获取用户的porder
        SetUserPorder().set_user_porder(headers=ec_zhuli_header)
        # 获取用户的preorder
        QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_zhuli_header)
        assert self.response["result"] is True
        porder = self.response["object"]

        # 清空购物车商品
        RemoveAllProductsInCart().remove_all_products_in_grocery_cart(headers=ec_zhuli_header)
        # 1. 在方食品分类下筛选出pantry商品并加购直至满68元
        SearchByCatalogueContent().search_by_catalogue_content(
            headers=ec_zhuli_header,
            zipcode=porder["zipcode"],
            date=porder["delivery_pickup_date"],
            filter_sub_category="instant",
            filters=json.dumps({"pantry": True})
        )
        pantry_content = self.response
        assert pantry_content["object"][
                   "total_count"] > 0, f"sub_category为instant且filters为pantry的条件下不存在商品，请检查环境"
        fee = 0.0
        if pantry_content["object"]["total_count"] > 0:
            for item in pantry_content["object"]["contents"]:
                # 销售状态为可销售
                if item['type'] != 'carousel' and item["data"]["sold_status"] == "available" and not item['data'][
                    'is_presale']:
                    # 加购，直至满68元
                    UpdatePreOrderLine().porder_items_v3(
                        headers=ec_zhuli_header,
                        product_id=item["data"]["id"],
                        quantity=item["data"]["sales_max_order_quantity"])
                    assert self.response['result'] is True and self.response['object'][
                        'updateItems'] != [], f"加购失败，self.response is {self.response}"

                    CommCheckProductsWithCart().check_product_exists_in_cart(
                        headers=ec_zhuli_header,
                        cart_domain='grocery',
                        product_id=item["data"]["id"])
                    cart_content = QueryPreOrder().query_preorder_v5(headers=ec_zhuli_header)
                    # 计算商品金额，不包含运费，必须大于68
                    fee = [float(section['fee_info']['sub_total_price']) for section in
                           cart_content['object']["sections"]]
                    if sum(fee) > 68:
                        break

            assert sum(fee) > 68, f"加购pantry商品不满68，不满足换购条件，购物车总金额为{sum(fee)}"

            # 2. 查询换购商品
            trade_in = ActivityTradeIn().activity_trade_in(
                headers=ec_zhuli_header, type="pantry",
                deal_id=pantry_deal_id,
                delivery_sale_org_id=porder["sales_org_id"]
            )
            assert trade_in["result"] is True and trade_in["object"][
                "total_count"] > 0, f"没有换购商品，trade in is: {trade_in}"

            # 3. 加购换购商品
            trade_in_ids = [[product['id'], product['min_order_quantity']] for product in
                            trade_in['object']['products']]
            for index, trade_id in enumerate(trade_in_ids):
                UpdatePreOrderLine().porder_items_v3_tradin(
                    headers=ec_zhuli_header,
                    product_id=trade_id[0],
                    quantity=trade_id[1]
                )
                # 判断加购成功
                assert self.response['result'] is True and self.response['object'][
                    'updateItems'] != [], f"加购换购商品失败，self.response is {self.response}"
                # 判断购物车是否存在加购的商品
                CommCheckProductsWithCart().check_product_exists_in_cart(
                    headers=ec_zhuli_header,
                    cart_domain='grocery',
                    product_id=trade_id[0]
                )
                if index == 3:
                    break

        else:
            log.info("pantry商品不存在，请检查环境")

    @weeeTest.mark.list('Regression', 'Smoke',  'Transaction', 'test_shop_more_products')
    def test_shop_more_products(self, ec_zhuli_header):
        shop_more_tab = ActivityTradeIn().activity_shop_more_tab(headers=ec_zhuli_header)
        assert len(shop_more_tab["object"]) > 0, f'去凑单页面tab数据异常{shop_more_tab}'
        for item in shop_more_tab["object"]:
            shop_more_products = ActivityTradeIn().item_activity_shop_more_products(
                headers=ec_zhuli_header, tab=item["key"])
            assert len(
                shop_more_products["object"]["products"]) > 0, f'去凑单{item["key"]}页面数据异常{shop_more_products}'
            priceGroups = shop_more_products["object"]["priceGroups"]
            products = shop_more_products["object"]["products"]
            # 切换价格区间tab
            self.check_shop_more_products(item, priceGroups, headers=ec_zhuli_header)
            # 检查商品基础信息
            for index, product in enumerate(products):
                CommonCheck().check_product_info(product=product, category_type="others", source="others", filters="others", headers=ec_zhuli_header)

    def query_preorder_v5_with_retry(self, headers, cart_domain, products, delivery_pickup_date):
        # 如果购物车为空，则走加购接口
        self.add_products_to_cart(products=products, delivery_pickup_date=delivery_pickup_date, headers=headers)
        preorder_v5 = QueryPreOrder().query_preorder_v5(headers=headers, cart_domain=cart_domain)
        return preorder_v5
        # try:
        #     preorder_v5 = QueryPreOrder().query_preorder_v5(headers=headers, cart_domain=cart_domain)
        #     # 如果购物车为空，则走加购接口
        #     self.add_products_to_cart(products=products, delivery_pickup_date=delivery_pickup_date)
        #     preorder_v5 = QueryPreOrder().query_preorder_v5(headers=headers, cart_domain=cart_domain)
        #     # if len(preorder_v5["object"]["sections"]) == 0:
        #     #     # 如果购物车为空，则走加购接口
        #     #     self.add_products_to_cart(products=products, delivery_pickup_date=delivery_pickup_date)
        #     #     preorder_v5 = QueryPreOrder().query_preorder_v5(headers=headers, cart_domain=cart_domain)
        #     return preorder_v5
        # except Exception as e:
        #     # 这里应该有一些日志记录异常
        #     print(f"Error querying preorder v5: {e}")
        #     raise

    def assert_cart_not_empty(self, preorder_v5):
        assert len(preorder_v5["object"]["sections"]) > 0, f'购物车商品为空，请确认{preorder_v5}'

    def add_products_to_cart(self, products, delivery_pickup_date, headers):
        # 加购
        CommCheckProductsWithCart().products_data_add_to_cart(
            headers=headers,
            products=products,
            porder_deal_date=delivery_pickup_date,
            product_source="mweb_category-item_list-null"
        )


    def check_cart_activity_info(self, activity_info, cart_type, sub_total_price, free_shipping_price, trade_in_price):
        if sub_total_price >= free_shipping_price:  # 返回换购信息
            activity = next((item for item in activity_info if item['type'] == "trade_in"), None)
            assert activity is not None, f"购物车里换购信息未返回{activity_info}"
            # for activity in activity_info:

            # 断言 赠品信息、tag 信息、文案 有返回
            if sub_total_price >= trade_in_price:
                assert float(activity["diff_amount"]) == 0.00, f'加购满68时，差额=0'
            else:
                assert float(activity["diff_amount"]) < trade_in_price, f'加购不满68时，差额小于68'

            assert float(activity["limit_amount"]) == 68, f'购物车换购信息异常{activity_info}'
            assert float(activity["limit_count"]) == 5, f'购物车换购信息异常{activity_info}'
            assert activity["limit_content"] is not None, f'购物车换购信息异常{activity_info}'
            assert activity["offer_content"] is not None, f'购物车换购信息异常{activity_info}'
            assert activity["tag"] is not None, f'购物车换购信息异常{activity_info}'
            assert activity["url"] is not None, f'购物车换购信息异常{activity_info}'
            if cart_type == "normal":
                assert "/promotion/trade-in?type=normal" in activity["url"], f'购物车换购信息异常{activity_info}'
            elif cart_type == "pantry":
                assert "/promotion/trade-in?type=pantry" in activity["url"], f'购物车换购信息异常{activity_info}'

        else:
            # 返回免运费提示信息
            activity = next((item for item in activity_info if item['type'] == "free_shipping"), None)
            assert activity is not None, f"购物车里换购信息未返回{activity_info}"
            assert float(activity["diff_amount"]) < free_shipping_price, f'加购不满35时，差额小于35'
            assert activity["url"] is not None, f'购物车换购信息异常{activity_info}'
            print(activity["url"])
            # 不足免运费门槛时，点击再买xx可享免运费banner 跳转分类页
            if cart_type == "normal":
                assert "/category/trending" in activity["url"], f'购物车换购信息异常{activity_info}'

    def add_products_to_cart_and_verify(self, products, delivery_pickup_date, headers):
        self.add_products_to_cart(products=products, delivery_pickup_date=delivery_pickup_date, headers=headers)
        # 再次查询购物车以验证商品是否成功加入
        preorder_v5 = self.query_preorder_v5_with_retry(headers=headers,
                                                        cart_domain="grocery",
                                                        products=products,
                                                        delivery_pickup_date=delivery_pickup_date
                                                        )
        self.assert_cart_not_empty(preorder_v5)

    def check_shop_more_products(self, item, priceGroups, headers):
        # 切换价格区间各tab
        for priceGroup in priceGroups:
            shop_more_products = ActivityTradeIn().item_activity_shop_more_products(
                headers=headers, price_group_key=priceGroup["group_key"],
                tab=item)
            assert len(shop_more_products["object"]["products"]) > 0, f'去凑单{item}页面数据异常{shop_more_products}'
