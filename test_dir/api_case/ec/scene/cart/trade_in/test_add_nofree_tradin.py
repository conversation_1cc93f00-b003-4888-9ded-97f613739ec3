"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :  test_add_nofree_tradin.py
@Description    :  
@CreateTime     :  2023/8/29 15:47
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/8/29 15:47
"""
import pytest
import weeeTest
import json

from weeeTest import jmespath

from test_dir.api.ec.ec_item.search_v3.search_by_catalogue_content import SearchByCatalogueContent
from test_dir.api.ec.ec_mkt.activity.activity_trade_in import ActivityTradeIn
from test_dir.api.ec.ec_so.preorder.query_preorder import QueryPreOrder
from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder
from test_dir.api.ec.ec_so.preorder.query_zipcode_by_region_id import QueryZipcodeByRegionId
from test_dir.api.ec.ec_so.preorder.update_preorderline import UpdatePreOrderLine
from test_dir.api_case.ec.common.comm_check_products_with_cart import CommCheckProductsWithCart
from test_dir.api_case.ec.common.common_check import CommonCheck
from test_dir.api_case.ec.common.remove_all_products_in_cart import RemoveAllProductsInCart
from test_dir.api_case.ec.common.set_user_porder import SetUserPorder


class TestAddNoFreeTradeIn(weeeTest.TestCase):
    @weeeTest.data.file(file_name='ec_so_data.json', return_type='dict')
    @weeeTest.mark.list('Regression', 'Smoke',  'Transaction')
    def test_add_nofree_trade_in(self, *args, ec_zhuli_header):
        """生鲜购物车-加购不满68不可以添加换购  加购金额$35-68
        【110196】 正 - 验证购物满68换购
        """
        available_prod_id, min_order_quantity = '', 0

        # 获取用户的porder
        SetUserPorder().set_user_porder(headers=ec_zhuli_header)
        # 获取用户的preorder
        QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_zhuli_header)
        assert self.response["result"] is True
        porder = self.response["object"]

        # 清空购物车商品
        RemoveAllProductsInCart().remove_all_products_in_grocery_cart(headers=ec_zhuli_header)
        # 加购不满68验证不可以加购换购商品

        # 1. 查询是可用商品
        catalogue_content = SearchByCatalogueContent().search_by_catalogue_content(
            headers=ec_zhuli_header,
            filter_sub_category="new",
            date=porder['delivery_pickup_date'],
            filters=json.dumps({"delivery": True}),  # {"delivery": True},
        )
        assert len(catalogue_content['object']['contents']) > 0, f'category为new没有商品，请检查环境'

        for item in catalogue_content["object"]["contents"]:
            # 销售状态为可销售
            if item['type'] != 'carousel' and item["data"]["sold_status"] == "available" and item["data"]["remaining_count"] > 0:
                if item["data"]["price"] < 68:
                    available_prod_id = item["data"]["id"]
                    min_order_quantity = item['data']['min_order_quantity']
                    break

        assert available_prod_id != '' and min_order_quantity != 0, f"category为New的商品没有价格少于68的商品"

        # 2. 就买1个，肯定不满68
        UpdatePreOrderLine().porder_items_v3(
            headers=ec_zhuli_header,
            product_id=available_prod_id,
            quantity=min_order_quantity
        )
        assert self.response['result'] is True and self.response['object'][
            'updateItems'] != [], f"加购失败，product_id={available_prod_id}, self.response is {self.response}"

        CommCheckProductsWithCart().check_product_exists_in_cart(
            headers=ec_zhuli_header,
            cart_domain='grocery',
            product_id=available_prod_id
        )

        # 3. 查询加购商品
        trade_in_grocery = ActivityTradeIn().activity_trade_in(
            headers=ec_zhuli_header,
            type=args[0]["preorder"]["type"],
            deal_id=porder["deal_id"],
            delivery_sale_org_id=args[0]["preorder"]["sales_org_id"]
        )
        assert trade_in_grocery["result"] is True and trade_in_grocery["object"][
            "total_count"] > 0, f"没有grocery换购商品，trade_in_grocery is: {trade_in_grocery}"
        # 4. 加购换购商品
        trade_in_ids = [[product['id'], product['min_order_quantity']] for product in
                        trade_in_grocery['object']['products']]
        for index, trade_id in enumerate(trade_in_ids):
            UpdatePreOrderLine().porder_items_v3_tradin(
                headers=ec_zhuli_header,
                product_id=trade_id[0],
                quantity=trade_id[1]
            )
            # 判断加购成功
            assert self.response['result'] is True and self.response['object'][
                'updateItems'], f"加购grocery换购商品失败，self.response is {self.response}"
            # 判断购物车是否存在加购的商品
            CommCheckProductsWithCart().check_product_exists_in_cart(
                headers=ec_zhuli_header,
                cart_domain='grocery',
                product_id=trade_id[0],
                is_in=False
            )
            if index == 3:
                break

    @weeeTest.mark.list('Regression', 'Smoke',  'Transaction')
    def test_add_nofree_pantry_trade_in(self, ec_zhuli_header):
        """Pantry物车-加购不满68不可添加换购"""
        available_prod_id, min_order_quantity = '', 0
        # 切换到销售组织=3下，拿到pantry 的deal_id
        QueryZipcodeByRegionId().query_zipcode_by_region_id(headers=ec_zhuli_header, zipcode=99348)
        # 获取用户在99348下的preorder
        pantry_porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_zhuli_header)["object"]
        pantry_deal_id = pantry_porder["deal_id"]
        # 切换回销售组织=4下
        QueryZipcodeByRegionId().query_zipcode_by_region_id(headers=ec_zhuli_header, zipcode="98011")
        # 获取用户的preorder
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_zhuli_header)["object"]
        # 清空购物车商品
        RemoveAllProductsInCart().remove_all_products_in_grocery_cart(headers=ec_zhuli_header)
        # 1. 在方食品分类下筛选出pantry 商品
        catalogue_content = SearchByCatalogueContent().search_by_catalogue_content(
            headers=ec_zhuli_header,
            zipcode=porder["zipcode"],
            date=porder["delivery_pickup_date"],
            filter_sub_category="beverages",
            filters=json.dumps({"pantry": True}),
        )
        assert len(catalogue_content['object'][
                       'contents']) > 0, f'category为beverages且filters=pantry没有商品，请检查环境，查询结果为：{catalogue_content}'

        for item in catalogue_content["object"]["contents"]:
            # 销售状态为可销售
            if item['type'] != 'carousel' and item["data"]["sold_status"] == "available" and item["data"]["remaining_count"] > 0:
                if item["data"]["price"] < 68:
                    available_prod_id = item["data"]["id"]
                    min_order_quantity = item['data']['min_order_quantity']
                    break
        assert available_prod_id != '' and min_order_quantity != 0, f"category为beverages的商品没有价格少于68的商品"
        # 2. 就买一个，不满68元
        if self.response["object"]["total_count"] > 0:
            UpdatePreOrderLine().porder_items_v3(
                headers=ec_zhuli_header,
                product_id=available_prod_id,
                quantity=min_order_quantity
            )
        # 3. 查询换购商品
        trade_in_grocery = ActivityTradeIn().activity_trade_in(
            headers=ec_zhuli_header,
            type="pantry",
            deal_id=pantry_deal_id,
            delivery_sale_org_id=porder["sales_org_id"]
        )
        assert trade_in_grocery["result"] is True and trade_in_grocery["object"][
            "total_count"] > 0, f"没有grocery换购商品，trade_in_grocery is: {trade_in_grocery}"
        # 4. 加购换购商品
        trade_in_ids = [[product['id'], product['min_order_quantity']] for product in
                        trade_in_grocery['object']['products']]
        for index, trade_id in enumerate(trade_in_ids):
            UpdatePreOrderLine().porder_items_v3_tradin(
                headers=ec_zhuli_header,
                product_id=trade_id[0],
                quantity=trade_id[1]
            )
            # 判断加购成功， 按理说这个接口就应该报错
            assert self.response['result'] is True and self.response['object'][
                'updateItems'] != [], f"加购grocery换购商品失败，self.response is {self.response}"
            # 判断购物车是否存在加购的商品
            CommCheckProductsWithCart().check_product_exists_in_cart(
                headers=ec_zhuli_header,
                cart_domain='grocery',
                product_id=trade_id[0],
                is_in=False
            )
            if index == 3:
                break

    @weeeTest.mark.list('Regression', 'Transaction', 'product', 'Smoke')
    def test_add_to_cart_between_49_68(self, ec_zhuli_header):
        """【109167】 加购金额 大于49 ，小于68 换购模块UX验证"""
        # 获取用户的porder
        SetUserPorder().set_user_porder(headers=ec_zhuli_header)
        # 获取用户的preorder
        QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_zhuli_header)
        assert self.response["result"] is True
        assert CommonCheck.list_check(['token', 'deal_id', 'items'], self.response['object'].keys())
        porder = self.response["object"]
        deal_date = porder["delivery_pickup_date"]
        zipcode = porder["zipcode"]

        # 清空购物车商品
        RemoveAllProductsInCart().remove_all_products_in_grocery_cart(headers=ec_zhuli_header)

        # 1. 通过分类接口获取零食商品
        normal = SearchByCatalogueContent().search_by_catalogue_content(
            headers=ec_zhuli_header,
            zipcode=zipcode,
            date=deal_date,
            sort="recommend",
            filter_sub_category="snack",
            filters=json.dumps({"delivery": True})
        )
        items = jmespath(normal, "object.contents")
        assert items, f"当前环境下没有category=snack的商品，接口返回为：{normal}，zipcode={zipcode}"
        product_list = []

        for item in items:
            # 通过分类获取type不是carousel并且商品状态不是change_other_day的商品
            if item['type'] != 'carousel' and item["data"]["sold_status"] == "available" and item['data']['is_pantry'] is False:
                product_list.append([item["data"]["id"], item['data']['min_order_quantity'], item['data']['max_order_quantity']])

        assert product_list, f'filter_sub_category=snack的商品不存在，product list为空，{product_list}'

        # 2. 加购商品，使其金额between（49， 68）
        fee = 0.0
        for product in product_list:
            UpdatePreOrderLine().porder_items_v3(
                headers=ec_zhuli_header,
                product_id=product[0],
                quantity=product[1]
            )

            assert self.response['result'] is True and self.response['object'][
                'updateItems'] != [], f"加购失败，self.response is {self.response}"

            CommCheckProductsWithCart().check_product_exists_in_cart(
                headers=ec_zhuli_header,
                cart_domain='grocery',
                product_id=product[0])

            cart_content = QueryPreOrder().query_preorder_v5(headers=ec_zhuli_header)
            # 计算商品金额，不包含运费，必须大于49 and < 68
            fee = [float(section['fee_info']['sub_total_price']) for section in
                   cart_content['object']["sections"]]
            if 49 <= sum(fee) < 68:
                break
        try:
            assert 49 <= sum(
                fee) < 68, f"第一次加购的商品金额不满足49<sum(fee)<68，不满足计算运费条件，购物车总金额为{sum(fee)}"
        except Exception as e:
            # 如果商品金额不满足上述关系，再加购1次
            if sum(fee) < 49:
                for product in reversed(product_list):
                    UpdatePreOrderLine().porder_items_v3(
                        headers=ec_zhuli_header,
                        product_id=product[0],
                        quantity=product[1] * 2
                    )

                    assert self.response['result'] is True and self.response['object'][
                        'updateItems'] != [], f"加购失败，self.response is {self.response}"

                    CommCheckProductsWithCart().check_product_exists_in_cart(
                        headers=ec_zhuli_header,
                        cart_domain='grocery',
                        product_id=product[0])

                    cart_content = QueryPreOrder().query_preorder_v5(headers=ec_zhuli_header)
                    # # 计算商品金额，不包含运费，必须大于49 and < 68
                    fee = [float(section['fee_info']['sub_total_price']) for section in
                           cart_content['object']["sections"]]
                    if 49 <= sum(fee) < 68:
                        break
            else:
                pytest.skip("加购的金额不满足条件")
        assert 49 < sum(
            fee) <68, f"第二次加购的商品金额不满足49<sum(fee)<68，不满足计算运费条件，购物车总金额为{sum(fee)}"

        # 加购满35不满49验证运费--金牌用户免运费，银牌用户5.95， 铜牌用户6.95
        # 查询购物车v5
        if 49 <= sum(fee) <68:
            cart = QueryPreOrder().query_preorder_v5(headers=ec_zhuli_header)
            offer_contents = [item.get('offer_content') for section in cart.get('object').get('sections') for item in section.get('activity_info') if section.get('activity_info')]
            assert any("to unlock extra deals" in j for j in offer_contents)

            trade_in_grocery = ActivityTradeIn().activity_trade_in(
                headers=ec_zhuli_header,
                type='normal',
                deal_id=porder["deal_id"],
                delivery_sale_org_id=4
            )
            assert trade_in_grocery['object']['products']
        else:
            pytest.skip("加购金额不达标，此用例跳过")
