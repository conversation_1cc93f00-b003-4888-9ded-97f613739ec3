"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :  test_PC_mini_cart.py
@Description    :  
@CreateTime     :  2024/7/22 18:26
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2024/7/22 18:26
"""
"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :  test_add_to_cart_low35.py.py
@Description    :  
@CreateTime     :  2023/8/12 17:40
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/8/12 17:40
"""
import json

import weeeTest
from weeeTest import jmespath

from test_dir.api.ec.ec_item.search_v3.search_by_catalogue_content import SearchByCatalogueContent
from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder
from test_dir.api.ec.ec_so.preorder.update_preorderline import UpdatePreOrderLine
from test_dir.api_case.ec.common.common_check import CommonCheck
from test_dir.api_case.ec.common.remove_all_products_in_cart import RemoveAllProductsInCart
from test_dir.api_case.ec.common.set_user_porder import SetUserPorder


class TestPcMiniCart(weeeTest.TestCase):
    @weeeTest.data.file(file_name='ec_so_data.json', return_type='dict')
    @weeeTest.mark.list('Regression', 'Transaction', 'smoke')
    def test_PC_mini_cart(self, *args, ec_zhuli_header):
        """查询PC端小购物车109702PC首页右上角小购物车-小购物车的交互"""
        "已标注"
        # 获取用户的porder
        SetUserPorder().set_user_porder(headers=ec_zhuli_header)
        # 获取用户的preorder
        QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_zhuli_header)
        assert self.response["result"] is True
        assert CommonCheck.list_check(['token', 'deal_id', 'items'], self.response['object'].keys())
        porder = self.response["object"]
        deal_date = porder["delivery_pickup_date"]
        zipcode = porder["zipcode"]

        RemoveAllProductsInCart().remove_all_products_in_grocery_cart(headers=ec_zhuli_header)
        # 加购不满35验证收取运费
        # 1. 通过分类接口获取零食商品
        normal = SearchByCatalogueContent().search_by_catalogue_content(
            headers=ec_zhuli_header,
            zipcode=zipcode,
            date=deal_date,
            sort="recommend",
            filter_sub_category="snack",
            filters=json.dumps({"delivery": True})
        )
        items = jmespath(normal, "object.contents")
        product_list = []
        for item in items:
            # 通过分类获取type不是carousel并且商品状态不是change_other_day的商品
            if item['type'] != 'carousel' and item["data"]["sold_status"] == "available" and item["data"]['price'] < 35:
                product_list.append([item["data"]["id"], item['data']['min_order_quantity']])

        if not product_list:
            # 线上环境运行，数据一般会存在，此分支基本不会运行
            print("没有可售的生鲜商品")
            UpdatePreOrderLine().porder_items_v3(
                headers=ec_zhuli_header,
                product_id=args[0]["cart"]["product_id"],
                quantity=args[0]["cart"]["quantity"]
            )
            # 判断加购成功
            assert self.response["result"] is True and len(
                self.response["object"]["updateItems"]) > 0, f"product id is"

        else:
            # 2. 加购零食分类返回的第一个有效商品, 经过上面过滤，价格一定小于35
            UpdatePreOrderLine().porder_items_v3(
                headers=ec_zhuli_header,
                product_id=product_list[0][0],
                quantity=product_list[0][1]
            )
            # 判断加购成功
            assert self.response["result"] is True and len(
                self.response["object"][
                    "updateItems"]) > 0, f"product id is {product_list[0][0]}, response is {self.response}"
        # 查询购物车v5
        cart = QuerySimplePreOrder().query_simple_preorder_v2(headers=ec_zhuli_header)
        # print("小购物车商品名称", cart["object"]["sections"][0]["items"])
        assert cart["object"]["sections"][0]["type"] in ["normal"]

        # final_amount = cart["object"]["final_amount"]
        # CommCheckProductsWithCart().check_product_exists_in_cart(
        #     headers=RequestHeader.ec_zhuli_header,
        #     cart_domain="grocery",
        #     product_id=product_list[0][0],
        #     final_amount=final_amount
        # )
        # # 3. 判断运费一定存在于[4.95, 5.95, 6.95]
        # # 判断用户等级，不用等级运费不同
        # AccountRest().get_account_rewards_level(headers=RequestHeader.ec_zhuli_header)
        # account_level = self.response['object']['current_level_label']
        # assert account_level in ['Gold', 'Silver', 'Bronze'], f"用户等级不在'Gold', 'Silver', 'Bronze'中，用户等级为：{account_level}"
        # # 获取当前购物车运费
        # real_shipping_fee = [float(section['fee_info']['shipping_fee']) for section in cart['object']['sections']]
        # # 获取当前购物车商品金额，不包含运费，只有一个生鲜购物车
        # current_product_amount = CommonCheck.compute_total_price_in_cart_not_include_freight(cart['object']['sections'])
        # expect_fee = CommonCheck.check_shipping_fee(account_level, current_product_amount)
        # # 根据上面的加购商品，一定是生鲜商品，所以不存在商品在多个购物车的情况，所以运费就是一个生鲜购物车的运费
        # # 如果存在多个购物车，这样计算【期望运费】是错误的
        # assert expect_fee == sum(real_shipping_fee), f"用户加购小于35但运费不正确, 期望运费为{expect_fee}, 实际运费为：{real_shipping_fee}，用户等级为{account_level}"


