"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :  test_add_to_cart_low35.py.py
@Description    :  
@CreateTime     :  2023/8/12 17:40
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/8/12 17:40
"""
import copy
import datetime
import json
import os

import weeeTest

from test_dir.api.ec.ec_customer.account_rest.account_rest import AccountRest
from test_dir.api.ec.ec_item.product.pdp_detail import PdpDetail
from test_dir.api.ec.ec_item.search_v3.search_by_catalogue_content import SearchByCatalogueContent
from test_dir.api.ec.ec_so.order.cancel_order import CancelOrder
from test_dir.api.ec.ec_so.order.prepare_checkout import PrepareCheckout
from test_dir.api.ec.ec_so.preorder.query_preorder import QueryPreOrder
from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder
from test_dir.api.ec.ec_so.preorder.update_preorderline import UpdatePreOrderLine
from test_dir.api.ec.ec_so.preorder.update_zipcode import UpdateZipcode
from test_dir.api_case.ec.common.common_payment import CommonPayment
from test_dir.api_case.ec.common.common_check import CommonCheck
from test_dir.api_case.ec.common.get_root_dir import get_project_dir
from test_dir.api_case.ec.common.remove_all_products_in_cart import RemoveAllProductsInCart
from test_dir.api_case.ec.common.comm_check_products_with_cart import CommCheckProductsWithCart


class TestDeliveryFee(weeeTest.TestCase):
    @weeeTest.mark.list('Transaction', 'Smoke')
    @weeeTest.mark.skipif(os.getenv("is_Thursday") == "True", reason="周四不执行")
    def test_delivery_fee_for_crv_product(self, ec_zhuli_header):
        """购物车加购76702（含crv的商品）点击结算按钮"""
        # 获取用户的porder
        with open(get_project_dir() + "/test_data/lizhu2_token.json", 'r', encoding='utf-8') as f:
            crv_header = json.load(f)
        crv_header['Zipcode'] = "94538"
        crv_header['Weee-Zipcode'] = "94538"

        # 2. 更新用户zipcode
        UpdateZipcode().update_zipcode_v3(crv_header, zipcode="94538")

        # 获取用户的preorder
        QuerySimplePreOrder().query_simple_preorder_v1(headers=crv_header)
        assert self.response["result"] is True
        assert CommonCheck.list_check(['token', 'deal_id', 'items'], self.response['object'].keys())
        porder = self.response["object"]
        deal_date = porder["delivery_pickup_date"]
        zipcode = porder["zipcode"]

        RemoveAllProductsInCart().remove_all_products_in_grocery_cart(headers=crv_header)

        PdpDetail().pdp_detail(headers=crv_header, product_id="76702", sales_org_id=1, zipcode=94538)
        assert self.response['object']['product']['sold_status'] == 'available', f"response={self.response}"

        # 2. 加购76702-可口可乐
        UpdatePreOrderLine().porder_items_v3(
            headers=crv_header,
            product_id=76702,
            quantity=4
        )
        # 判断加购成功
        assert self.response["result"] is True and len(
            self.response["object"]["updateItems"]) > 0, f"product id is 76702, response is {self.response}"
        # 查询购物车v5
        cart = QueryPreOrder().query_preorder_v5(headers=crv_header)
        final_amount = cart["object"]["final_amount"]
        CommCheckProductsWithCart().check_product_exists_in_cart(
            headers=crv_header,
            cart_domain="grocery",
            product_id=76702,
            final_amount=final_amount
        )
        # 3. 判断运费一定存在于[4.95, 5.95, 6.95]
        # 判断用户等级，不用等级运费不同
        AccountRest().get_account_rewards_level(headers=crv_header)
        account_level = self.response['object']['current_level_label']
        assert account_level in ['Gold', 'Silver',
                                 'Bronze'], f"用户等级不在'Gold', 'Silver', 'Bronze'中，用户等级为：{account_level}"
        # 获取当前购物车运费
        real_shipping_fee = [float(section['fee_info']['shipping_fee']) for section in cart['object']['sections']]
        # 获取当前购物车商品金额，不包含运费，只有一个生鲜购物车
        current_product_amount = CommonCheck.compute_total_price_in_cart_not_include_freight(cart['object']['sections'])
        expect_fee = CommonCheck.check_shipping_fee(account_level, current_product_amount)
        # 根据上面的加购商品，一定是生鲜商品，所以不存在商品在多个购物车的情况，所以运费就是一个生鲜购物车的运费
        # 如果存在多个购物车，这样计算【期望运费】是错误的
        assert expect_fee == sum(
            real_shipping_fee), f"用户加购小于35但运费不正确, 期望运费为{expect_fee}, 实际运费为：{real_shipping_fee}，用户等级为{account_level}"

        # 4. 计算瓶子回收费和taxi
        pre = PrepareCheckout().prepare_checkout_v2_marketplace(headers=crv_header)
        # 4.1 回收费 （还需要与朱莉讨论）
        assert any([item['title']['tag_text'] == 'Beverage container fee' for item in
                    pre['object']['order_summary']["order_summary_options"]])
        assert any(
            [item['amount']['tag_text'] == '$0.20' for item in pre['object']['order_summary']["order_summary_options"]])
        # 如果该用户90天没有购买，视为新用户，服务费和税费跟非新用户不同
        # 4.2 税费 (计算较为复杂，需要与朱莉讨论)
        # assert pre['object']['fee_info']['tax'] == '1.64' or pre['object']['fee_info']['tax'] == '1.43' or pre['object']['fee_info']['tax'] == '1.56'
        assert pre['object']['fee_info']['tax'] in ['1.64', '1.43', '1.56', '1.85', '1.88', '1.67']
        # 4.3 服务费
        assert pre['object']['fee_info']['service_fee'] == '2.95' or pre['object']['fee_info']['service_fee'] == '0.00'

        UpdateZipcode().update_zipcode_v3(crv_header, zipcode="98011")
        crv_header['Zipcode'] = "98011"
        crv_header['Weee-Zipcode'] = "98011"

    @weeeTest.mark.list('Transaction', 'Smoke')
    def test_delivery_fee_for_crv_and_fbw(self, ec_zhuli_header):
        """加购fbw+收crv的商品一起结算"""
        # 获取用户的porder
        with open(get_project_dir() + "/test_data/lizhu2_token.json", 'r', encoding='utf-8') as f:
            crv_header = json.load(f)
        crv_header['Weee-Zipcode'] = "94538"
        crv_header['Zipcode'] = "94538"

        # 更新用户zipcode
        UpdateZipcode().update_zipcode_v3(crv_header, zipcode="94538")

        # 测试时发现li.zhu2这个账户默认的picup_data是当天，导致查询的所有商品都是change_other_day状态
        # PorderDate().porder_date(headers=crv_header, delivery_pickup_date=(datetime.datetime.now() + datetime.timedelta(days=2)).strftime("%Y-%m-%d"))
        # 获取用户的preorder
        QuerySimplePreOrder().query_simple_preorder_v1(headers=crv_header)
        assert self.response["result"] is True
        assert CommonCheck.list_check(['token', 'deal_id', 'items'], self.response['object'].keys())
        porder = self.response["object"]
        deal_date = porder["delivery_pickup_date"]
        zipcode = porder["zipcode"]

        RemoveAllProductsInCart().remove_all_products_in_grocery_cart(headers=crv_header)

        bakery = SearchByCatalogueContent().search_by_catalogue_content(
            headers=crv_header,
            zipcode=zipcode,
            # date=porder["delivery_pickup_date"],
            date=(datetime.datetime.now() + datetime.timedelta(days=2)).strftime("%Y-%m-%d"),
            filter_sub_category="bakery",
            filters=json.dumps({"catalogue_num": "bakery06"})
        )
        bakery_products = bakery['object']['contents']
        assert bakery_products, f"当前环境下没有bakery商品，bakery_products={bakery_products}"
        available_bakery_products = [[item['data']['id'], item['data']['min_order_quantity']] for item in
                                     bakery_products if
                                     item['type'] == 'product' and item['data']['sold_status'] == 'available']
        assert available_bakery_products, f"当前环境下没有可用的bakery商品，available_bakery_products={available_bakery_products}"
        # 1. 加购bakery商品
        UpdatePreOrderLine().porder_items_v3(
            headers=crv_header,
            product_id=available_bakery_products[0][0],
            quantity=available_bakery_products[0][1]
        )

        # 2. 加购76702-可口可乐
        UpdatePreOrderLine().porder_items_v3(
            headers=crv_header,
            product_id=76702,
            quantity=4
        )
        # 判断加购成功
        assert self.response["result"] is True and len(
            self.response["object"]["updateItems"]) > 0, f"product id is 76702, response is {self.response}"
        UpdateZipcode().update_zipcode_v3(crv_header, zipcode="98011")
        crv_header['Zipcode'] = "98011"
        crv_header['Weee-Zipcode'] = "98011"

        # 3. 怎么断言？

    @weeeTest.mark.list('Transaction', 'Smoke')
    def test_buy_crv_use_paypal_payment(self, ec_zhuli_header):
        """结算页选择paypal未支付成功跳到待支付"""
        # 获取用户的porder
        with open(get_project_dir() + "/test_data/lizhu2_token.json", 'r', encoding='utf-8') as f:
            crv_header = json.load(f)
        crv_header['Weee-Zipcode'] = "94538"
        crv_header['Zipcode'] = "94538"

        # 更新用户zipcode
        UpdateZipcode().update_zipcode_v3(crv_header, zipcode="94538")

        # 获取用户的preorder
        QuerySimplePreOrder().query_simple_preorder_v1(headers=crv_header)
        assert self.response["result"] is True
        assert CommonCheck.list_check(['token', 'deal_id', 'items'], self.response['object'].keys())
        porder = self.response["object"]
        deal_date = porder["delivery_pickup_date"]
        zipcode = porder["zipcode"]

        RemoveAllProductsInCart().remove_all_products_in_grocery_cart(headers=crv_header)

        detail = PdpDetail().pdp_detail(headers=crv_header, product_id="76702", sales_org_id=1, zipcode=94538)
        assert detail['object']['product']['sold_status'] == 'available', f"response={self.response}"

        # 2. 加购76702-可口可乐
        UpdatePreOrderLine().porder_items_v3(
            headers=crv_header,
            product_id=76702,
            quantity=4
        )
        # 判断加购成功
        assert self.response["result"] is True and len(
            self.response["object"]["updateItems"]) > 0, f"product id is 76702, response is {self.response}"

        pre = PrepareCheckout().prepare_checkout_v2_marketplace(headers=crv_header)
        assert pre['object'], f"预结算接口返回不正确，pre={pre}"
        # 用例结果预期
        # 订单总结下面的税更新成tax$fees字段显示i按钮，点击弹出当前crv费用详情
        assert any([item['track_title'] == 'taxes' and item['amount']['tag_text'].startswith('$') for item in pre['object']['order_summary']['order_summary_options']])

        # 使用Paypal支付，产生一个待支付的订单
        order_info = CommonPayment.pay_with_paypal(crv_header)
        order_ids = order_info["order_ids"]
        assert order_ids, f"使用paypal支付，未产生订单号，接口返回结果为：{order_ids}"
        # 取消待支付订单
        CancelOrder().cancel_unpaid_order_new(headers=crv_header, order_ids=order_ids)
        assert self.response['object'] == 'success'

        UpdateZipcode().update_zipcode_v3(crv_header, zipcode="98011")
        crv_header['Zipcode'] = "98011"
        crv_header['Weee-Zipcode'] = "98011"


    def test_bought_exclude_crv(self, ec_zhuli_header):
        # 获取用户的porder
        with open(get_project_dir() + "/test_data/lizhu2_token.json", 'r', encoding='utf-8') as f:
            crv_header = json.load(f)
        crv_header['Weee-Zipcode'] = "94538"
        crv_header['Zipcode'] = "94538"

        # 更新用户zipcode
        UpdateZipcode().update_zipcode_v3(crv_header, zipcode="94538")

        # 获取用户的preorder
        QuerySimplePreOrder().query_simple_preorder_v1(headers=crv_header)
        assert self.response["result"] is True
        assert CommonCheck.list_check(['token', 'deal_id', 'items'], self.response['object'].keys())
        porder = self.response["object"]

        # 查找不含crv的商品
        UpdateZipcode().update_zipcode_v3(crv_header, zipcode="98011")
        crv_header['Zipcode'] = "98011"
        crv_header['Weee-Zipcode'] = "98011"


