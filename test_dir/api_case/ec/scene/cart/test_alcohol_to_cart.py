"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :  test_save_for_later_cart.py
@Description    :
@CreateTime     :  2023/9/13 13:07
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/9/13 13:07
"""
import pytest
import weeeTest

from test_dir.api.ec.ec_item.search_v3.search_by_catalogue_content import SearchByCatalogueContent
from test_dir.api.ec.ec_so.order.cancel_order import CancelOrder
from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder
from test_dir.api.ec.ec_so.preorder.update_preorderline import UpdatePreOrderLine
from test_dir.api.ec.ec_so.preorder.update_zipcode import UpdateZipcode
from test_dir.api_case.ec.common.comm_check_products_with_cart import CommCheckProductsWithCart
from test_dir.api_case.ec.common.common_buy_products import CommonBuy
from test_dir.api_case.ec.common.common_payment import CommonPayment
from test_dir.api_case.ec.common.remove_all_products_in_cart import RemoveAllProductsInCart


class TestAlcoholToCart(weeeTest.TestCase):
    def _search_alcohol_products(self, header):
        UpdateZipcode().update_zipcode_v3(header)
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=header)["object"]
        # 清空购物车
        RemoveAllProductsInCart().remove_all_products_in_grocery_cart(headers=header)
        # 1. 通过分类接口获取酒分类的商品
        alcohol = SearchByCatalogueContent().search_by_catalogue_content(
            headers=header,
            zipcode=porder["zipcode"],
            date=porder["delivery_pickup_date"],
            filter_sub_category="alcohol"
        )
        assert alcohol["object"]["contents"], f"当前环境下没有酒类商品，请检查，接口返回的结果为{alcohol['object']}"
        return alcohol


    @weeeTest.mark.list('Regression', 'Transaction', 'product', 'Smoke', 'alcohol')
    def test_alcohol_to_cart(self, ec_login_header):
        """购物车-添加酒商品到购物车--110190-test_alcohol_to_cart"""
        "已标注"
        # 获取登录header
        # 获取用户的preorder
        alcohol = self._search_alcohol_products(ec_login_header)
        available_alcohol_products = [[item['data']['id'], item['data']['sales_min_order_quantity']] for item in
                                      alcohol["object"]["contents"] if item['type'] != 'carousel' and item["data"]["sold_status"] == "available"]

        # 2. 加购酒分类的第一个商品
        UpdatePreOrderLine().porder_items_v3(
            headers=ec_login_header,
            product_id=available_alcohol_products[0][0],
            quantity=available_alcohol_products[0][1]
        )
        # 判断加购成功
        assert self.response["result"] is True and len(
            self.response["object"]["updateItems"]) > 0, f"product id is {available_alcohol_products[0][0]}"
        CommCheckProductsWithCart().check_product_exists_in_cart(
            headers=ec_login_header,
            cart_domain="grocery",
            product_id=available_alcohol_products[0][0]
        )
        # 3. 添加upsell商品 (upsell商品不是每次都有)
        CommonBuy.buy_upsell_product(ec_login_header)

        # 4. 使用Paypal支付，产生一个待支付的订单
        order_info = CommonPayment.pay_with_paypal(ec_login_header)
        order_ids = order_info["order_ids"]
        assert order_ids, f"使用paypal支付，未产生订单号，接口返回结果为：{order_ids}"
        # 取消待支付订单
        CancelOrder().cancel_unpaid_order_new(headers=ec_login_header, order_ids=order_ids)
        assert self.response['object'] == 'success'

    @pytest.mark.repeat(100)
    def test_test(self, ec_login_header):
        """购物车-添加酒商品到购物车"""
        # 获取登录header
        # 获取用户的preorder
        UpdateZipcode().update_zipcode_v3(ec_login_header)
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]
        # 清空购物车
        RemoveAllProductsInCart().remove_all_products_in_grocery_cart(headers=ec_login_header)
        # 1. 通过分类接口获取酒分类的商品
        alcohol = SearchByCatalogueContent().search_by_catalogue_content(
            headers=ec_login_header,
            zipcode=porder["zipcode"],
            date=porder["delivery_pickup_date"],
            filter_sub_category="alcohol"
        )
        assert alcohol["object"]["contents"], f"当前环境下没有酒类商品，请检查，接口返回的结果为{alcohol['object']}"

