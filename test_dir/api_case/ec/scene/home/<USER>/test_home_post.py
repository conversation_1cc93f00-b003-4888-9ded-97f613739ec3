# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_user.py
@Description    :
@CreateTime     :  2023/5/16 15:23
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 15:23
"""

import weeeTest

from test_dir.api.ec.ec_item.product.pdp_detail import PdpDetail
from test_dir.api.ec.ec_social.review_info.review_info import ReviewInfo
from test_dir.api_case.ec.common.common_check import CommonCheck


class TestHomePost(weeeTest.TestCase):
    @weeeTest.mark.list('test_home_post', 'Regression', 'Smoke', 'Transaction')
    def test_home_post(self, ec_login_header):
        """ 首页晒单验证流程 """

        # 获取首页\PC Account页猜你喜欢数据
        review_home = ReviewInfo().social_review_list(headers=ec_login_header)
        # 断言猜你喜欢必须有数据
        assert len(review_home["object"]["categories"]) > 0, f'PC首页晒单数据返回异常{review_home}，请确认'
        assert len(review_home["object"]["list"]) > 0, f'PC首页晒单数据返回异常{review_home}，请确认'
        for index, item in enumerate(review_home["object"]["list"]):
            if item["product"]["biz_type"] == "normal":
                CommonCheck().check_social_review_list(review_list=item, source="home", headers=ec_login_header)
            if index == 5:
                break

    @weeeTest.mark.list('Regression')
    def test_home_post_goto_pdp(self, ec_login_header):
        """
        【112273】 PC首页点击晒单能正常跳转到pdp页
        """
        # 1. 获取首页的feature reviews
        home_review = ReviewInfo().social_review_list(headers=ec_login_header)
        assert home_review.get('object').get('list'), f"当前首页没有feature reviews数据，home_review={home_review}"
        home_review_comments = home_review.get('object').get('list')[0].get("comment")
        comment_id = home_review.get('object').get('list')[0].get("id")
        product_id = home_review.get('object').get('list')[0].get("product_id")
        pdp_review = ReviewInfo().query_review_list_for_pdp(
            headers=ec_login_header,
            data={
                "product_id": product_id,
                "mid": comment_id,
                "sort": "relevance",
                "page": 1,
                "limit": 10
            }
        )
        assert pdp_review.get('object').get('list'), f"php review没有comments, pdp_review={pdp_review}"
        assert home_review_comments == pdp_review.get('object').get('list')[0].get("comment")
        detail = PdpDetail().pdp_detail(headers=ec_login_header, product_id=product_id)
        assert detail.get('object').get('product').get("id") == product_id and detail.get(
            "result"), f"获取商品{product_id}的详情失败，detail={detail}"
