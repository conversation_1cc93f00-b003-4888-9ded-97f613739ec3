# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_user.py
@Description    :
@CreateTime     :  2023/5/16 15:23
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 15:23
"""
import json
from typing import Any

import weeeTest
from weeeTest import jmespath

from test_dir.api.ec.ec_item.cuisine.api_cuisine import ApiCuisine
from test_dir.api.ec.ec_item.search_v2.search_by_cuisine import SearchByCuisine
from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder
from test_dir.api_case.ec.common.comm_check_products_with_cart import CommCheckProductsWithCart
from test_dir.api_case.ec.common.comm_cms import CommCheckFunction


class TestCuisine(weeeTest.TestCase):
    @weeeTest.mark.list('cuisine_list','Regression', 'Transaction')
    def test_home_cuisine(self, ec_login_header):
        """ 首页-异域美食验证流程 """
        # headers = Header().login_header()
        # 获取用户的porder
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]
        # 获取首页cuisine，返回有哪些cuisine
        cuisine = ApiCuisine().get_cuisine_list_home_page(headers=ec_login_header,
                                                          zipcode=porder["zipcode"], lang="en",
                                                          date=porder["delivery_pickup_date"])
        assert cuisine["object"]["total_count"] > 0
        cuisine_list = jmespath(cuisine, "object.cuisine_list")
        self.home_cuisine_assertion(cuisine_list)

    @weeeTest.mark.list('cuisine_list','Regression', 'Transaction')
    def test_cuisine_list(self, ec_login_header):
        """ Cuisine页-异域美食验证流程 """
        # 获取用户的porder
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]

        # 获取首页cuisine，返回有哪些cuisine
        cuisine = ApiCuisine().get_cuisine_list_home_page(headers=ec_login_header,
                                                          zipcode=porder["zipcode"], lang="en",
                                                          date=porder["delivery_pickup_date"])
        assert cuisine["object"]["total_count"] > 0
        cuisine_list = jmespath(cuisine, "object.cuisine_list")
        self.home_cuisine_assertion(cuisine_list)
        # 获取cuisine分类数据
        category_list = ApiCuisine().get_cuisine_category_list(headers=ec_login_header)
        assert len(category_list["object"]["category_list"]) > 0,f'cuisine分类没有数据返回{category_list}，请确认'
        self.category_list_assertion(category_list["object"]["category_list"])
        # 根据访问japanese cuisine 进行操作
        cuisine_filter = SearchByCuisine().search_by_cuisine_filter(headers=ec_login_header,
                                                                    filterCuisine="japanese", lang="en",
                                                                    zipcode=porder["zipcode"],
                                                                    date=porder["delivery_pickup_date"])
        self.cuisine_product_assertion(products=cuisine_filter["object"]["products"], headers=ec_login_header)
        # 切换japanese cuisine 下的子分类
        self.cuisine_sub_filter_assertion(filterCuisine="japanese",
                                          category_list=cuisine_filter["object"]["categories"],
                                          zipcode=porder["zipcode"],
                                          date=porder["delivery_pickup_date"],
                                          headers=ec_login_header
                                          )

        # 加购搜索结果里的商品及断言加购成功
        CommCheckProductsWithCart().products_add_to_cart(ec_login_header,
                                                         cuisine_filter["object"]["products"],
                                                         porder["delivery_pickup_date"], "cuisines-japanese-all")
        # 排序断言
        self.cuisine_sorts_product_assertion(filterCuisine="japanese", zipcode=porder["zipcode"],
                                             sorts=cuisine_filter["object"]["sorts"],
                                             date=porder["delivery_pickup_date"],
                                             headers=ec_login_header)

        # 根据cuisine循环切换进行访问
        self.cuisine_filter_assertion(date=porder["delivery_pickup_date"], zipcode=porder["zipcode"],
                                      category_list=category_list["object"]["category_list"],
                                      headers=ec_login_header)

    def home_cuisine_assertion(self, cuisine_list: list | Any):
        for cuisine in cuisine_list:
            assert cuisine['cuisine_img'] is not None
            assert cuisine['cuisine_key'] is not None
            assert cuisine['cuisine_title'] is not None

    def category_list_assertion(self, category_list: list | Any):
        for cuisine in category_list:
            assert cuisine['key'] is not None
            assert cuisine['name'] is not None
            assert cuisine['img_url'] is not None
            tag_list = cuisine['tag_list']
            assert len(tag_list) > 0
            return tag_list

    def cuisine_product_assertion(self, products: list | Any, headers):
        # 对返回结果商品操作及断言
        for index, product in enumerate(products):
            assert "weeecdn" in product['img']
            assert product['name'] is not None
            assert product['price'] is not None
            assert str(product["id"]) in product['slug'], "这个链接返回的不是这个产品的链接，请确认~"
            # 点击进PDP
            CommCheckFunction().comm_check_pdp_link(product["id"], product["view_link"], headers=headers)
            # # 产品标签tag
            # if product["entrance_tag"] is not None:
            #     more_link = product["entrance_tag"]["more_link"]
            #     assert product["entrance_tag"]['tag_name'] is not None
            #     # 验证点击more_link 跳转正常
            #     CommCheckFunction().comm_check_link(more_link)
            if index == 3:
                break

    def cuisine_filter_assertion(self, date, zipcode, category_list: list | Any, headers):
        # 根据cuisine循环切换进行访问
        for category in category_list:
            # 根据cuisine循环进行访问（没有选任何过滤）
            cuisine_filter = SearchByCuisine().search_by_cuisine_filter(headers=headers,
                                                                        filterCuisine=category["key"], lang="en",
                                                                        zipcode=zipcode,
                                                                        date=date)
            # 断言cuisine必有产品
            assert cuisine_filter["object"] is not None

    def cuisine_sub_filter_assertion(self, date, zipcode, filterCuisine, category_list: list | Any, headers):
        # 根据cuisine循环切换子分类进行访问
        for categorie in category_list:
            # 根据cuisine循环进行访问（没有选任何过滤）
            cuisine_filter = SearchByCuisine().search_by_cuisine_filter(headers=headers,
                                                                        filterCuisine=filterCuisine, lang="en",
                                                                        zipcode=zipcode,
                                                                        date=date,
                                                                        filters=json.dumps({"catalogue_num": categorie[
                                                                            "catalogue_num"]})
                                                                        )
            # 断言cuisine必有产品
            assert cuisine_filter["object"] is not None

    def cuisine_sorts_product_assertion(self, filterCuisine, zipcode, date, sorts: list | Any, headers):
        # 对返回结果sorts 排序操作及断言
        for sort in sorts:
            # 过滤sorts
            cuisine_filter = SearchByCuisine().search_by_cuisine_filter(headers=headers,
                                                                        filterCuisine=filterCuisine, lang="en",
                                                                        zipcode=zipcode,
                                                                        date=date,
                                                                        sort=sort["sort_key"])
            assert cuisine_filter["object"] is not None
            products = cuisine_filter["object"]["products"]
            sort_1 = products[0]["price"]
            sort_2 = products[-1]["price"]
            if sort["sort_key"] == "price_asc":
                # 断言商品"价格：低到高"
                assert sort_1 <= sort_2, f"商品价格：低到高 排序不对：{sort_1}<{sort_2}"
            if sort["sort_key"] == "price_desc":
                # print(products)
                # 断言商品"价格：高到低"
                assert sort_1 >= sort_2, f"商品价格：高到低排序不对：{sort_1}>{sort_2}"


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net', env='tb1')
