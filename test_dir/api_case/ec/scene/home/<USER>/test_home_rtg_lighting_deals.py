# # !/usr/bin/python3
# # -*- coding: utf-8 -*-
# """
# <AUTHOR>  <PERSON><PERSON><PERSON> <PERSON>
# @Version        :  V1.0.0
# ------------------------------------
# @File           :  test_user.py
# @Description    :
# @CreateTime     :  2023/5/16 15:23
# @Software       :  PyCharm
# ------------------------------------
# @ModifyTime     :  2023/5/16 15:23
# """
#
# import requests
# import weeeTest
# from weeeTest import RequestHeader
#
# from test_dir.api.ec.ec_mkt.activity.lighting_deals_v1 import LightingDeals
# from test_dir.api_case.ec.common.comm_cms import CommCheckFunction
# from test_dir.api_case.ec.common.set_user_porder import SetUserPorder
#
#
# class TestHomeRtgLightingDeals(weeeTest.TestCase):
#
#     @weeeTest.data.file(file_name='ec_item_data.json', return_type='dict')
#     @weeeTest.mark.skip("rtg 业务下线，移除此case")
#     def test_home_rtg_lighting_deals(self, *args):
#         """ 秒杀-首页餐馆菜秒杀相关验证流程 """
#         # 清除生鲜购物车
#         # RemoveAllProductsInCart().remove_all_products_in_grocery_cart(headers=headers)
#         # 获取用户的porder
#         # 切换到98011 下有餐馆菜秒杀
#         porder = SetUserPorder().set_user_new_porder(headers=RequestHeader.ec_login_header,
#                                                      zipcode="98011", lang="zh")
#         sales_org_id = porder["sales_org_id"]
#         print(sales_org_id)
#         deal_date = porder["delivery_pickup_date"]
#         delivery_date = porder["delivery_pickup_date"]
#         zipcode = porder["zipcode"]
#         data = args[0]["category"]["search_by_catalogue"]
#         # RTG秒杀商品列表(首页)
#         rtg_lighting = LightingDeals().carousels_lighting(headers=RequestHeader.ec_login_header, date=delivery_date)
#         assert rtg_lighting["object"] is not None
#         # 如果首页存在rtg秒杀
#         if rtg_lighting["object"]["total_count"]:
#             compon = rtg_lighting["object"]["component_metadata"]
#             products = rtg_lighting["object"]["products"]
#             # 首页查看全部按钮
#             more_link = compon["more_link"]
#             # 点击跳转
#             CommCheckFunction().comm_check_link(more_link)
#             for product in products:
#                 # 点击秒杀
#                 view_link = product["view_link"]
#                 # 点击跳转
#                 CommCheckFunction().comm_check_link(view_link)
#
#                 break
#         else:
#             print("没有配置秒杀，请先配置")
#
#
# if __name__ == '__main__':
#     weeeTest.main(base_url='https://api.tb1.sayweee.net', env='tb1')
