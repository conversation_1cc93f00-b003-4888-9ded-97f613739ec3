# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_user.py
@Description    :
@CreateTime     :  2023/5/16 15:23
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 15:23
"""
from typing import Any

import weeeTest

from test_dir.api.ec.ec_item.ds_collection_rest.ds_collection_home import DsCollectionHome
from test_dir.api.ec.ec_so.preorder.create_preorder import CreatePreOrder
from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder
from test_dir.api_case.ec.common.comm_check_products_with_cart import CommCheckProductsWithCart
from test_dir.api_case.ec.common.comm_cms import CommCheckFunction
from test_dir.api_case.ec.common.common_check import CommonCheck


class TestHomeBasketStarter(weeeTest.TestCase):
    @weeeTest.mark.list('basket_starter', 'Regression', 'Smoke',  'Transaction')
    def test_home_basket_starter_anony_user(self, ec_anony_header):
        """ 推荐-首页basket_starter验证流程-匿名用户 """
        # 匿名用户之前要创建porder
        CreatePreOrder().create_preorder(headers=ec_anony_header)
        # 获取用户的porder
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_anony_header)["object"]
        # 匿名户也必须有basket_starter
        cms_collection = DsCollectionHome().ds_collection_home(headers=ec_anony_header,
                                                               scenes="basketstarter",
                                                               ds_url="cm_item_basket_starter"
                                                               )

        # 断言basket_starter必须有数据
        assert cms_collection["object"] is not None, f'basket_starter没有数据返回，{cms_collection["object"]}'
        assert cms_collection["object"]["total_count"] > 0, f'basket_starter商品没有返回，请确认'
        # basket_starter基础操作及断言
        # self.basket_product_assertion(cms_collection["object"])
        products = cms_collection["object"]["products"]
        # 对返回结果商品操作及断言
        for index, product in enumerate(products):
            CommonCheck().check_product_info(ec_anony_header, product, category_type="others", source="others", filters="others")

        # 加购basket_starter下的商品
        CommCheckProductsWithCart().products_add_to_cart(headers=ec_anony_header, products=products,
                                                         porder_deal_date=porder["delivery_pickup_date"],
                                                         product_source="mweb_home-cm_item_basket_starter-null")

    @weeeTest.mark.list('basket_starter',  'Transaction', 'tb1')
    def test_home_basket_starter_new_user(self, ec_signup_header):
        """ 推荐-首页basket_starter验证流程-新用户 """
        # signup_header = Header().signup_header
        # 获取用户的porder
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_signup_header)["object"]
        deal_date = porder["delivery_pickup_date"]
        # 新用户必须有basket_starter
        cms_collection = DsCollectionHome().ds_collection_home(headers=ec_signup_header,
                                                               scenes="basketstarter",
                                                               ds_url="cm_item_basket_starter"
                                                               )

        # 断言basket_starter必须有数据
        assert cms_collection["object"]["total_count"] > 0, f'basket_starter商品没有返回，请确认'
        # basket_starter基础操作及断言
        # self.basket_product_assertion(cms_collection["object"])
        products = cms_collection["object"]["products"]
        assert cms_collection["object"]['ds_collection_key'] == "cm_item_basket_starter"
        # 对返回结果商品操作及断言
        for index, product in enumerate(products):
            CommonCheck().check_product_info(ec_signup_header, product, category_type="others", source="others", filters="others")

        # 加购basket_starter下的商品
        CommCheckProductsWithCart().products_add_to_cart(headers=ec_signup_header, products=products,
                                                         porder_deal_date=porder["delivery_pickup_date"],
                                                         product_source="mweb_home-cm_item_basket_starter-null")

    @weeeTest.mark.list('basket_starter', 'Regression', 'Smoke',  'Transaction')
    def test_home_basket_starter_old_user(self, ec_login_header):
        """ 推荐-首页basket_starter验证流程-老用户 """
        # 老用户没有basket_starter
        cms_collection = DsCollectionHome().ds_collection_home(headers=ec_login_header,
                                                               scenes="basketstarter",
                                                               ds_url="cm_item_basket_starter")
        assert cms_collection["object"]["total_count"] > 0, f'basket_starter商品没有返回，请确认{cms_collection}'

        cms_collection = CommCheckFunction().comm_cms_component(headers=ec_login_header,
                                                                component_key="cm_item_line_v2",
                                                                component_instance_key="cm_item_basket_starter",
                                                                scenes="basketstarter")

        # 断言basket_starter没有数据
        assert cms_collection == "90000"

    def basket_product_assertion(self, collection: dict | Any):
        assert collection['ds_collection_key'] == "cm_item_basket_starter"
        # 对返回结果商品操作及断言
        for index, product in enumerate(collection["products"]):
            assert "weeecdn" in product['img']
            assert product['name'] is not None
            assert product['price'] is not None

            assert str(product["id"]) in product['slug'], "这个链接返回的不是这个产品的链接，请确认~"
            # 产品标签tag
            if product["entrance_tag"] is not None:
                more_link = product["entrance_tag"]["more_link"]
                assert product["entrance_tag"]['tag_name'] is not None
                # 验证点击more_link 跳转正常
                CommCheckFunction().comm_check_link(more_link)
            if index == 3:
                break
        # 点击商品卡片跳转pdp
        CommCheckFunction().comm_check_pdp_link(collection["products"][-1]["id"],
                                                collection["products"][-1]["view_link"])


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net', env='tb1')
