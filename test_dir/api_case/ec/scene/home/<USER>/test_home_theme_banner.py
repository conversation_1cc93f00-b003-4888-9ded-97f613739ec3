# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_user.py
@Description    :
@CreateTime     :  2023/5/16 15:23
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 15:23
"""
from typing import Any

import weeeTest
from weeeTest import log
from test_dir.api.ec.ec_mkt.banner.get_banner_list import GetBannerList
from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder
from test_dir.api_case.ec.common.comm_cms import CommCheckFunction


class TestHomeThemeBanner(weeeTest.TestCase):

    @weeeTest.mark.list('Regression',  'Transaction')
    def test_home_theme_banner(self, ec_login_header):
        """ Banner-首页主题 banner验证流程 """
        # 获取用户的porder
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]
        # 获取theme banner
        banner_list = GetBannerList().get_theme_banner(headers=ec_login_header,
                                                       zipcode=porder["zipcode"],
                                                       date=porder["delivery_pickup_date"])
        assert banner_list["result"] is True
        if banner_list["object"] is None:
            log.info("请确认是否有配置theme banner")
        else:
            self.theme_banner_assertion(banner_list["object"])

    def theme_banner_assertion(self, carousel: dict | Any):
        assert carousel['img_url'] is not None
        assert carousel['detail'] is not None
        assert carousel['type'] == "activity"
        # 断言有主题banner,并访问轮播图链接
        url = carousel['detail']
        # 点击跳转
        CommCheckFunction().comm_check_link(url)


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net', env='tb1')
