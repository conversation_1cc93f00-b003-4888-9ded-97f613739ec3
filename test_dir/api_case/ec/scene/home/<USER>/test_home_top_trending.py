# # !/usr/bin/python3
# # -*- coding: utf-8 -*-
# """
# <AUTHOR>  <PERSON><PERSON><PERSON> <PERSON>
# @Version        :  V1.0.0
# ------------------------------------
# @File           :  test_user.py
# @Description    :
# @CreateTime     :  2023/5/16 15:23
# @Software       :  PyCharm
# ------------------------------------
# @ModifyTime     :  2023/5/16 15:23
# """
# from typing import Any
#
# import weeeTest
#
# from test_dir.api.ec.ec_item.ds_collection_rest.ds_collection_home import DsCollectionHome
# from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder
# from test_dir.api_case.ec.common.comm_check_products_with_cart import CommCheckProductsWithCart
# from test_dir.api_case.ec.common.comm_cms import CommCheckFunction
#
#
# class TestHomeTopTrending(weeeTest.TestCase):
#     @weeeTest.data.file(file_name='ec_item_data.json', return_type='dict')
#     @weeeTest.mark.list('Regression', 'tb1',  'Transaction')
#     @weeeTest.mark.skip("PPD-807移除了这个组件，目前没有这个组件了")
#     def test_home_top_trending(self, *args):
#         """ 推荐-首页top_trending加购验证流程 """
#         # 获取用户的porder
#         porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=RequestHeader.ec_login_header)["object"]
#         # 获取首页top_trending数据
#         top_trending = DsCollectionHome().ds_collection_home(headers=RequestHeader.ec_login_header,
#                                                              ds_url="cm_item_exciting",
#                                                              dataobject_key="ds_item_exciting",
#                                                              scenes="cmscomponent")
#         # 断言top_trending必须有数据
#         assert top_trending["object"]["total_count"] > 0,f'首页top_trending没有数据:{top_trending["object"]}'
#         self.top_trending_product_assertion(porder["delivery_pickup_date"],top_trending["object"]["products"])
#
#
#     def top_trending_product_assertion(self, date, products: list | Any):
#         # 对返回结果商品操作及断言
#         for index, product in enumerate(products):
#             assert "weeecdn" in product["img"]
#             assert product["name"] is not None
#             assert product["price"] is not None
#             assert product["brand_name"] is not None
#             assert any(tag['tag_key'] == "freshly" for tag in product['product_tag_list'])
#             assert product["sold_status"] == "available", f'首页商品不是可售状态{product["sold_status"]}'
#             assert str(product["id"]) in product['slug'], "这个链接返回的不是这个产品的链接，请确认~"
#             # 产品标签tag
#             if product["entrance_tag"] is not None:
#                 more_link = product["entrance_tag"]["more_link"]
#                 assert product["entrance_tag"]['tag_name'] is not None
#                 # 验证点击more_link 跳转正常
#                 CommCheckFunction().comm_check_link(more_link)
#             if index == 3:
#                 break
#         # 点击商品卡片跳转pdp
#         CommCheckFunction().comm_check_pdp_link(products[-1]["id"], products[-1]["view_link"])
#         # 加购editor_pick下的商品
#         CommCheckProductsWithCart().products_add_to_cart(headers=RequestHeader.ec_login_header,
#                                                          products=products,
#                                                          porder_deal_date=date,
#                                                          product_source="mweb_home-cm_item_exposure_collection-null")
#
#
# if __name__ == '__main__':
#     weeeTest.main(base_url='https://api.tb1.sayweee.net', env='tb1')
