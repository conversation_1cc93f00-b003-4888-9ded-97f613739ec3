# !/usr/bin/python3
# -*- coding: utf-8 -*-

"""
<AUTHOR>  <PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  seller_desc.py
@Description    :
@CreateTime     :  2023/7/20 20:06
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/7/20 20:06
"""
import weeeTest
from test_dir.api.ec.ec_growth.bar_info import BarInfo


class TestBarInfoSimple(weeeTest.TestCase):

    @weeeTest.mark.list('Transaction', 'product')
    def test_bar_info(self, ec_login_header):
        """ bar_info """
        BarInfo().bar_info(headers=ec_login_header)
        # 断言
        assert self.response["result"] is True


