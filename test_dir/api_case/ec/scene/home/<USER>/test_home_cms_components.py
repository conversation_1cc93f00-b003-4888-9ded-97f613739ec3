# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_user.py
@Description    :
@CreateTime     :  2023/5/16 15:23
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 15:23
"""
import copy
import weeeTest

from test_dir.api.ec.ec_content.cms.cms_sayweee_mobile_home import CmsSayweeeHome
from test_dir.api_case.ec.common.common_check import CommonCheck


class TestHomeCmsComponents(weeeTest.TestCase):
    # @weeeTest.mark.list('test_home_cms_sayweee_mobile_home_anny', 'Regression', 'Smoke', 'Transaction')
    def test_home_cms_sayweee_mobile_home_anny(self, ec_anony_header):
        """ 首页-匿名用户-mobile首页所有组件验证流程 """
        # 获取湾区首页所有组件数据
        mobile_home = CmsSayweeeHome().cms_sayweee_mobile_home(headers=ec_anony_header,
                                                               zipcode="98011", sales_org_id=4, lang="en")
        assert mobile_home["result"] is True
        components = mobile_home["object"]["layout"]["sections"][0]["components"]
        assert len(components) > 0
        self.mobile_component_assert(components=components, status_type="h5_anny")

    @weeeTest.mark.list('Regression', 'Smoke', 'Transaction', 'sayweee_mobile_home_login')
    def test_home_cms_sayweee_mobile_home_login(self, ec_login_header):
        """ 首页-登录用户-mobile首页所有组件验证流程 """
        # 获取湾区首页所有组件数据
        mobile_home = CmsSayweeeHome().cms_sayweee_mobile_home(headers=ec_login_header,
                                                               zipcode="98011", sales_org_id=4, lang="en")
        assert mobile_home["result"] is True
        layout = mobile_home["object"]["layout"]
        # assert mobile_home["object"]["layout"]["sections"][0]["section_key"] == "recommended"
        components = mobile_home["object"]["layout"]["sections"][0]["components"]
        self.mobile_component_assert(components=components,  status_type="h5_login")
        for component in components:
            CommonCheck().check_home_component_assert(component=component, headers=ec_login_header)

    @weeeTest.mark.list('cms_sayweee_mobile_home', 'Regression', 'Smoke', 'Transaction')
    def test_home_cms_sayweee_pc_home_anny(self, ec_anony_header):
        """ 首页-匿名用户-pc首页所有组件验证流程 """
        specail_pc_header = copy.deepcopy(ec_anony_header)
        specail_pc_header['platform'] = 'pc'
        # 获取湾区首页所有组件数据
        pc_home = CmsSayweeeHome().cms_sayweee_pc_home(headers=specail_pc_header,
                                                       zipcode="98011", sales_org_id=4, lang="en")
        assert pc_home["result"] is True
        layout = pc_home["object"]["layout"]
        datasource = pc_home["object"]["datasource"]
        # assert mobile_home["object"]["layout"]["sections"][0]["section_key"] == "recommended"
        components = pc_home["object"]["layout"]["sections"][0]["components"]
        self.pc_component_assert(components=components, status_type="pc_anny")

    @weeeTest.mark.list('cms_sayweee_pc_home_login', 'Regression', 'Smoke', 'Transaction')
    def test_home_cms_sayweee_pc_home_login(self, ec_login_header):
        """ 首页-登录用户-pc首页所有组件验证流程 """
        specail_pc_header = copy.deepcopy(ec_login_header)
        specail_pc_header['Platform'] = 'pc'
        # 获取湾区首页所有组件数据
        pc_home = CmsSayweeeHome().cms_sayweee_pc_home(headers=specail_pc_header,
                                                       zipcode="98011", sales_org_id=4, lang="en")
        assert pc_home["result"] is True
        layout = pc_home["object"]["layout"]
        datasource = pc_home["object"]["datasource"]
        # assert mobile_home["object"]["layout"]["sections"][0]["section_key"] == "recommended"
        components = pc_home["object"]["layout"]["sections"][0]["components"]
        self.pc_component_assert(components=components, status_type="pc_login")
        # 各类组件公共方法
        for component in components:
            CommonCheck().check_home_component_assert(component=component, headers=specail_pc_header)
        specail_pc_header['Platform'] = 'H5'

    def mobile_component_assert(self, components, status_type):
        # for component in components:
        required_component_h5 = ["cm_search_bar", "cm_main_banner", "cm_top_message", "cm_categories",
                                 "cm_content_feed", "cm_item_editors_pick",
                                 "cm_item_trending", "cm_item_sale", "cm_item_new",
                                 "cm_product_line_tabs_fresh_daily"
                                 ]
        required_anny = ["cm_item_basket_starter"]
        # 获取components中所有的component_instance_key
        component_instance_keys = [component["component_instance_key"] for component in components]
        if status_type in ("h5_anny"):
            # 断言匿名用户返回cm_item_basket_starter
            for required_name in required_anny:
                assert required_name in component_instance_keys, f'组件信息返回异常，请确认{components}'
        elif status_type in ("h5_login"):
            # 断言登录用户返回new
            for required_name in required_component_h5:
                assert required_name in component_instance_keys, f'{required_name}组件信息返回异常，请确认{components}'

    def pc_component_assert(self, components, status_type):
        # for component in components:
        # cm_item_mkpl_collection MKPL TOPX 组件
        required_component_pc = ["cm_main_banner", "cm_top_message", "cm_notice_banner",
                                 "cm_banner_array", "cm_item_editors_pick",
                                 "cm_item_new", "cm_item_trending", "cm_item_sale",
                                 "cm_product_line_tabs_fresh_daily",
                                 "cm_item_mkpl_collection",
                                 "cm_item_perference", "cm_post_list",
                                 ]
        # required_component_pc = ["cm_collection_grocery_dAniGly7-1"]
        # ["cm_lightning_deals", "cm_mkpl_lightning",]
        required_anny = ["cm_item_basket_starter"]
        # 获取sections列表中所有字典的section_name值
        component_instance_keys = [component["component_instance_key"] for component in components]
        if status_type in ("pc_anny"):
            # 断言匿名用户返回
            for required_name in required_anny:
                assert required_name in component_instance_keys, f'{required_name}组件信息返回异常，请确认{components}'

        elif status_type in ("pc_login"):
            # 断言登录用户返回new
            for required_name in required_component_pc:
                assert required_name in component_instance_keys, f'{required_name}组件信息返回异常，请确认{components}'


