"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :  test_address_adjust_pin.py
@Description    :  
@CreateTime     :  2023/7/25 10:18
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/7/25 10:18
"""
import weeeTest
from test_dir.api.ec.ec_so.address.address_adjust_pin import AddressAdjustPin


class TestAddressAdjustPin(weeeTest.TestCase):
    """对地址进行pin是否正确"""

    @weeeTest.data.file(file_name='ec_so_data.json', return_type='dict')
    @weeeTest.mark.list('Transaction', 'product', 'dev')
    def test_address_adjust_pin(self, *args, ec_login_header):
        AddressAdjustPin().address_adjust_pin(headers=ec_login_header, addr_firstname=args[0]["address"]["addr_firstname"],
                                              addr_address=args[0]["address"]["addr_address"],
                                              addr_lastname=args[0]["address"]["addr_lastname"],
                                              phone=args[0]["address"]["phone"],
                                              addr_zipcode=args[0]["address"]["addr_zipcode"])
        # 断言
        assert self.response["result"] is True
