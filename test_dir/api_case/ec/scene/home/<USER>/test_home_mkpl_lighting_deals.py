# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_user.py
@Description    :
@CreateTime     :  2023/5/16 15:23
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 15:23
"""

import weeeTest
from weeeTest import log

from test_dir.api.ec.ec_mkt.activity.lighting_deals_v1 import LightingDeals
from test_dir.api_case.ec.common.comm_cms import CommCheckFunction
from test_dir.api_case.ec.common.set_user_porder import SetUserPorder


class TestHomeMKPLLightingDeals(weeeTest.TestCase):

    @weeeTest.data.file(file_name='ec_item_data.json', return_type='dict')
    @weeeTest.mark.list('MK', 'Regression-skip',  'Transaction', 'lighting')
    def test_home_mkpl_lighting_deals(self, *args, ec_login_header):
        """ 秒杀-首页MKPL秒杀相关验证流程 """
        # 只有周四才会有MKPL秒送
        porder = SetUserPorder().set_user_new_porder(headers=ec_login_header, zipcode="98011", lang="zh")
        sales_org_id = porder["sales_org_id"]
        # print(sales_org_id)
        deal_date = porder["delivery_pickup_date"]
        delivery_date = porder["delivery_pickup_date"]
        zipcode = porder["zipcode"]
        data = args[0]["category"]["search_by_catalogue"]
        # MKPL秒杀商品列表(首页)
        mkpl_lighting = LightingDeals().mkpl_lighting(headers=ec_login_header, date=delivery_date)
        # assert mkpl_lighting["object"] is not None    # 这个活动不是每天都有，所以断言去掉
        # 如果首页存在MKPL秒杀
        if mkpl_lighting.get("object") and mkpl_lighting.get("object").get("total_count", -1) > 0:
            log.info("mkpl秒杀活动存在")
            compon = mkpl_lighting["object"]["component_metadata"]
            products = mkpl_lighting["object"]["products"]
            # 首页查看全部按钮
            more_link = compon["more_link"]
            # 点击跳转
            CommCheckFunction().comm_check_link(more_link)
            for product in products:
                # 点击秒杀
                view_link = product["view_link"]
                # 点击跳转
                CommCheckFunction().comm_check_link(view_link)

                break
        else:
            print("没有配置秒杀，请先配置")

