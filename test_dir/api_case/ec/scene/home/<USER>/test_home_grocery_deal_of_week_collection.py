# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_user.py
@Description    :
@CreateTime     :  2023/5/16 15:23
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 15:23
"""
import copy
from typing import Any

import weeeTest

from test_dir.api.ec.ec_content.collection.deal_of_week_collection import DealOfWeekCollection
from test_dir.api.ec.ec_content.collection.get_collection_grocery_detail import GetCollectionGroceryDetail
from test_dir.api.ec.ec_growth.share import Share
from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder
from test_dir.api_case.ec.common.comm_check_products_with_cart import CommCheckProductsWithCart
from test_dir.api_case.ec.common.comm_cms import CommCheckFunction
from test_dir.api_case.ec.common.set_user_porder import SetUserPorder


class TestHomeGroceryDealOfWeekCollection(weeeTest.TestCase):

    @weeeTest.mark.list('deal_of_week', 'Regression', 'Smoke',  'Transaction')
    def test_home_grocery_deal_of_week_collection(self, ec_login_header):
        """ 合集-一周一品验证流程 """
        # 切换到98011地区,获取用户的porder
        hg_header = copy.deepcopy(ec_login_header)
        SetUserPorder().set_user_new_porder(headers=hg_header, zipcode="99991", lang="en")

        # 获取用户的porder
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=hg_header)["object"]
        # 首页一周一品
        deal_of_week = DealOfWeekCollection().deal_of_week_collection(headers=hg_header,
                                                                      date=porder["delivery_pickup_date"],
                                                                      zipcode=porder["zipcode"])
        assert deal_of_week["object"] is not None
        # week_collection = deal_of_week["object"]
        # 判断首页一周一品是否存在
        # if deal_of_week["object"] is not None:
        self.deal_of_week_assertion(hg_header, porder["delivery_pickup_date"], deal_of_week["object"])

        # 访问一周一品详情页
        collect_detail = GetCollectionGroceryDetail().get_collection_grocery_detail(
            headers=hg_header,
            collection_key=deal_of_week["object"]["key"])
        assert collect_detail[
                   "object"] is not None, f'这个合集{deal_of_week["object"]["key"]}未返回任何信息，请确认{collect_detail["object"]}'
        assert collect_detail["object"]["total_count"] == 1
        # 分享合集
        collect_share = Share().collection_grocery_share(headers=hg_header,
                                                         collection_key=deal_of_week["object"]["key"])
        # 合集分享断言
        self.collect_share_assertion(collect_share["object"]["share_infos"])
        # 切回98011
        SetUserPorder().set_user_zipcode(headers=hg_header, zipcode="98011")

    def deal_of_week_assertion(self, headers, date, deal_of_week: dict | Any):
        assert deal_of_week["title"] is not None
        assert deal_of_week["deal_id"] is not None
        assert deal_of_week["link_url"] is not None
        assert "/promotion/collect/" in deal_of_week["link_url"], f'没有返回一周一品的跳转链接{deal_of_week}'
        # 点击商品卡片跳转一周一品页面
        CommCheckFunction().comm_check_link(deal_of_week["link_url"], headers=headers)
        # 首页一周一品商品
        product = deal_of_week["product"]
        assert "weeecdn" in product['img']
        assert product['name'] is not None
        assert product['price'] is not None
        assert product['sold_status'] == "available"
        assert str(product["id"]) in product['slug'], "这个链接返回的不是这个产品的链接，请确认~"
        # 加购首页一周一品商品
        CommCheckProductsWithCart().product_add_to_cart(headers=headers,
                                                        product=deal_of_week["product"],
                                                        porder_deal_date=date,
                                                        product_source="mweb_home-cm_featured_this_week-null",
                                                        quantity=product["min_order_quantity"])

        # 一周一品列表里的商品
        products = deal_of_week["products"]
        for content in products:
            # 待调整
            # assert content["share_img_url"] is not None
            # assert content["title"] is not None
            assert content["view_link"] is not None

    def collect_share_assertion(self, share: dict | Any):
        for content in share["share_content"]:
            assert content["share_img_url"] is not None
            assert content["title"] is not None
            assert content["link_url"] is not None
            # assert "/promotion/collect/" in content["link_url"] , f'当前theme 返回链接不正确，请确认{content["link_url"]}'
            assert content["language"] is not None
        assert share["show_language"] is False
        assert len(share["share_channels"]) > 0
        assert "copyLink" in share["share_channels"]

