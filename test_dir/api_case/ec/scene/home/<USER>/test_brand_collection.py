# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_user.py
@Description    :
@CreateTime     :  2023/5/16 15:23
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 15:23
"""

import weeeTest

from test_dir.api.ec.ec_content.page.qery_page_data import QueryPageData
from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder
from test_dir.api_case.ec.common.common_check import CommonCheck
from test_dir.api_case.ec.common.set_user_porder import SetUserPorder


class TestCmsBrandCollection(weeeTest.TestCase):

    @weeeTest.data.file(file_name='ec_item_data.json', return_type='dict')
    @weeeTest.mark.list('cms_brand_collection', 'Regression', 'Transaction')
    def test_cms_brand_collection(self, *args, ec_login_header):
        """ 合集-李锦记品牌合集验证流程 """
        # 这里验证李锦记页面品牌活动
        # 获取登录header
        # 获取用户的porder
        SetUserPorder().set_user_new_porder(headers=ec_login_header, zipcode="98011", lang="en")
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]
        sales_org_id = porder["sales_org_id"]
        deal_date = porder["delivery_pickup_date"]
        delivery_date = porder["delivery_pickup_date"]
        zipcode = porder["zipcode"]
        data = args[0]["category"]["search_by_catalogue"]
        page_key = "BqWhOoBx"
        # 获取李锦记品牌cms 活动页面
        cms_page = QueryPageData().query_page_data(headers=ec_login_header,
                                                   page_key="BqWhOoBx", page_type="9")

        assert cms_page["object"] is not None, f'李锦记品牌活动cms页面数据异常，请确认{cms_page["object"]}'
        CommonCheck().check_cms_data_page(headers=ec_login_header, cms_data=cms_page["object"],
                                          porder=porder, page_key="BqWhOoBx")
