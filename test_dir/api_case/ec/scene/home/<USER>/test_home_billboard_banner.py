# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_user.py
@Description    :
@CreateTime     :  2023/5/16 15:23
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 15:23
"""
from typing import Any

import weeeTest
from test_dir.api.ec.ec_mkt.banner.get_banner_list import GetBannerList
from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder
from test_dir.api_case.ec.common.comm_cms import CommCheckFunction


class TestHomeBillboardBanner(weeeTest.TestCase):

    # @weeeTest.mark.list('BA', 'Regression', 'Smoke',  'Transaction')
    def test_home_billboard_banner(self, ec_login_header):
        """ Banner-首页billboard轮播图验证流程 """
        # 该功能已于2024-07-24下线，用例不再执行
        # 在en-explorer 下会有
        # 获取用户的porder
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]

        # 1、获取首页billboard轮播图
        banner_list = GetBannerList().get_banner_list(headers=ec_login_header,
                                                      type="billboard",
                                                      sales_org_id=porder["sales_org_id"],
                                                      lang="en",
                                                      date=porder["delivery_pickup_date"])
        assert banner_list["object"] is not None
        # 断言有billboard banner,并访问轮播图链接
        self.billboard_banner_assertion(banner_list["object"]["carousel"])



    def billboard_banner_assertion(self, carousel_banner: list | Any):
        for carousel in carousel_banner:
            assert carousel['img_url'] is not None
            assert carousel['detail'] is not None
            # 断言有首页轮播图,并访问轮播图链接
            url = carousel['detail']
            # 点击跳转
            CommCheckFunction().comm_check_link(url)

