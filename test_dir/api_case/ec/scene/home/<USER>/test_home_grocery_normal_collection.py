# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_user.py
@Description    :
@CreateTime     :  2023/5/16 15:23
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 15:23
"""
import copy
from typing import Any

import weeeTest

from test_dir.api.ec.ec_content.collection.get_collection_grocery_detail import GetCollectionGroceryDetail
from test_dir.api.ec.ec_content.collection.get_collection_grocery_home import GetCollectionGroceryHome
from test_dir.api.ec.ec_growth.share import Share
from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder
from test_dir.api_case.ec.common.comm_check_products_with_cart import CommCheckProductsWithCart
from test_dir.api_case.ec.common.comm_cms import CommCheckFunction
from test_dir.api_case.ec.common.set_user_porder import SetUserPorder


class TestHomeGroceryNormalCollection(weeeTest.TestCase):

    @weeeTest.mark.list('Regression_skip', 'Smoke_skip', 'Transaction')
    # @weeeTest.mark.timeout(10000)
    def test_home_grocery_normal_collection(self, ec_login_header):
        """ 合集-普通手工合集验证流程 """
        # 切换到98011地区,获取用户的porder
        g_header = copy.deepcopy(ec_login_header)
        SetUserPorder().set_user_new_porder(headers=g_header, zipcode="99991", lang="en")
        # 运营很少配置手动合集，所以此用例spe_collection很多时候不返回数据
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=g_header)["object"]
        # 获取首页所有合集key
        grocery_home = GetCollectionGroceryHome().get_collection_grocery_home(headers=g_header)
        assert grocery_home["object"]["total_count"] > 0, f'首页未返回任何合集，请确认{grocery_home["object"]}'
        self.normal_collection_assertion(g_header, porder["delivery_pickup_date"], grocery_home["object"]["deals"])
        # 切回98011
        SetUserPorder().set_user_zipcode(headers=g_header, zipcode="98011")

    def normal_collection_assertion(self, headers, date, deals: dict | Any):
        # 遍历deals列表
        for deal in deals:
            assert deal["title"] is not None
            assert deal["key"] is not None
            assert deal["collection_more_link"] is not None
            assert deal["link_url"] is not None
            # 验证点击more_link 跳转正常
            CommCheckFunction().comm_check_link(deal["collection_more_link"], headers=headers)
            # 验证点击link_url跳转正常
            CommCheckFunction().comm_check_link(deal["link_url"], headers=headers)

            # 检查'link_url'如果包含特定子串'promotion/collect'，表示是ERP后台配置是合集
            # 检查'link_url'如果包含特定子串'page/activity'，表示是sales后台配置的cms合集

            # 根据合集key，访问合集详情
            collect_detail = GetCollectionGroceryDetail().get_collection_grocery_detail(
                headers=headers, collection_key=deal["key"])

            assert collect_detail[
                       "object"] is not None, f'这个合集{deal["key"]}未返回任何信息，请确认{collect_detail["object"]}'
            assert collect_detail["object"]["total_count"] == 1
            detail_deals = collect_detail["object"]["deals"]
            # 分享合集
            collect_share = Share().collection_grocery_share(headers=headers,
                                                             collection_key=deal["key"])
            # 合集分享断言
            self.collect_share_assertion(deal["key"], collect_share["object"]["share_infos"])

            for detail_deal in detail_deals:
                collection_more_link = detail_deal["collection_more_link"]
                link_url = detail_deal["link_url"]
                # assert "/promotion/collect" in detail_deal["link_url"]
                assert detail_deal["title"] is not None
                assert detail_deal["key"] == deal["key"]
                # 点击商品卡片跳转pdp
                CommCheckFunction().comm_check_pdp_link(product_id=detail_deal["products"][-1]["id"],
                                                        view_link=detail_deal["products"][-1]["view_link"], headers=headers)
                # 加购合集下的商品
                CommCheckProductsWithCart().products_add_to_cart(headers=headers,
                                                                 products=detail_deal["products"],
                                                                 porder_deal_date=date,
                                                                 product_source="collect-" + str(detail_deal["id"]))

    def collect_share_assertion(self, key, share: dict | Any):
        for content in share["share_content"]:
            # assert content["share_img_url"] is not None, f'合集分享 没有返回img url，请确认{key}'
            assert content["title"] is not None, f'合集分享 没有返回title，请确认{key}'
            assert content["link_url"] is not None, f'合集分享 没有返回link_url，请确认{key}'
            # assert "/promotion/collect/" in content["link_url"] , f'当前theme 返回链接不正确，请确认{content["link_url"]}'
            assert content["language"] is not None, f'合集分享 没有返回language，请确认{key}'
        assert share["show_language"] is False
        assert len(share["share_channels"]) > 0
        assert "copyLink" in share["share_channels"]


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net', env='tb1')
