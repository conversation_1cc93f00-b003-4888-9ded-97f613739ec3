# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_user.py
@Description    :
@CreateTime     :  2023/5/16 15:23
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 15:23
"""

import weeeTest

from test_dir.api.ec.ec_item.search_v2.search_by_cuisine import SearchByCuisine
from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder
from test_dir.api_case.ec.common.common_check import CommonCheck


class TestHomeOurFavorites(weeeTest.TestCase):

    @weeeTest.mark.list('home_our_favorites', 'Regression', 'Smoke',  'Transaction')
    def test_home_our_favorites(self, ec_login_header):
        """ 推荐-首页our_favorites加购验证流程 """
        # 获取用户的porder
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]

        # 获取首页our_favorites数据
        our_favorites = SearchByCuisine().search_by_cuisine_home_page(headers=ec_login_header,
                                                                      dataobject_key="ds_big_product_editors_pick_2557449")
        # 断言our_favorites必须有数据
        assert our_favorites["object"]["total_count"] > 0, f'首页editor_pick没有返回数据{our_favorites["object"]}'
        for index, product in enumerate(our_favorites["object"]["products"]):
            CommonCheck().check_product_info(ec_login_header, product=product, category_type="others", filters="others",
                                             source="homepage")
            if index == 3:
                break
