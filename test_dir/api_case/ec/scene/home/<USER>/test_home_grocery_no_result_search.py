# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_user.py
@Description    :
@CreateTime     :  2023/5/16 15:23
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 15:23
"""

import weeeTest

from test_dir.api.ec.ec_item.record.search_submit_noresult import SearchSubmitNoresult
from test_dir.api.ec.ec_item.search_v3.search_by_keyword_v3 import SearchByKeywordV3
from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder


class TestHomeGroceryNoResultSearch(weeeTest.TestCase):
    @weeeTest.data.file(file_name='ec_item_data.json', return_type='dict')
    @weeeTest.mark.list('Regression', 'Transaction')
    def test_home_grocery_no_result_search(self, *args, ec_login_header):
        """ 搜索-首页大搜索无结果验证流程 """
        # 获取用户的porder
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]

        # 随机搜索，搜索空结果
        search_res = SearchByKeywordV3().search_by_keyword_v3(headers=ec_login_header,
                                                              filter_key_word=args[0]["search"]["noresult_keyword"])
        assert search_res["object"]["total_count"] == 0

        # 提交空结果
        search_submit = SearchSubmitNoresult().search_submit_noresult(headers=ec_login_header,
                                                                      noresult_keyword=args[0]["search"][
                                                                          "noresult_keyword"])

        # 断言提交成功
        assert search_submit["result"] is True
        assert search_submit["object"] == 'Success'

