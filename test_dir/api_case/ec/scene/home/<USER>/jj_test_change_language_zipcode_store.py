# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_user.py
@Description    :
@CreateTime     :  2023/5/16 15:23
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 15:23
"""
import json
import time

import weeeTest

from test_dir.api.ec.ec_customer.language_rest.language_rest import LanguageRest
from test_dir.api.ec.ec_item.store.api_store import ApiStore
from test_dir.api.ec.ec_so.preorder.update_zipcode import UpdateZipcode


class TestChangeLanguageZipcodeStore(weeeTest.TestCase):
    @weeeTest.data.file(file_name='ec_item_data.json', return_type='dict')
    @weeeTest.mark.list('Regression',  'Transaction','tb1')
    def test_change_language_zipcode_store(self, *args, ec_login_header):
        """ Preorder-切换语言、切换销售组织、切换store验证流程 """
        # # 获取用户的preorder
        # porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=headers)["object"]
        # 获取不同销售组织下不同zipcode
        # sales_zipcode = json.loads(EcDB().get_sales_org_id_zipcode())
        sales_data = args[0]["sales_data"]
        for sale_date in sales_data:
            zipcode = sale_date["zipcode"]

            # 切换不同销售组织的zipcode
            UpdateZipcode().update_zipcode_v1(headers=ec_login_header, zipcode=zipcode)
            if self.response["message_id"] == "SO90029":
                print("这个地区没有帖子")
            else:
                # 断言
                assert self.response["result"] is True
                if zipcode == "94501":
                    # 切换语言
                    lang = ["en", "zh", "zh-Hant", "ja", "ko", "vi"]
                    for lang in lang:
                        LanguageRest().update_account_language(headers=ec_login_header, lang=lang)
                        if lang is "en":
                            # 获取store
                            ApiStore().store_list(headers=ec_login_header, lang=lang, zipcode=int(zipcode))
                            if self.response["object"] is not None:
                                for store in self.response["object"]:
                                    store_id = store["store_id"]
                                    # 切换store
                                    ApiStore().store_select(headers=ec_login_header, store_id=store_id,
                                                            zipcode=int(zipcode))
                                    time.sleep(1)
                                    # 断言
                                    assert self.response["result"] is True
        UpdateZipcode().update_zipcode_v1(headers=ec_login_header)



if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net', env='tb1')
