# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_user.py
@Description    :
@CreateTime     :  2023/5/16 15:23
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 15:23
"""
from typing import Any

import weeeTest

from test_dir.api.ec.ec_item.ds_collection_rest.ds_collection_home import DsCollectionHome
from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder
from test_dir.api_case.ec.common.comm_cms import CommCheckFunction


class TestHomeCollectionSpecific(weeeTest.TestCase):
    @weeeTest.mark.list('Regression', 'Smoke',  'Transaction')
    def test_home_collection_specific(self, ec_login_header):
        """ 合集-首页waterfall置顶合集验证流程 """
        # 获取用户的porder
        # 运营很少配置手动合集，所以此用例spe_collection很多时候不返回数据
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]
        spe_collection = DsCollectionHome().collection_specific(headers=ec_login_header)
        assert spe_collection["result"] is True
        if spe_collection["object"]["total_count"] > 0:
            self.collection_specific_assertion(spe_collection["object"]["deals"], headers=ec_login_header)

    def collection_specific_assertion(self, deals: dict | Any, headers):
        for deal in deals:
            assert deal["title"] is not None, f'spe_collection信息返回异常{deals}'
            assert deal["key"] is not None, f'spe_collection信息返回异常{deals}'
            assert deal["collection_more_link"] is not None, f'spe_collection信息返回异常{deals}'
            assert deal["link_url"] is not None, f'spe_collection信息返回异常{deals}'
            assert len(deal["link_url"]) > 0, f'spe_collection信息返回异常{deals}'

            # 验证点击more_link 跳转正常
            CommCheckFunction().comm_check_link(deal["collection_more_link"], headers=headers)
            # 验证点击link_url跳转正常
            CommCheckFunction().comm_check_link(deal["link_url"], headers=headers)

