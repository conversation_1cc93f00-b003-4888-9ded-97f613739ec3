# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_user.py
@Description    :
@CreateTime     :  2023/5/16 15:23
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 15:23
"""
from typing import Any

import weeeTest

from test_dir.api.ec.ec_promotion.promotion.query_promotion_info import QueryPromotionInfo
from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder
from test_dir.api_case.ec.common.set_user_porder import SetUserPorder


class TestHomeFBWCoupon(weeeTest.TestCase):
    @weeeTest.mark.list('Regression', 'Transaction')
    def test_home_promotions_personal(self, ec_login_header):
        """ 推荐-首页FBW Coupon验证流程 """
        # 获取用户的porder
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]
        deal_date = porder.get("delivery_pickup_date")
        deal_id = porder.get("deal_id")
        sales_id = porder.get("sales_org_id")
        zipcode = porder.get("zipcode")
        # 获取首页FBW Coupon数据
        fbw_coupon = QueryPromotionInfo().promotions_personal_homepage(headers=ec_login_header,
                                                                       zipcode=zipcode, lang="en",
                                                                       date=deal_date)
        assert fbw_coupon["result"] is True
        if fbw_coupon.get("object"):
            self.fbw_coupon_home_assertion(fbw_coupon.get("object"), fbw_coupon_source="homepage")

    @weeeTest.mark.list('Regression', 'Transaction')
    def test_landing_promotions_personal(self, ec_login_header):
        """ 推荐-FBW Coupon 落地页验证流程 """
        # 获取用户的porder
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]
        # 获取首页FBW Coupon数据
        fbw_coupon = QueryPromotionInfo().promotions_personal_module(headers=ec_login_header,
                                                                     source="landing", user_tag="fbw_bakery")
        assert fbw_coupon["result"] is True
        self.fbw_coupon_home_assertion(fbw_coupon.get("object"), fbw_coupon_source="landing")

    def fbw_coupon_home_assertion(self, date, fbw_coupon_source):
        # 对返回结果商品操作及断言
        has_discount = date.get("has_discount")
        landing_title = date.get("landing_title")
        landing_top_tip = date.get("landing_top_tip")
        module_subtitle = date.get("module_subtitle")
        module_title = date.get("module_title")
        skip_url = date.get("skip_url")
        source = date.get("source")
        user_tag = date.get("user_tag")
        groups = date.get("groups")
        if groups is not None:
            if fbw_coupon_source == "homepage":
                assert source == "homepage", f"{source}fbw coupon 数据返回异常{date}"
                assert skip_url is not None, f"{source}fbw coupon 数据返回异常{date}"
                assert "/promotion/coupon-landing" in skip_url, f"{source}fbw coupon 数据返回异常{date}"
            elif fbw_coupon_source == "landing":
                assert source == "landing", f"{source}fbw coupon 数据返回异常{date}"
                assert landing_title is not None, f"{source}fbw coupon 数据返回异常{date}"
                assert landing_top_tip == "Fresh bakery", f"{source}fbw coupon 数据返回异常{date}"
            assert module_subtitle is not None, f"{source}fbw coupon 数据返回异常{date}"
            assert module_title is not None, f"{source}fbw coupon 数据返回异常{date}"
            assert user_tag == "fbw_bakery", f"{source}fbw coupon 数据返回异常{date}"
            assert has_discount is True, f"{source}fbw coupon 数据返回异常{date}"
            for group in groups:
                skus = group.get("skus")
                assert skus, f"{source}fbw coupon 数据返回异常{date}"
                assert group.get("group_name") is not None, f"{source}fbw coupon 数据返回异常{date}"
                assert group.get("group_short_title") is not None, f"{source}fbw coupon 数据返回异常{date}"
                assert group.get("group_title") is not None, f"{source}fbw coupon 数据返回异常{date}"
                if len(skus) > 6:
                    assert group.get("can_see_more") is True, f"{source}fbw coupon 数据返回异常{date}"
                for item in skus:
                    product_tag_list = item.get("product_tag_list")
                    assert any(tag['tag_key'] == "personalized_discount" for tag in
                               product_tag_list), f"{source}fbw coupon 数据返回异常{date}"
                    tags = [item2 for item2 in product_tag_list if item2['tag_key'] == "personalized_discount"]
                    for tag in tags:
                        assert tag.get["tag_color"] == "#EC4143", f"{source}fbw coupon 数据返回异常{date}"
                        assert tag.get("tag_font_color") == "#FFFFFF", f"{source}fbw coupon 数据返回异常{date}"
                        assert tag.get("tag_name") is not None, f"{source}fbw coupon 数据返回异常{date}"
                        assert tag.get("tail_title") == "with coupon", f"{source}fbw coupon 数据返回异常{date}"
