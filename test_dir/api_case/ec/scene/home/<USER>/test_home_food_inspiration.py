# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_user.py
@Description    :
@CreateTime     :  2023/5/16 15:23
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 15:23
"""
import copy
from typing import Any
import weeeTest

from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder
from test_dir.api.ec.ec_social.recommend_post_info.recommend_portal import RecommendPortal
from test_dir.api_case.ec.common.comm_cms import CommCheckFunction
from test_dir.api_case.ec.common.common_check import CommonCheck


class TestHomeFoodInspiration(weeeTest.TestCase):

    @weeeTest.mark.list('Smoke', 'Transaction', 'test_home_food_inspiration')
    def test_home_food_inspiration(self, ec_login_header):
        """ 首页-热门帖子验证流程 """
        # 获取用户的porder
        # 用例上写的是要切换store, 切换到explore store才有food inspiration, 但header中是cn store，也有数据
        food_header = copy.deepcopy(ec_login_header)
        food_header['Weee-Store'] = 'us'
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]
        # 获取首页热门帖子推荐数据
        food_inspiration = RecommendPortal().recommend_portal_v2(headers=food_header, lang="en",
                                                                 date=porder["delivery_pickup_date"])
        assert food_inspiration.get("object") is not None, f"当前首页没有food inspiration{food_inspiration}"
        assert food_inspiration.get("object").get("list"), f"当前首页没有food inspiration{food_inspiration}"
        assert "/social/hashtag/list" in food_inspiration.get("object").get(
            "url"), f'首页热门帖子数据异常，请确认{food_inspiration}'
        # 点击热门帖子页面
        CommCheckFunction().comm_check_link(food_inspiration.get("object").get("url"), headers=food_header)
        assert food_inspiration.get("object").get(
            "promoted_featured_title") is not None, f"当前首页没有food inspiration{food_inspiration}"
        assert food_inspiration.get("object").get(
            "label") is not None, f"当前首页没有food inspiration{food_inspiration}"
        assert food_inspiration.get("object").get("source") == "portal", f"首页food inspiration异常{food_inspiration}"

        food_list = food_inspiration.get("object").get("list")
        for item in food_list:
            self.home_food_inspiration_assertion(item, headers=food_header)
        food_header['Weee-Store'] = 'cn'

    def home_food_inspiration_assertion(self, food_obj: dict | Any, headers):
        assert food_obj.get("label"), f"首页food inspiration异常{food_obj}"
        assert food_obj.get("post_ids"), f"首页food inspiration异常{food_obj}"
        assert food_obj.get("posts"), f"首页food inspiration异常{food_obj}"
        assert food_obj.get("tag_id"), f"首页food inspiration异常{food_obj}"
        assert food_obj.get("url"), f"首页food inspiration异常{food_obj}"
        if food_obj.get("tag_id") in (-1, -2):
            assert "/social?tag_id=" + str(food_obj.get("tag_id")) in food_obj.get("url")
        elif food_obj.get("tag_id") in (464, 463):
            assert "/social/event/detail/" in food_obj.get("url")

        # 点击视频跳转
        CommCheckFunction().comm_check_link(food_obj.get("url"), headers=headers)
        # 每个帖子分类下的视频
        for index, post in enumerate(food_obj.get("posts")):
            CommonCheck().check_video_data(video_list=post, headers=headers)

            if index == 5:
                break


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net', env='tb1')
