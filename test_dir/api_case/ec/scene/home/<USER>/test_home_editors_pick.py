# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_user.py
@Description    :
@CreateTime     :  2023/5/16 15:23
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 15:23
"""
from typing import Any

import weeeTest

from test_dir.api.ec.ec_item.search_v2.search_by_cuisine import SearchByCuisine
from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder
from test_dir.api_case.ec.common.comm_check_products_with_cart import CommCheckProductsWithCart
from test_dir.api_case.ec.common.comm_cms import CommCheckFunction
from test_dir.api_case.ec.common.common_check import CommonCheck


class TestHomeEditorPick(weeeTest.TestCase):
    @weeeTest.mark.list('home_editor_pick', 'Regression', 'Smoke',  'Transaction')
    def test_home_editor_pick(self, ec_login_header):
        """ 推荐-首页editor_pick加购验证流程 """
        # 获取用户的porder
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]

        # 获取首页editor_pick数据
        editors_pick = SearchByCuisine().search_by_cuisine_home_page(headers=ec_login_header,
                                                                     dataobject_key="ds_item_editors_pick")
        # 断言editor_pick必须有数据
        assert editors_pick["object"]["total_count"] > 0, f'首页editor_pick没有返回数据{editors_pick["object"]}'
        for index, product in enumerate(editors_pick["object"]["products"]):
            CommonCheck().check_product_info(ec_login_header, product=product, category_type="others", filters="others",
                                             source="homepage")
            if index == 3:
                break



    @weeeTest.mark.list('home_our_favorites', 'Regression', 'Smoke',  'Transaction')
    def test_home_our_favorites(self, ec_login_header):
        """ 推荐-首页our_favorites加购验证流程 """
        # 获取用户的porder
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]

        # 获取首页our_favorites数据
        our_favorites = SearchByCuisine().search_by_cuisine_home_page(headers=ec_login_header,
                                                                      dataobject_key="ds_big_product_editors_pick_1822024")
        # 断言our_favorites必须有数据
        assert our_favorites["object"]["total_count"] > 0, f'首页editor_pick没有返回数据{our_favorites["object"]}'
        for index, product in enumerate(our_favorites["object"]["products"]):
            CommonCheck().check_product_info(ec_login_header, product=product, category_type="others", filters="others",
                                             source="homepage")
            if index == 3:
                break

    def editors_pick_product_assertion(self, date, products: list | Any, headers):
        # 对返回结果商品操作及断言
        for index, product in enumerate(products):
            assert "weeecdn" in product["img"]
            assert product["name"] is not None
            assert product["price"] is not None
            assert product["sold_status"] == "available", f'首页商品不是可售状态{product["sold_status"]}'

            assert str(product["id"]) in product['slug'], "这个链接返回的不是这个产品的链接，请确认~"
            # 产品标签tag
            if product["entrance_tag"] is not None:
                more_link = product["entrance_tag"]["more_link"]
                assert product["entrance_tag"]['tag_name'] is not None
                # 验证点击more_link 跳转正常
                CommCheckFunction().comm_check_link(more_link)
            if index == 3:
                break
        # 点击商品卡片跳转pdp
        CommCheckFunction().comm_check_pdp_link(products[-1]["id"], products[-1]["view_link"])
        # 加购editor_pick下的商品
        CommCheckProductsWithCart().products_add_to_cart(headers=headers,
                                                         products=products,
                                                         porder_deal_date=date,
                                                         product_source="mweb_home-cm_item_editors_pick-null")


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net', env='tb1')
