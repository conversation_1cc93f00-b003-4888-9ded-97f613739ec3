# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_user.py
@Description    :
@CreateTime     :  2023/5/16 15:23
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 15:23
"""
import json
from typing import Any

import pytest
import weeeTest
from weeeTest import log

from test_data.ec.simple.common import Header
from test_dir.api.ec.ec_marketplace.home_carousel.home_mkpl_carousel import HomeMkplCarousel
from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder
from test_dir.api_case.ec.common.comm_check_products_with_cart import CommCheckProductsWithCart
from test_dir.api_case.ec.common.comm_cms import CommCheckFunction
from test_dir.api_case.ec.common.get_root_dir import get_project_dir
from test_dir.api_case.ec.common.set_user_porder import SetUserPorder


class TestHomeFreshDaily(weeeTest.TestCase):
    @staticmethod
    def _home_fresh_daily(headers):
        # 获取用户的porder
        # porder = SetUserPorder().get_last_delivery_date(headers=headers)
        # 获取首页每日现做数据
        cms_collection = HomeMkplCarousel().fresh_daily_carousel(headers=headers)
        assert cms_collection["object"] is not None
        assert cms_collection["object"][
                   "total_count"] > 0, f'首页“每日现做组件” 没有数据返回，请确认~{cms_collection["object"]}'
        assert cms_collection["object"]["component_metadata"]["title"] is not None
        assert cms_collection["object"]["component_metadata"]["sub_title"] is not None
        # component_metadata = cms_collection["object"]["component_metadata"]
        # component_metadata = cms_collection["object"]["products"]
        tabs = cms_collection["object"]["tabs"]
        return tabs

    print("hello===>", get_project_dir() + "/test_data/autotest_token.json")
    with open(get_project_dir() + "/test_data/autotest_token.json", "r", encoding='utf-8') as f:
        login_header = json.load(f)
    log.debug(f"test_home_fresh_daily_login_header={login_header}")
    porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=login_header)["object"]
    tabs = _home_fresh_daily(headers=login_header)

    @weeeTest.mark.list('Regression', 'Transaction')
    @pytest.mark.parametrize("tabs", tabs)
    def test_home_fresh_daily(self, tabs, ec_login_header):
        """ 推荐-首页“每日现做组件”验证流程 """
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]

        # 切换fresh_gourmet ,fresh_bakery tab
        self.fresh_daily_tab_assertion(tab=tabs, date=porder["delivery_pickup_date"],
                                       header=ec_login_header)

    def fresh_daily_product_assertion(self, date, products: list | Any, header):
        # 对返回结果商品操作及断言
        for index, product in enumerate(products):
            assert "weeecdn" in product["img"]
            assert product["name"] is not None, f'FBW商品{product["id"]} 信息{product}返回异常，请确认~'
            assert product["price"] is not None, f'FBW商品{product["id"]} 信息{product}返回异常，请确认~'
            assert product["biz_type"] == "fbw", f'FBW商品{product["id"]} 信息{product}返回异常，请确认~'
            # if product.get('product_tag_list'):
            #     assert any(tag['tag_key'] == "freshly" for tag in product.get('product_tag_list')), f"product={product}"
            # 20241115暂时去掉，需求有变动
            # assert product["sold_status"] == "available", f'FBW商品{product["id"]} 信息{product}返回异常，请确认~'
            assert str(product["id"]) in product['slug'], f'FBW商品{product["id"]} 信息{product}返回异常，请确认~'
            if index == 3:
                break
        # 点击商品卡片跳转pdp
        CommCheckFunction().comm_check_pdp_link(products[-1]["id"], products[-1]["view_link"], headers=header)
        # 加购商品
        CommCheckProductsWithCart().products_add_to_cart(headers=header,
                                                         products=products,
                                                         porder_deal_date=date,
                                                         product_source="mweb_home-cm_item_exposure_collection-null")

    def fresh_daily_tab_assertion(self, tab, date, header):
        # 切换tab
        # 切换fresh_gourmet ,fresh_bakery tab
        cms_tab = HomeMkplCarousel().fresh_daily_carousel(headers=header,
                                                          key=tab["key"])
        assert cms_tab["object"] is not None
        assert cms_tab["object"]["total_count"] > 0, f'首页“每日现做组件” 没有数据返回，请确认~{cms_tab["object"]}'
        if tab["key"] == "fresh_gourmet":
            assert "/mkpl/freshdeli/landing" in tab[
                "more_link"], f'fresh_gourmet 查看更多跳转链接不对，{tab["more_link"]}'
            # assert cms_tab["object"]["products"]["category"]
            # 检查所有产品的category是否以'freshgourmet'开头
            assert all(product['category'].startswith('freshgourmet') for product in
                       cms_tab["object"]["products"]), f"Not all categories are 'freshgourmet'"
        elif tab["key"] == "fresh_bakery":
            assert "/mkpl/bakery/landing" in tab[
                "more_link"], f'fresh_bakery 查看更多跳转链接不对，{tab["more_link"]}'
            # 检查所有产品的category是否以'bakery'开头
            assert all(product['category'].startswith('bakery') for product in
                       cms_tab["object"]["products"]), f"Not all categories are 'bakery'"
        else:
            assert tab["more_link"] is not None, f'{tab["key"]} 查看更多跳转链接不对，{tab["more_link"]}'
            assert cms_tab["object"]["products"], f'{tab["key"]}数据异常'

        # 点击查看每个tab下的查看更多按钮
        CommCheckFunction().comm_check_link(tab['more_link'], headers=header)
        # 首页“每日现做组件” 基本断言
        self.fresh_daily_product_assertion(date, cms_tab["object"]["products"], header)


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net', env='tb1')
