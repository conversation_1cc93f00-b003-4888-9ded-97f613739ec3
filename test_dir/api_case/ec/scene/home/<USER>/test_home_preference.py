# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_user.py
@Description    :
@CreateTime     :  2023/5/16 15:23
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 15:23
"""
from typing import Any
import random
import weeeTest

from test_dir.api.ec.ec_item.product_v2.get_products_preference import GetProductsPreference
from test_dir.api.ec.ec_item.recommend.get_preference_products import GetPreferenceProducts
from test_dir.api.ec.ec_mkt.preference.get_preference import GetPreference
from test_dir.api.ec.ec_so.preorder.create_preorder import CreatePreOrder
from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder
from test_dir.api_case.ec.common.comm_check_products_with_cart import CommCheckProductsWithCart
from test_dir.api_case.ec.common.comm_cms import CommCheckFunction
from test_dir.api_case.ec.common.common_check import CommonCheck


class TestHomePreference(weeeTest.TestCase):
    @weeeTest.mark.list('Regression', 'Smoke',  'Transaction')
    def test_home_cart_preference_anony(self, ec_anony_header):
        """ 猜你喜欢-匿名用户-首页/购物车猜你喜欢验证流程 """
        # 匿名用户之前要创建porder
        CreatePreOrder().create_preorder(headers=ec_anony_header)
        # 获取首页\PC Account页猜你喜欢数据
        perference_home = GetPreference().get_preference(headers=ec_anony_header)
        # 断言猜你喜欢必须有数据
        assert perference_home["object"][
                   "total_count"] > 0, f'首页猜你喜欢数据返回不足{perference_home}，请确认首页商品推荐是否有问题~'
        assert perference_home["object"][
                   "search_catalogue_num"] == "perference_home", f'首页猜你喜欢数据返回不足{perference_home}，请确认首页商品推荐是否有问题~'
        products = perference_home["object"]["products"]
        assert len(products) > 10, f'首页猜你喜欢数据返回不足{products}，请确认首页商品推荐是否有问题~'
        # 获取购物车猜你喜欢
        perference_cart = GetPreferenceProducts().get_preference_products_cart(headers=ec_anony_header)
        # 断言猜你喜欢必须有数据
        assert perference_cart["object"][
                   "total_count"] > 0, f'购物车猜你喜欢数据返回不足{perference_cart}，请确认首页商品推荐是否有问题~'
        products = perference_cart["object"]["products"]
        assert len(products) > 10, f'购物车猜你喜欢数据返回不足{perference_cart}，请确认首页商品推荐是否有问题~'

    @weeeTest.mark.list('Regression', 'Smoke',  'Transaction')
    def test_home_cart_preference(self, ec_login_header):
        """ 猜你喜欢-首页/购物车/PC Account页猜你喜欢验证流程 """
        # 获取用户的porder
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]
        # 获取首页\PC Account页猜你喜欢数据
        perference_home = GetPreference().get_preference(headers=ec_login_header,
                                                         zipcode=porder["zipcode"],
                                                         date=porder["delivery_pickup_date"])
        # 断言猜你喜欢必须有数据
        assert perference_home["object"][
                   "total_count"] > 0, f'首页猜你喜欢数据返回不足{perference_home}，请确认首页商品推荐是否有问题~'
        assert perference_home["object"][
                   "search_catalogue_num"] == "perference_home", f'首页猜你喜欢数据返回不足{perference_home}，请确认首页商品推荐是否有问题~'
        products = perference_home["object"]["products"]
        # 商品信息断言、点击pdp、搜藏操作
        for index, product in enumerate(products) :
            CommonCheck().check_product_info(ec_login_header, product=product, category_type="others",
                                             filters="others", source="homepage")
            if index == 5:
                break

        # 获取猜你喜欢下的商品进行加购
        CommCheckProductsWithCart().products_add_to_cart(ec_login_header, products,
                                                         porder["delivery_pickup_date"],
                                                         product_source="portal-recommend")

        # 获取购物车猜你喜欢
        perference_cart = GetPreferenceProducts().get_preference_products_cart(headers=ec_login_header)
        # 断言猜你喜欢必须有数据
        assert perference_cart["object"][
                   "total_count"] > 0, f'猜你喜欢数据返回不足{perference_cart}，请确认首页商品推荐是否有问题~'
        products = perference_cart["object"]["products"]

        # 商品信息断言、点击pdp、搜藏操作
        for index, product in enumerate(products) :
            CommonCheck().check_product_info(ec_login_header, product=product, category_type="others",
                                             filters="others", source="cart")
            # 点击商品进行搜藏
            CommCheckFunction().comm_set_favorites(headers=ec_login_header,
                                                   target_id=product["id"])
            if index == 5:
                break


        # 获取猜你喜欢下的商品进行加购
        CommCheckProductsWithCart().products_add_to_cart(ec_login_header,
                                                         products, porder["delivery_pickup_date"],
                                                         product_source="shopping-search")

    @weeeTest.mark.list('B2B', 'Regression', 'Smoke',  'Transaction')
    def test_b2b_home_cart_preference(self, ec_login_header):
        """ 猜你喜欢-首页/购物车/PC Account页猜你喜欢验证流程 """
        # 获取用户的porder
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]
        # 获取首页\PC Account页猜你喜欢数据
        perference_home = GetPreference().get_preference(headers=ec_login_header,
                                                         zipcode=porder["zipcode"],
                                                         date=porder["delivery_pickup_date"])
        # 断言猜你喜欢必须有数据
        assert perference_home["object"][
                   "total_count"] > 0, f'首页猜你喜欢数据返回不足{perference_home}，请确认首页商品推荐是否有问题~'
        assert perference_home["object"][
                   "search_catalogue_num"] == "perference_home", f'首页猜你喜欢数据返回不足{perference_home}，请确认首页商品推荐是否有问题~'
        products = perference_home["object"]["products"]
        # 商品信息断言、点击pdp、搜藏操作
        for index, product in enumerate(products) :
            CommonCheck().check_product_info(ec_login_header, product=product, category_type="others",
                                             filters="others", source="homepage")
            if index == 5:
                break

        # 获取猜你喜欢下的商品进行加购
        CommCheckProductsWithCart().products_add_to_cart(ec_login_header, products,
                                                         porder["delivery_pickup_date"],
                                                         product_source="portal-recommend")

        # 获取购物车猜你喜欢
        perference_cart = GetPreferenceProducts().get_preference_products_cart(headers=ec_login_header)
        # 断言猜你喜欢必须有数据
        assert perference_cart["object"][
                   "total_count"] > 0, f'猜你喜欢数据返回不足{perference_cart}，请确认首页商品推荐是否有问题~'
        products = perference_cart["object"]["products"]

        # 商品信息断言、点击pdp、搜藏操作
        for index, product in enumerate(products) :
            CommonCheck().check_product_info(ec_login_header, product=product, category_type="others",
                                             filters="others", source="cart")
            # 点击商品进行搜藏
            CommCheckFunction().comm_set_favorites(headers=ec_login_header,
                                                   target_id=product["id"])
            if index == 5:
                break


        # 获取猜你喜欢下的商品进行加购
        CommCheckProductsWithCart().products_add_to_cart(ec_login_header,
                                                         products, porder["delivery_pickup_date"],
                                                         product_source="shopping-search")


    @weeeTest.mark.list( 'Regression', 'Smoke',  'Transaction')
    def test_catalogue_product_preference(self, ec_login_header):
        """ 分类页-加购弹出猜你喜欢商品验证流程 """
        # 获取用户的porder
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]
        # 获取分类页加购时猜你喜欢数据
        catalogue_perference = GetProductsPreference().get_products_preference(headers=ec_login_header,
                                                                               product_id=95442)
        assert catalogue_perference[
                   "object"] is not None, f'95442当前商品没有返回推荐数据，请确认{catalogue_perference["object"]}'
        assert catalogue_perference["object"][
                   "total_count"] > 0, f'95442当前商品没有返回推荐数据，请确认{catalogue_perference["object"]}'
        products = catalogue_perference["object"]["products"]
        # 商品信息断言、点击pdp、搜藏操作
        for index, product in enumerate(products):
            CommonCheck().check_product_info(product=product, category_type="others",
                                             filters="others", source="homepage", headers=ec_login_header)
            if index == 5:
                break

    def perference_product_assertion(self, products: list | Any, headers):
        for index, product in enumerate(products):

            assert "weeecdn" in product['img']
            assert product['name'] is not None
            assert product['price'] is not None
            assert product['sold_status'] == "available"
            assert str(product["id"]) in product['slug'], "这个链接返回的不是这个产品的链接，请确认~"
            # 断言如果是mkpl商品，商家信息不为空
            if product['biz_type'] == 'seller':
                assert product['vender_id'] is not None
                assert product['vender_info_view'] != {}
            if index == 3:
                break
        # 点击商品卡片,跳转
        CommCheckFunction().comm_check_pdp_link(products[0]["id"], products[0]['view_link'])
        # 点击商品进行搜藏
        CommCheckFunction().comm_set_favorites(headers=headers,
                                               target_id=random.choice(products)["id"])


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net', env='tb1')
