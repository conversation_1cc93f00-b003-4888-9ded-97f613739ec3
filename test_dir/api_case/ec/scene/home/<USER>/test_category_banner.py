# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_user.py
@Description    :
@CreateTime     :  2023/5/16 15:23
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 15:23
"""
from typing import Any

import weeeTest

from test_dir.api.ec.ec_item.category.api_catalogues import ApiCatalogues
from test_dir.api.ec.ec_item.search_v3.search_by_catalogue_content import SearchByCatalogueContent
from test_dir.api.ec.ec_mkt.banner.get_banner_list import GetBannerList
from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder
from test_dir.api_case.ec.common.comm_cms import CommCheckFunction


class TestCategoryBanner(weeeTest.TestCase):

    @weeeTest.mark.list('category_banner', 'Regression', 'Smoke',  'Transaction')
    def test_category_banner(self, ec_login_header):
        """ Banner-分类页轮播图验证流程 """
        # 获取用户的porder
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]
        # 根据销售组织，语言获取轮播图，待优化

        categories = ["trending", "new", "sale"]
        for item in categories:
            category = SearchByCatalogueContent().search_by_catalogue_content(headers=ec_login_header,
                                                                              filter_sub_category=item,
                                                                              date=porder["delivery_pickup_date"],
                                                                              zipcode=porder["zipcode"])
            assert category["object"] is not None
            # banner
            self.category_banner_assertion(category["object"]["contents"], headers=ec_login_header)

    @weeeTest.mark.list('category_banner_v2', 'Regression', 'Smoke',  'Transaction')
    def test_category_banner_v2(self, ec_login_header):
        """ Banner-分类页轮播图验证流程 """
        # 获取用户的porder
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]
        # 根据销售组织，语言获取轮播图，待优化
        # 2.获取首页分类,不包括特殊分类
        catalogue_home = ApiCatalogues().catalogue_home(headers=ec_login_header,
                                                        date=porder["delivery_pickup_date"],
                                                        zipcode=porder["zipcode"])
        assert catalogue_home["object"] is not None
        category_lists = [item['num'] for item in catalogue_home['object']['category_list'] if
                          item['type'] != 4 and item['num'] != "global"]

        categories = ["trending", "new", "sale"]
        category_lists += categories
        print(category_lists)

        for item in category_lists:
            # 获取分类banner
            category_banner = GetBannerList().get_category_banner_v2(headers=ec_login_header,
                                                                     category=item,
                                                                     zipcode=porder["zipcode"]
                                                                     )
            if category_banner["object"]["carouselInfoList"]:
                # banner
                self.category_banner_v2_assertion(category_banner["object"]["carouselInfoList"], headers=ec_login_header)

    def category_banner_assertion(self, contents: dict | Any, headers):

        for content in contents:
            if content["type"] == "carousel":
                data = content["data"]
                for carousel in data:
                    assert carousel['img_url'] is not None
                    assert carousel['url'] is not None
                    url = carousel['url']
                    # 并访问轮播图链接
                    CommCheckFunction().comm_check_link(url, headers)

    def category_banner_v2_assertion(self, contents: dict | Any, headers):

        for content in contents:
            if content["id"] is not None:
                assert content['type'] == "category"
                url = "https://www.sayweee.com" + content["url"]
                # 并访问轮播图链接
                CommCheckFunction().comm_check_link(url, headers=headers)
            assert content['img'] is not None
            assert content['img_url'] is not None
            # 并访问图片正常
            CommCheckFunction().comm_check_link(content['img'], headers=headers)
            CommCheckFunction().comm_check_link(content['img_url'], headers=headers)


