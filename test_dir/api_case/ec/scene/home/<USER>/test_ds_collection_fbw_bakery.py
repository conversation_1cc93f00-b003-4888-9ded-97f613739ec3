# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_user.py
@Description    :
@CreateTime     :  2023/5/16 15:23
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 15:23
"""

import weeeTest

from test_dir.api.ec.ec_item.ds_collection_rest.ds_collection_home import DsCollectionHome
from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder
from test_dir.api_case.ec.common.comm_check_products_with_cart import CommCheckProductsWithCart
from test_dir.api_case.ec.common.comm_cms import CommCheckFunction


class TestDsCollectionFbwBakery(weeeTest.TestCase):
    @weeeTest.mark.list('Regression', 'Smoke', 'Transaction')
    def test_ds_collection_fbw_bakery(self, ec_login_header):
        """ 推荐-FBW合集验证流程 """
        # 每日现做是面包+卤味
        # 获取用户的porder
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]
        ds_collection = DsCollectionHome().ds_collection_home(headers=ec_login_header,
                                                              scenes="fbwbakerycollection",
                                                              ds_url="cm_item_fbw_bakery")

        assert ds_collection["object"]['ds_collection_key_exist'] is True ,f'fbw_bakery合集数据异常，请确认{ds_collection}'
        assert ds_collection["object"][
                   'ds_collection_key'] == "cm_item_fbw_bakery", f'ds_collection_key:{ds_collection["object"]["ds_collection_key"]}'
        assert ds_collection["object"][
                   "total_count"] > 0, f'fbw_bakery合集数据异常，请确认{ds_collection}'
        # 验证点击more_link 跳转正常
        more_link = ds_collection["object"]["component_metadata"]["more_link"]
        CommCheckFunction().comm_check_link(more_link, headers=ec_login_header)
        assert "/promotion/collection/fbwbakerycollection" in more_link, f'fbw_bakery合集跳转链接不对：{more_link}'

        for index, item in enumerate(ds_collection["object"]["products"]):
            assert item["biz_type"] == "fbw"
            # 加购editor_pick下的商品
            CommCheckProductsWithCart().product_add_to_cart(headers=ec_login_header,
                                                            product=item,
                                                            porder_deal_date=porder["delivery_pickup_date"],
                                                            product_source="mweb_sayweee_collection-cm_item_list-fbwbakerycollection")

            if index == 6:
                break
