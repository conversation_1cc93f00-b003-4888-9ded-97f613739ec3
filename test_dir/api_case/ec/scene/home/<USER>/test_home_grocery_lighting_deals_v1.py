# # !/usr/bin/python3
# # -*- coding: utf-8 -*-
# """
# <AUTHOR>  <PERSON><PERSON><PERSON> <PERSON>
# @Version        :  V1.0.0
# ------------------------------------
# @File           :  test_user.py
# @Description    :
# @CreateTime     :  2023/5/16 15:23
# @Software       :  PyCharm
# ------------------------------------
# @ModifyTime     :  2023/5/16 15:23
# """
# import logging
# 
# import weeeTest
# from weeeTest import RequestHeader
# 
# from test_dir.api.ec.ec_mkt.activity.lighting_deals_v1 import LightingDeals
# from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder
# from test_dir.api.ec.ec_so.preorder.update_preorderline import UpdatePreOrderLine
# 
# 
# class TestHomeGroceryLightDealsV1(weeeTest.TestCase):
# 
#     @weeeTest.data.file(file_name='ec_item_data.json', return_type='dict')
#     @weeeTest.mark.list('Regression', 'tb1',  'Transaction')
#     @weeeTest.mark.skip("秒杀V1版本接口目前已废弃")
#     def test_home_grocery_lighting_deals_v1(self, *args):
#         """ 秒杀-生鲜秒杀V1接口验证流程 """
#         # 获取用户的porder
#         porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=RequestHeader.ec_login_header)["object"]
# 
#         sales_org_id = porder["sales_org_id"]
#         deal_date = porder["delivery_pickup_date"]
#         delivery_date = porder["delivery_pickup_date"]
#         zipcode = porder["zipcode"]
#         data = args[0]["category"]["search_by_catalogue"]
#         # 清除生鲜购物车
#         # RemoveAllProductsInCart().remove_all_products_in_grocery_cart(headers=headers)
#         # 获取首页生鲜秒杀
#         lighting_deals = LightingDeals().lighting_deals(headers=RequestHeader.ec_login_header, lang="en", date=porder["delivery_pickup_date"],
#                                                         limit=10)
#         assert lighting_deals["result"] is True
# 
#         # 进入秒杀详情
#         lightings = LightingDeals().lightings(headers=RequestHeader.ec_login_header, deal_id=porder["deal_id"],
#                                               zipcode=porder["zipcode"])
#         assert lightings["result"] is True
# 
#         # 判断是否有秒杀
#         if lightings["result"] is False:
#             logging.info("没有秒杀")
#         else:
# 
#             # 进入秒杀详情。判断开始秒杀是否有商品
#             if lightings["object"]["ongoing_products"]:
#                 count = 0
#                 # 加购秒杀商品
#                 for products in lightings["object"]["ongoing_products"]:
#                     # 判断是生鲜商品，再跳出循环
#                     if products["sold_status"] == "available":
#                         UpdatePreOrderLine().porder_items_v3(headers=RequestHeader.ec_login_header, product_id=products["id"],
#                                                              date=porder["delivery_pickup_date"],
#                                                              refer_type="normal", source="special"
#                                                              )
# 
#                         # 判断加购成功
#                         assert self.response["result"] is True
#                         # 判断加入生鲜购物车成功
#                         # CheckProductsAddedToCartSuccess().check_product_add_to_cart_success(headers=headers,
#                         #                                                                     cart_domain="grocery",
#                         #                                                                    product_id=products["id"])
#                         #
#                         # 商品太多，会循环太久，限制10次循环
#                         count += 1
#                         if count == 10:
#                             break
#             # 判断秒杀即将开始是否有商品
#             if lightings["object"]["begoing_products"]:
#                 count = 0
#                 for item in lightings["object"]["begoing_products"]:
#                     product_id = item["id"]
#                     # 设置秒杀提醒
#                     LightingDeals().lightning_deals_remind(headers=RequestHeader.ec_login_header, product_id=product_id, status="A")
#                     assert self.response["result"] is True
#                     # 取消秒杀提醒
#                     LightingDeals().lightning_deals_remind(headers=RequestHeader.ec_login_header, product_id=product_id, status="X")
#                     assert self.response["result"] is True
#                     count += 1
#                     # 商品太多，会循环太久，限制10次循环
#                     if count == 10:
#                         break
# 
#             # 判断首页是否有秒杀
#             if lighting_deals["object"]["total_count"] > 0:
#                 # 分享秒杀
#                 LightingDeals().special_share(headers=RequestHeader.ec_login_header, deal_id=porder["deal_id"])
#                 assert self.response["result"] is True
#             else:
#                 logging.info("首页还没有秒杀")
# 
# 
# if __name__ == '__main__':
#     weeeTest.main(base_url='https://api.tb1.sayweee.net', env='tb1')
