# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_user.py
@Description    :
@CreateTime     :  2023/5/16 15:23
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 15:23
"""
import time
from typing import Any

import weeeTest
from weeeTest import log

from test_dir.api.ec.ec_item.theme.api_themes import SearchThemes
from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder
from test_dir.api.ec.ec_so.preorder.update_preorderline import UpdatePreOrderLine
from test_dir.api.ec.ec_so.preorder.update_zipcode import UpdateZipcode
from test_dir.api_case.ec.common.comm_check_products_with_cart import CommCheckProductsWithCart
from test_dir.api_case.ec.common.comm_cms import CommCheckFunction
from test_dir.api_case.ec.common.remove_all_products_in_cart import RemoveAllProductsInCart


class TestHomeTopicTheme(weeeTest.TestCase):
    # @weeeTest.mark.list('Regression', 'Transaction',  'product', 'topic_theme')
    def test_000_home_topic_theme(self, cache, ec_login_header):
        """ Theme-social首页topic热门特辑验证流程 """
        # 接口有改动，先暂停执行
        # 去掉SMOKE，接口总是超时
        # 清除生鲜购物车
        RemoveAllProductsInCart().remove_all_products_in_grocery_cart(headers=ec_login_header)
        # 获取用户的porder
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]
        UpdateZipcode().update_zipcode_v3(ec_login_header)
        # 获取首页主题推荐数据
        # 有效主题列表查询 - social home页面调用
        topics_themes = SearchThemes().top_themes(headers=ec_login_header)
        assert topics_themes.get("object"), f"topics未返回任何主题，接口返回结果为：{topics_themes}"
        # 校验每个theme的链接
        for item in topics_themes["object"]:
            assert item["title"], f"item={item}"
            CommCheckFunction().comm_check_link(item['image'], headers=ec_login_header)
            for i, product_image in enumerate(item['product_images']):
                if i % 3 == 0:
                    CommCheckFunction().comm_check_link(product_image, headers=ec_login_header)

        # 进入每一个子主题进入校验
        topic_theme_ids = [item['id'] for item in topics_themes["object"]]
        cache.set("topic_theme_ids", topic_theme_ids)
        for theme_id in topic_theme_ids:
            theme_products = SearchThemes().themes_products_v4(
                headers=ec_login_header,
                data={"top_tag_id": theme_id}
            )
            # 这个接口极容易失败，增加一次重试
            if not isinstance(theme_products, dict):
                time.sleep(10)
                theme_products = SearchThemes().themes_products_v4(
                    headers=ec_login_header,
                    data={"top_tag_id": theme_id}
                )

            # 此处断言发生过报错
            log.debug("theme_products===>" + str(theme_products))
            assert theme_products["object"]["total_count"] > 0, f"theme_products={theme_products}, theme_id={theme_id}"
            assert theme_products["object"]["top_tag_id"] == theme_id
            assert theme_products["object"][
                "products"], f"该主题下没有商品，返回结果为：{theme_products}， theme_id={theme_id}"
            assert theme_products['object'][
                'sub_themes'], f"该主题没有子主题，返回结果为：{theme_products}，theme_id={theme_id}"

            available_product_ids = [[item['id'], item['min_order_quantity']] for item in
                                     theme_products['object']['products'] if item['sold_status'] == 'available']
            sub_theme_ids = [item['id'] for item in theme_products['object']['sub_themes']]
            cache.set(str(theme_id), sub_theme_ids)
            # 加购主题下的商品
            for index, product in enumerate(available_product_ids):
                UpdatePreOrderLine().porder_items_v3(
                    headers=ec_login_header,
                    product_id=product[0],
                    quantity=product[1]
                )
                assert self.response["result"] is True and len(
                    self.response["object"][
                        "updateItems"]) > 0, f"product id is {product[0]}, response is {self.response}"
                CommCheckProductsWithCart().check_product_exists_in_cart(
                    headers=ec_login_header,
                    cart_domain="grocery",
                    product_id=product[0]
                )
                if index == 1:
                    break
            # # 主题分享
            # # 主题页面分享
            # share = Share().collection_theme_share(
            #     headers=RequestHeader.ec_login_header,
            #     tag_id=theme_id
            # )
            # assert share["object"] is not None
            # self.themes_list_share_assertion(share["object"])
        # 子子主题校验, 放在用例中校验 test_home_topic_sub_theme_check， 否则用例时间太长

    # @weeeTest.mark.list('Regression',  'Transaction','product', 'topic_theme')
    def test_001_home_topic_sub_theme_check(self, cache, ec_login_header):
        # 去掉smoke，接口总是超时
        # 因接口有改动，先暂停执行
        t_t_ids = cache.get("topic_theme_ids", [])
        assert t_t_ids, f"父主题id为空：{t_t_ids}"
        for theme_id in t_t_ids:
            sub_theme_ids = cache.get(str(theme_id), [])
            assert sub_theme_ids, f"子主题id为空，{sub_theme_ids}"
            for index, sub_theme_id in enumerate(sub_theme_ids):
                sub_theme_products = {}
                try:
                    sub_theme_products = SearchThemes().themes_products_v4(
                        headers=ec_login_header,
                        data={
                            "top_tag_id": theme_id,
                            "l2_tag_id": sub_theme_id
                        }
                    )
                except Exception as e:
                    log.info("获取sub_theme_products失败" + str(e))
                if not isinstance(sub_theme_products, dict):
                    # 这个接口比较重，加上重试机制
                    time.sleep(20)
                    sub_theme_products = SearchThemes().themes_products_v4(
                        headers=ec_login_header,
                        data={
                            "top_tag_id": theme_id,
                            "l2_tag_id": sub_theme_id
                        }
                    )

                # 5.23日报错，增加调试信息 素芬已反应给开发，这个接口太重，后续会优化
                log.debug("sub_theme_products===>" + str(sub_theme_products))
                assert sub_theme_products.get('object', None), f"themes_products返回错误，结果为：{sub_theme_products}"
                assert sub_theme_products['object'].get('products', None), f"该主题下没有商品，返回结果为：{sub_theme_products}， top_tag_id={theme_id}, l2_tag_id={sub_theme_id}"
                available_sub_product_ids = [[item['id'], item['min_order_quantity']] for item in
                                             sub_theme_products['object']['products'] if
                                             item['sold_status'] == 'available']
                for index_i, product in enumerate(available_sub_product_ids):
                    UpdatePreOrderLine().porder_items_v3(
                        headers=ec_login_header,
                        product_id=product[0],
                        quantity=product[1]
                    )
                    assert self.response["result"] is True and len(
                        self.response["object"][
                            "updateItems"]) > 0, f"product id is {product[0]}, response is {self.response}"
                    CommCheckProductsWithCart().check_product_exists_in_cart(
                        headers=ec_login_header,
                        cart_domain="grocery",
                        product_id=product[0]
                    )
                    if index_i == 1:
                        break

                if index == 1:
                    break

    def themes_list_share_assertion(self, share: dict | Any, headers):
        share_content = share["share_content"]
        print("share_content_length===>", len(share_content))
        for content in share_content:
            assert content["share_img_url"] is not None
            assert content["title"] is not None
            assert content["link_url"] is not None
            assert content["language"] is not None
        assert share["show_language"] is True
        assert len(share["share_channels"]) > 0
        assert "copyLink" in share["share_channels"]
        assert "/promotion/theme_landing" in share["view_link"], f'当前theme 返回链接不正确，请确认{share["view_link"]}'
        # 验证点击view_link 跳转正常
        CommCheckFunction().comm_check_link(share["view_link"], headers=headers)
