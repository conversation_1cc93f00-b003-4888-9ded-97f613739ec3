import pytest
import weeeTest


class TestHomeFooter(weeeTest.TestCase):
    area_list = [
        "chinese",
        "japanese",
        "korean",
        "vietnamese",
        "filipino",
        "indian",
        "asian"
    ]

    @weeeTest.mark.list("not_regression")
    @pytest.mark.parametrize("area", area_list)
    def test_footer_grocery_delivery_area(self, area):
        """109147 pc footer 校验 """
        r = self._access_footer_area(area)
        assert area in r, f"地区不在在线商城的网页内容中，area={area}, r={r}"
        assert "Why shop at Weee!" in r, f"r={r}"


    def _access_footer_area(self, area: str):
        res = self.get(special_url="https://www.sayweee.com/en/groceries-online", url=f"/{area}")
        return res.text
