# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON> <PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_user.py
@Description    :
@CreateTime     :  2023/5/16 15:23
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 15:23
"""

import weeeTest

from test_dir.api.ec.ec_content.collection.get_collection_grocery_home import GetCollectionGroceryHome
from test_dir.api.ec.ec_content.page.qery_page_data import QueryPageData
from test_dir.api.ec.ec_promotion.promotion.query_promotion_info import QueryPromotionInfo
from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder
from test_dir.api_case.ec.common.common_check import CommonCheck


class TestCmsAcitivityCollection(weeeTest.TestCase):
    @weeeTest.data.file(file_name='ec_item_data.json', return_type='dict')
    # @weeeTest.mark.list('Regression', 'Smoke', 'Transaction','test_home_grocery_cms_collection')
    # 用例运行失败，需要恢复
    def test_home_grocery_cms_collection(self, *args, ec_login_header):
        """ 合集-CMS合集验证流程 """
        # 获取登录header
        # 获取用户的porder
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]

        sales_org_id = porder["sales_org_id"]
        deal_date = porder["delivery_pickup_date"]
        delivery_date = porder["delivery_pickup_date"]
        zipcode = porder["zipcode"]
        data = args[0]["category"]["search_by_catalogue"]
        cms_page = QueryPageData().query_page_data(headers=ec_login_header,
                                                   page_key='sale-Crazy8', page_type='7',
                                                   lang='en', sales_org_id=sales_org_id,
                                                   zipcode=zipcode)
        assert cms_page["result"] is True
        CommonCheck().check_cms_data_page(headers=ec_login_header, cms_data=cms_page["object"],
                                          porder=porder, page_key="sale-Crazy8")

        # 获取首页所有合集key
        GetCollectionGroceryHome().get_collection_grocery_home(headers=ec_login_header)
        # 获取cms页面数据源为killer deal
        event_promotion = QueryPromotionInfo().event_promotion_product(headers=ec_login_header,
                                                                       event_code="20240412_Crazy8",
                                                                       type_id=2,
                                                                       zipcode=porder["zipcode"],
                                                                       date=porder["delivery_pickup_date"]
                                                                       )
        assert event_promotion["object"][
                   "total_count"] > 0, f'cms页面数据源killer deal 返回为空，请确认{event_promotion["object"]}'
        # SearchByIdsV3().search_by_ids_cms_v3()

