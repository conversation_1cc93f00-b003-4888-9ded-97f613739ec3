# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_user.py
@Description    :
@CreateTime     :  2023/5/16 15:23
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 15:23
"""
from typing import Any

import weeeTest

from test_dir.api.ec.ec_growth.share import Share
from test_dir.api.ec.ec_item.theme.api_themes import SearchThemes
from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder
from test_dir.api.ec.ec_so.preorder.update_preorderline import UpdatePreOrderLine
from test_dir.api_case.ec.common.comm_check_products_with_cart import CommCheckProductsWithCart
from test_dir.api_case.ec.common.comm_cms import CommCheckFunction
from test_dir.api_case.ec.common.remove_all_products_in_cart import RemoveAllProductsInCart


class TestHomeTheme(weeeTest.TestCase):
    @weeeTest.mark.list('Regression', 'Transaction', 'product')
    def test_home_popular_collections_theme(self, ec_login_header):
        """ Theme-首页热门特辑验证流程 """
        # 去掉smoke标签，接口总是超时
        # 获取用户的porder
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]
        # 清空购物车
        RemoveAllProductsInCart().remove_all_products_in_grocery_cart(headers=ec_login_header)
        # 获取首页主题推荐数据
        # 有效主题列表查询 - home页面调用
        home_themes = SearchThemes().themes_v3(
            headers=ec_login_header, lang="en",
            date=porder["delivery_pickup_date"]
        )
        assert home_themes["object"]["themes"], f'首页未返回热门特辑数据，请确认{home_themes}'
        # 首页theme基础断言
        self.home_themes_assertion(themes=home_themes["object"]["themes"])
        # 首页theme 切换及商品加购
        self.themes_list_assertion(themes=home_themes["object"]["themes"], headers=ec_login_header)
        #主题页面分享
        share = Share().collection_theme_share(
            headers=ec_login_header,
            tag_id=home_themes["object"]["themes"][0]["id"]
        )
        assert share["object"] is not None
        self.themes_list_share_assertion(share["object"], headers=ec_login_header)

    def home_themes_assertion(self, themes: list):
        for theme in themes:
            assert theme["img"].startswith("https"), f"theme的img属性不正确，theme={theme}"
            assert theme["link_url"].startswith("https"), f"theme的img属性不正确，theme={theme}"
            assert theme["title"] is not None, f"theme和title为空， theme={theme}"
            assert theme["support_area"] is not None, f"theme和title为空， theme={theme}"
            assert theme["support_language"] is not None, f"theme和title为空， theme={theme}"

            assert "/promotion/theme_landing" in theme["link_url"], f'当前theme返回链接不正确，请确认{theme}'

    def themes_list_assertion(self, themes: list, headers):
        for theme in themes:
            # 访问首页各主题推荐
            themes_items = SearchThemes().themes_items_v3(headers=headers, tag_id=theme["id"])
            assert themes_items["object"] is not None, f'切换主题失败，请确认{themes_items["object"]}'
            # 现在没有object.total_count这个属性
            # assert themes_items["object"]["total_count"] > 0, f"请确认，theme_item={themes_items}"
            assert themes_items["object"]["current_tag_id"] == theme[
                "id"], f"theme id={theme['id']}与当前切换的id不一致，current_tag_id={themes_items['object']['current_tag_id']}"
            theme_product_map = themes_items["object"]["theme_product_map"]
            assert theme_product_map, f"theme_product_map为空，请检查数据，themes_items={themes_items}"

            # 校验theme_product_map数据
            available_products = []
            for _, v in theme_product_map.items():
                for item in v:
                    # assert CommonCheck.list_check(['tag_list', 'view_link', 'id'], item.keys()), f"item={item}"
                    # CommCheckFunction().comm_check_link(item['view_link'])
                    if item['sold_status'] == 'available':
                        available_products.append([item['id'], item['min_order_quantity']])

            theme_tree = themes_items["object"]["theme_tree"]
            assert theme_tree["id"] == theme["id"], f"当前theme id不正确，theme_tree={theme_tree}, theme={theme}"
            # 断言主题下存在子分类
            assert len(theme_tree["sub_themes"]) > 0, f"sub_themes 不存在，请确认"
            # 加购主题下的商品
            for index, product in enumerate(available_products):
                UpdatePreOrderLine().porder_items_v3(
                    headers=headers,
                    product_id=product[0],
                    quantity=product[1]
                )
                assert self.response["result"] is True and len(
                    self.response["object"][
                        "updateItems"]) > 0, f"product id is {product[0]}, response is {self.response}"
                CommCheckProductsWithCart().check_product_exists_in_cart(
                    headers=headers,
                    cart_domain="grocery",
                    product_id=product[0]
                )
                if index == 3:
                    break

    def themes_list_share_assertion(self, share: dict | Any, headers):
        share_content = share["share_content"]
        print("share_content_length===>", len(share_content))
        for content in share_content:
            assert content["share_img_url"] is not None
            assert content["title"] is not None
            assert content["link_url"] is not None
            assert content["language"] is not None
        assert share["show_language"] is True
        assert len(share["share_channels"]) > 0
        assert "copyLink" in share["share_channels"]
        assert "/promotion/theme_landing" in share["view_link"], f'当前theme 返回链接不正确，请确认{share["view_link"]}'
        # 验证点击view_link 跳转正常
        CommCheckFunction().comm_check_link(share["view_link"], headers=headers)


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net', env='tb1')
