#!/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
@File           :  test_popup_simple.py
@Description    :  弹窗功能测试用例
@CreateTime     :  2023/5/30 11:06
@ModifyTime     :  2024/3/21
"""

import pytest
import weeeTest
from weeeTest import log
from test_dir.api.ec.ec_activity.popup import Popup
from test_dir.api_case.ec.common.comm_cms import CommCheckFunction
from typing import Dict, List


class TestPopUpSimple(weeeTest.TestCase):
    """弹窗功能测试类"""
    
    TEST_PAGES = [
        "page_home",
        "page_mkpl_waterfall",
        "rtg_home"
    ]

    def setup_method(self):
        """测试前置设置"""
        self.popup = Popup()
        self.comm_check = CommCheckFunction()

    @weeeTest.mark.list('Regression', 'Smoke', 'Transaction', 'product', 'test_popup')
    def test_popup(self, ec_login_header):
        """
        测试各页面Popup功能
        
        Args:
            ec_login_header: 登录用户的请求头

        验证点:
        1. 预检查接口返回正常
        2. 每个页面的弹窗配置正确
        3. 弹窗成功回调正常
        4. 弹窗链接可访问
        """
        # 预检查弹窗配置
        self._verify_popup_precheck(ec_login_header)
        
        # 测试每个页面的弹窗
        for page in self.TEST_PAGES:
            self._test_page_popup(page, ec_login_header)

    def _verify_popup_precheck(self, headers: Dict) -> List[str]:
        """
        验证弹窗预检查接口
        
        Args:
            headers: 请求头

        Returns:
            配置的页面列表
        """
        popup_page = self.popup.popup_precheck(headers=headers)
        assert popup_page["result"] is True, "弹窗预检查接口调用失败"
        pages = popup_page["object"]
        assert isinstance(pages, list), "返回的页面配置格式错误"
        return pages

    def _test_page_popup(self, page: str, headers: Dict):
        """
        测试单个页面的弹窗功能
        
        Args:
            page: 页面标识
            headers: 请求头
        """
        log.info(f"开始测试页面 {page} 的弹窗")
        
        try:
            active_pop = self.popup.activity_popup(headers=headers, page=page)
            assert active_pop["result"] is True, f"页面 {page} 弹窗接口调用失败"

            if active_pop["object"]["popup_exist"]:
                popup_info = active_pop["object"]["popup_info"]
                self._verify_popup_info(popup_info, headers)
                self._verify_popup_success(popup_info["popup_id"], headers)
            else:
                log.info(f"页面 {page} 未配置弹窗")
        except Exception as e:
            log.error(f"测试页面 {page} 弹窗时发生错误: {str(e)}")
            raise

    def _verify_popup_info(self, popup_info: Dict, headers: Dict):
        """
        验证弹窗信息的完整性
        
        Args:
            popup_info: 弹窗信息
            headers: 请求头
        """
        assert "popup_id" in popup_info, "弹窗信息缺少popup_id"
        assert "content_img_url" in popup_info, "弹窗信息缺少图片URL"
        
        if popup_info.get("link_url"):
            log.info(f"验证弹窗跳转链接: {popup_info['link_url']}")
            self.comm_check.comm_check_link(popup_info["link_url"], headers=headers)

    def _verify_popup_success(self, popup_id: str, headers: Dict):
        """
        验证弹窗成功回调
        
        Args:
            popup_id: 弹窗ID
            headers: 请求头
        """
        popup_success = self.popup.popup_success(headers=headers, popup_id=int(popup_id))
        assert popup_success["object"] == "success", "弹窗成功回调失败"


