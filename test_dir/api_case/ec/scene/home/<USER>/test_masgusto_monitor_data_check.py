# 1. 获取销售组织   --> 使用静态还是动态数据，与zipcode关系
## 1.1 /ec/item/common/all_sales_org  但数据不包含zipcode
# 2. 获取语言 --> 接口获取还是静态数据？
# 3. store:
## 3.1 /ec/item/store/list/all
# 4. 断言
# banner, component, 数据推荐？


import copy
import time
import uuid

import pytest
import weeeTest
# from allure_commons._allure import step
from allure import step
from pytest_assume.plugin import assume
from weeeTest import log

from test_dir.api.ec.ec_content.cms.cms_sayweee_mobile_home import CmsSayweeeHome as CmsSayweeeMobileHome
from test_dir.api.ec.ec_item.category.api_catalogues import ApiCatalogues
from test_dir.api.ec.ec_item.content.get_waterfall import GetHomeWaterfall
from test_dir.api.ec.ec_item.product_v2.get_products_modules import GetProductsModules
from test_dir.api.ec.ec_item.product_v2.get_products_modules_second import GetProductsModulesSecond
from test_dir.api.ec.ec_item.recommend.get_preference_products import GetPreferenceProducts
from test_dir.api.ec.ec_item.search_v2.search_by_cuisine import SearchByCuisine
from test_dir.api.ec.ec_item.search_v3.search_by_catalogue import SearchByCatalogue
from test_dir.api.ec.ec_item.search_v3.search_by_catalogue_content import SearchByCatalogueContent
from test_dir.api.ec.ec_item.store.api_store import ApiStore
from test_dir.api.ec.ec_item.theme.api_themes import SearchThemes
from test_dir.api.ec.ec_item.top_ranking.api_top_ranking import ApiTopRanking
from test_dir.api.ec.ec_mkt.activity.activity_trade_in import ActivityTradeIn
from test_dir.api.ec.ec_mkt.banner.get_banner_list import GetBannerList
from test_dir.api.ec.ec_mkt.preference.get_preference import GetPreference
from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder
from test_dir.api.ec.ec_social.post_info.post_info import PostInfo
from test_dir.api.ec.ec_social.recommend_post_info.recommend_portal import RecommendPortal
from test_dir.api.ec.ec_social.review_info.review_info import ReviewInfo
from test_dir.api.ec.ec_social.event.event import Event
from test_dir.api_case.ec.common.comm_cms import CommCheckFunction
from test_dir.api_case.ec.common.set_user_porder import SetUserPorder

sales_data = [
    (101, "94501"),
    (102, "90001"),
    (103, "94586"),
    (107, "10026"),
    (116, "02535")
]

store_info = [(1, 'cn'), (2, 'ja'), (3, 'ko'), (5, 'vn'), (6, 'ph'), (9, 'in'), (7, 'us')]


# "zh", "zh-Hant", "ja",

class TestMasGustoDataCheck(weeeTest.TestCase):
    @pytest.mark.parametrize("language", ["zh", "zh-Hant", "ja", "ko", "vi"])
    @pytest.mark.parametrize('sales_id, zipcode', sales_data)
    @pytest.mark.masgusto_monitor
    # @pytest.mark.parametrize("monitor_header", [[]], indirect=True)
    def test_masgusto_data_check_non_en(self, sales_id, zipcode, language, monitor_header):
        _monitor_header = copy.deepcopy(monitor_header)
        """masgusto-非英文下切换各销售组织+语言"""

        # 操作步骤：
        # 1. 切销售组织，更新zipcode
        # 1.1 更新header里的zipcode
        # 2. 获取最新日期
        # 2.1 改porder 及header的日期
        # 3. 循环切语言
        # 3.1 更新header的语言
        # 执行case 

        SetUserPorder.set_user_header_porder(headers=_monitor_header, zipcode=zipcode, language=language)
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=_monitor_header)["object"]
        deal_date = porder.get("delivery_pickup_date")
        deal_id = porder.get("deal_id")
        sales_id = porder.get("sales_org_id")
        zipcode = porder.get("zipcode")
        # 如果您需要对这些变量进行断言，确保它们不是 None 或者提供默认值
        assert deal_date is not None, "delivery_pickup_date is missing in the response"
        assert deal_id is not None, "deal_id is missing in the response"
        assert sales_id is not None, "sales_org_id is missing in the response"
        assert zipcode is not None, "zipcode is missing in the response"
        self.data_check_testcase(headers=_monitor_header, sales_id=sales_id, zipcode=zipcode,
                                 deal_date=deal_date, language=language, deal_id=deal_id)

    @pytest.mark.parametrize("language", ["en"])
    @pytest.mark.parametrize('sales_id, zipcode', sales_data)
    @pytest.mark.masgusto_monitor
    def test_masgusto_data_check_en(self, sales_id, zipcode, language, monitor_header):
        """masgusto-英文下切换各销售组织+语言+store"""
        # 操作步骤：
        # 1. 切语言为en
        # 1.1 改header里的语言
        # 2. 切销售组织，改zipcode
        # 2.1 改header里的zipcode
        # 3 获取最新日期
        # 3.1 更新header及porer的日期
        # 4 获取store
        # 5 循环切换改store
        # 4.1 改header里的store
        # 执行case

        _monitor_header = copy.deepcopy(monitor_header)
        SetUserPorder.set_user_header_porder(headers=_monitor_header, zipcode=zipcode, language=language)
        if language == 'en':
            # 4.获取store id
            store_res = ApiStore().store_list(headers=_monitor_header, zipcode=zipcode, lang=language)
            store = [(item['store_id'], item['store_key']) for item in store_res['object']]
            for store_id, store_key in store:
                # 4.1 更新store
                ApiStore().store_select(headers=_monitor_header, store_id=store_id, zipcode=int(zipcode))
                # 4.2 更新header里的store
                _monitor_header.update({
                    "Weee-Store": store_key
                })

                # 获取porder
                _porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=_monitor_header)
                pytest.assume(_porder.get("result") is True)
                # assert self.response["result"] is True
                porder = _porder.get("object")
                deal_date = porder.get("delivery_pickup_date")
                deal_id = porder.get("deal_id")
                sales_id = porder.get("sales_org_id")
                zipcode = porder.get("zipcode")

                # 如果您需要对这些变量进行断言，确保它们不是 None 或者提供默认值
                assert deal_date is not None, "delivery_pickup_date is missing in the response"
                assert deal_id is not None, "deal_id is missing in the response"
                assert sales_id is not None, "sales_org_id is missing in the response"
                assert zipcode is not None, "zipcode is missing in the response"

                self.data_check_testcase(headers=_monitor_header, sales_id=sales_id, zipcode=zipcode,
                                         deal_date=deal_date, language=language, deal_id=deal_id,
                                         store_key=store_key)

    def data_check_testcase(self, headers, sales_id, zipcode, language, deal_date, deal_id,
                            store_key: str = None):
        # 验证首页banner数据
        carousel_banner = GetBannerList().get_banner_list(headers=headers, type="carousel",
                                                          dataobject_key="ds_main_banner",
                                                          sales_org_id=sales_id,
                                                          zipcode=zipcode, lang=language,
                                                          date=deal_date
                                                          )
        self.home_banner_assert(headers=headers, content=carousel_banner, sales_id=sales_id, zipcode=zipcode,
                                language=language, store_key=store_key)

        # # 验证首页新人专区数据 basket starter
        # 放在常规case 验证
        # basket_starter = DsCollectionHome().ds_collection_home(headers=headers,
        #                                                        scenes="basketstarter",
        #                                                        lang=language,
        #                                                        ds_url="cm_item_basket_starter"
        #                                                        )
        # self.home_basket_starter_assert(content=basket_starter, sales_id=sales_id, zipcode=zipcode,
        #                                 language=language, store_key=store_key)

        # 验证首页特殊分类-新品推荐数据
        home_new_category = SearchByCatalogue().search_by_catalogue(headers=headers, zipcode=zipcode, lang=language,
                                                                    sales_org_id=sales_id, date=deal_date,
                                                                    filter_sub_category="new",
                                                                    dataobject_key="ds_item_new")
        self.home_new_category_assert(headers=headers, content=home_new_category, sales_id=sales_id, zipcode=zipcode,
                                      language=language, store_key=store_key)
        # 验证首页特殊分类-人气热卖推荐数据
        home_trending_category = SearchByCatalogue().search_by_catalogue(headers=headers, zipcode=zipcode,
                                                                         lang=language,
                                                                         sales_org_id=sales_id, date=deal_date,
                                                                         filter_sub_category="trending",
                                                                         dataobject_key="ds_item_trending")
        self.home_trending_category_assert(headers=headers, content=home_trending_category, sales_id=sales_id,
                                           zipcode=zipcode,
                                           language=language, store_key=store_key)
        # 验证首页特殊分类-特价分类推荐数据
        home_sale_category = SearchByCatalogue().search_by_catalogue(headers=headers, zipcode=zipcode, lang=language,
                                                                     sales_org_id=sales_id, date=deal_date,
                                                                     filter_sub_category="sale",
                                                                     dataobject_key="ds_item_sale")
        self.home_sale_category_assert(headers=headers, content=home_sale_category, sales_id=sales_id, zipcode=zipcode,
                                       language=language, store_key=store_key)

        # 验证首页review数据
        review_home = ReviewInfo().social_review_list(headers=headers, zipcode=zipcode, lang=language,
                                                      date=deal_date, dataobject_key="ds_post_list")
        self.home_review_assert(headers=headers, content=review_home, sales_id=sales_id, zipcode=zipcode,
                                language=language, store_key=store_key)

        # 验证editor picks数据,中文不展示
        if language not in ("zh", "zh-Hant") or sales_id not in (3, 16):
            editors_pick = SearchByCuisine().search_by_cuisine_home_page(headers=headers, lang=language,
                                                                         dataobject_key="ds_item_editors_pick")
            self.home_editors_pick_assert(headers=headers, content=editors_pick, sales_id=sales_id, zipcode=zipcode,
                                          language=language, store_key=store_key)

        # 首页所有组件数据（mobile）
        component_data = CmsSayweeeMobileHome().cms_sayweee_mobile_home(
            headers=headers,
            zipcode=zipcode,
            sales_org_id=sales_id
        )
        self.home_component_assert(headers=headers, content=component_data, sales_id=sales_id, zipcode=zipcode,
                                   language=language, store_key=store_key)

        # 验证首页猜你喜欢数据
        preference_home = GetPreference().get_preference(headers=headers, zipcode=zipcode, lang=language,
                                                         date=deal_date, dataobject_key="ds_item_perference")
        self.home_preference_assert(headers=headers, content=preference_home, sales_id=sales_id, zipcode=zipcode,
                                    language=language, store_key=store_key)

        # 验证首页waterfall数据
        random_uuid = uuid.uuid4()
        recommend_session = str(random_uuid)
        waterfall = {}
        contents = []
        for i in range(2):
            waterfall = GetHomeWaterfall().get_home_waterfall(
                headers=headers,
                recommend_session=recommend_session,
                page_num=i + 1)
            if waterfall.get('object').get('contents'):
                contents.append(waterfall.get('object').get('contents'))
            else:
                log.info(f"第{i + 1}页没有waterfall数据")
                break
        waterfall['object']['contents'] = [items for content in contents for items in content]
        self.home_waterfall_assert(headers=headers, content=waterfall, sales_id=sales_id, zipcode=zipcode,
                                   language=language, store_key=store_key)

        # 验证首页热门特辑
        food_inspiration = RecommendPortal().recommend_portal(headers=headers,
                                                              lang=language,
                                                              date=deal_date
                                                              )
        self.home_food_inspiration_assert(headers=headers, content=food_inspiration, sales_id=sales_id, zipcode=zipcode,
                                          language=language, store_key=store_key)

        # 验证PDP相关商品数据返回
        products_modules = GetProductsModules().get_products_modules(headers=headers,
                                                                     product_id=1380)
        self.pdp_recommend_assert(headers=headers, content=products_modules, sales_id=sales_id, zipcode=zipcode,
                                  language=language, store_key=store_key)

        # 验证pdp晒单数据
        pdp_review = ReviewInfo().query_review_list(headers=headers,
                                                    product_id=1380)
        self.pdp_review_assert(headers=headers, content=pdp_review, sales_id=sales_id, zipcode=zipcode,
                               language=language, store_key=store_key)

        # 验证pdp视频数据
        pdp_video = PostInfo().post_pdp_video(headers=headers,
                                              product_id=1380, type="video")
        self.pdp_video_assert(headers=headers, content=pdp_video, sales_id=sales_id, zipcode=zipcode,
                              language=language, store_key=store_key)

        # 验证PDP waterfall数据
        pdp_waterfall = GetProductsModulesSecond().recently_view_waterfall(headers=headers,
                                                                           recommend_session=recommend_session,
                                                                           product_id=1380)
        self.pdp_waterfall_assert(headers=headers, content=pdp_waterfall, sales_id=sales_id, zipcode=zipcode,
                                  language=language, store_key=store_key)

        # 验证购物车换购数据
        trade_in = ActivityTradeIn().activity_trade_in(headers=headers,
                                                       type="normal",
                                                       deal_id=deal_id,
                                                       delivery_sale_org_id=sales_id
                                                       )
        self.cart_trade_in_assert(headers=headers, content=trade_in, sales_id=sales_id, zipcode=zipcode,
                                  language=language, store_key=store_key)

        # 验证购物车猜你喜欢
        preference_cart = GetPreferenceProducts().get_preference_products_cart(headers=headers)

        self.cart_preference_assert(headers=headers, content=preference_cart, sales_id=sales_id,
                                    zipcode=zipcode,
                                    language=language, store_key=store_key)

        # # 验证凑单页面折扣专区数据
        # item_tab = ["deals", "featured"]
        deals_more_products = ActivityTradeIn().item_activity_shop_more_products(headers=headers,
                                                                                 tab="deals")
        self.cart_recommend_assert(headers=headers, content=deals_more_products, sales_id=sales_id, zipcode=zipcode,
                                   language=language, store_key=store_key)
        # 验证凑单页面精选专区数据
        featured_more_products = ActivityTradeIn().item_activity_shop_more_products(headers=headers,
                                                                                    tab="featured")
        self.cart_recommend_assert(headers=headers, content=featured_more_products, sales_id=sales_id, zipcode=zipcode,
                                   language=language, store_key=store_key)

        # 验证topx数据
        top_ranking_list = ApiTopRanking().top_ranking_list(headers=headers, catalogue_num="trending")

        self.topx_assert(headers=headers, content=top_ranking_list, sales_id=sales_id, zipcode=zipcode,
                         language=language, store_key=store_key)

        # 验证社区视频数据
        social_recommend = RecommendPortal().social_recommended(headers=headers,
                                                                recommend_session=str(uuid.uuid4())
                                                                )
        self.social_video_assert(headers=headers, content=social_recommend, sales_id=sales_id, zipcode=zipcode,
                                 language=language, store_key=store_key)
        # # 验证社区下topic 数据
        # topics_themes = SearchThemes().top_themes(headers=headers)
        # self.social_topic_assert(headers=headers, content=topics_themes, sales_id=sales_id, zipcode=zipcode,
        #                          language=language, store_key=store_key)
        # # 验证社区下Recipes 数据
        # topics_themes = SearchThemes().top_themes(headers=headers)
        # self.social_topic_assert(headers=headers, content=topics_themes, sales_id=sales_id, zipcode=zipcode,
        #                          language=language, store_key=store_key)
        #
        # # 验证社区下event 数据
        # event = Event().query_event_list(headers=headers)
        # self.social_event_assert(headers=headers, content=event, sales_id=sales_id, zipcode=zipcode,
        #                          language=language, store_key=store_key)

        # 验证首页分类组件数据
        catalogue_home = ApiCatalogues().catalogue_home(headers=headers,
                                                        zipcode=zipcode, lang=language,
                                                        date=deal_date)
        self.home_category_assert(headers=headers, content=catalogue_home, sales_id=sales_id, zipcode=zipcode,
                                  language=language, store_key=store_key)
        category_lists = catalogue_home.get("object").get("category_list")
        # 筛选出满足条件的分类-普通分类
        normal_category_lists = [
            category_list for category_list in category_lists
            if category_list['type'] == 0 and category_list['num'] != "global"
        ]
        # 验证分类页普通分类-数据
        for category in normal_category_lists:
            # 访问普通分类并返回
            # normal_category = None
            if sales_id in (3, 16, 24, 29, 36):
                # MO/MOF zipcode
                normal_category = SearchByCatalogueContent().search_by_catalogue_content(
                    headers=headers, zipcode=zipcode, lang=language, date=deal_date,
                    filter_sub_category=category["num"], filters={"delivery_type": "delivery_type_fbw"})
            else:
                normal_category = SearchByCatalogueContent().search_by_catalogue_content(
                    headers=headers, zipcode=zipcode, lang=language, date=deal_date,
                    filter_sub_category=category["num"], filters={"delivery_type": "delivery_type_local"})

            self.category_page_assert(headers=headers, content=normal_category, category=category["num"],
                                      sales_id=sales_id, zipcode=zipcode,
                                      language=language, store_key=store_key)
        # 验证分类页pantry 数据
        if sales_id in (4, 8, 10, 13, 14, 15, 17, 18, 19, 20, 21, 23, 27, 28, 29):
            # 验证分类页特殊分类-特价分类推荐数据
            sale_category = SearchByCatalogueContent().search_by_catalogue_content(
                headers=headers, zipcode=zipcode, lang=language, date=deal_date,
                filter_sub_category="sale", filters={"delivery_type": "delivery_type_pantry"})

            contents = sale_category.get('object').get('contents')
            total_count = sale_category.get('object').get('total_count')
            categories = sale_category.get('object').get('categories')
            assert contents, f"sales_id={sales_id}, zipcode={zipcode}, lang={language}, store_key={store_key},分类sale pantry数据返回异常,{headers}"
            assert categories, f"sales_id={sales_id}, zipcode={zipcode}, lang={language}, store_key={store_key},分类sale pantry数据返回异常,{headers}"
            assert total_count > 0, f"sales_id={sales_id}, zipcode={zipcode}, lang={language}, store_key={store_key},分类sale pantry数据返回异常,{headers}"

        # 验证分类页特殊分类-新品推荐数据
        new_category = SearchByCatalogueContent().search_by_catalogue_content(
            headers=headers, zipcode=zipcode, lang=language, date=deal_date,
            filter_sub_category="new")
        self.category_page_assert(headers=headers, content=new_category, category="new",
                                  sales_id=sales_id, zipcode=zipcode,
                                  language=language, store_key=store_key)

        # 验证分类页特殊分类-人气热卖推荐数据
        trending_category = SearchByCatalogueContent().search_by_catalogue_content(
            headers=headers, zipcode=zipcode, lang=language, date=deal_date,
            filter_sub_category="trending")
        self.category_page_assert(headers=headers, content=trending_category, category="trending",
                                  sales_id=sales_id, zipcode=zipcode,
                                  language=language, store_key=store_key)

        # 验证分类页特殊分类-特价分类推荐数据
        sale_category = SearchByCatalogueContent().search_by_catalogue_content(
            headers=headers, zipcode=zipcode, lang=language, date=deal_date,
            filter_sub_category="sale")
        self.category_page_assert(headers=headers, content=sale_category, category="sale",
                                  sales_id=sales_id, zipcode=zipcode,
                                  language=language, store_key=store_key)

        # 验证支付结算（拉起结算到待支付即可）

    def home_banner_assert(self, headers, content, sales_id, zipcode, language, store_key: str = None):
        # 首页主banner接口断言
        with assume, step(
                f"检查首页主banner数据， sales_id={sales_id}, zipcode={zipcode}, lang={language}, store_key={store_key}"):
            if isinstance(content, dict) and content.get('object'):
                assert content.get('result') and content.get('object').get('carousel') and content.get(
                    'message_id') == '10000', f'获取carousel banner失败，carousel_banner={content}'
                for carousel in content.get('object').get('carousel'):
                    CommCheckFunction().comm_check_link(
                        carousel.get("url"),
                        headers=headers), f'获取carousel banner失败，carousel_banner={content},{headers}'
            else:
                assert False, f"banner数据返回不正确，carousel_banner={content},{headers}"

    def home_component_assert(self, headers, content, sales_id, zipcode, language, store_key: str = None):
        # 首页全部组件接口断言
        with assume, step(
                f"检查首页全部组件数据， sales_id={sales_id}, zipcode={zipcode}, lang={language}, store_key={store_key}"):
            if isinstance(content, dict):
                object_layout = content.get('object').get('layout')
                sections = object_layout.get('sections')
                assert sections, f"未获取component数据，component_data={content},{headers}"
                assert sections[0].get('components'), f"未获取component数据，component_data={content},{headers}"
            else:
                assert False, f"组件数据返回不正确，component_data={content},{headers}"

    def home_preference_assert(self, headers, content, sales_id, zipcode, language, store_key: str = None):
        # 首页猜你喜欢接口断言
        with assume, step(
                f"检查首页猜你喜欢数据， sales_id={sales_id}, zipcode={zipcode}, lang={language}, store_key={store_key}"):
            if isinstance(content, dict):

                products = content.get('object').get('products')
                assert products, f"猜你喜欢商品数<=0, preference_home={content},{headers}"
            else:
                assert False, f"猜你喜欢数据返回不正确，preference_home={content},{headers}"

    def home_food_inspiration_assert(self, headers, content, sales_id, zipcode, language, store_key: str = None):
        # 首页热门特辑接口断言
        with assume, step(
                f"检查首页热门特辑推荐数据， sales_id={sales_id}, zipcode={zipcode}, lang={language}, store_key={store_key}"):
            if isinstance(content, dict):
                object_list = content.get('object').get('list')
                assert object_list, f"未获得推荐数据，food_inspiration={content},{headers}"
            else:
                assert False, f"推荐数据返回不正确，food_inspiration={content},{headers}"

    def home_waterfall_assert(self, headers, content, sales_id, zipcode, language, store_key: str = None):
        # 首页waterfall接口断言
        with assume, step(
                f"检查首页waterfall， sales_id={sales_id}, zipcode={zipcode}, lang={language}, store_key={store_key}"):
            if isinstance(content, dict):
                object_contents = content.get('object').get('contents')
                assert object_contents and len(object_contents) >= 5, f"首页waterfall<=5, waterfall={content},{headers}"
            else:
                assert False, f"首页waterfall数据返回不正确，waterfall={content},{headers}"

    def home_category_assert(self, headers, content, sales_id, zipcode, language, store_key: str = None):
        # 首页分类组件接口断言
        with assume, step(
                f"检查首页分类组件， sales_id={sales_id}, zipcode={zipcode}, lang={language}, store_key={store_key}"):
            if isinstance(content, dict):
                object_content = content.get('object')
                if object_content:
                    category_lists = object_content.get('category_list')
                    assert category_lists and len(
                        category_lists) > 0, f"首页分类{category_lists}返回没有数据，请确认,{headers}"
                    # 断言global+ 分类一直存在
                    assert any(item.get('num') == "global" for item in category_lists), f"全球分类不存在,{headers}"
                    if sales_id == 4:
                        # 断言4销售组织一定存在酒分类
                        assert any(item.get('num') == "alcohol" for item in category_lists), f"酒分类不存在,{headers}"
                    if sales_id not in (3, 16, 36):
                        # 断言非直邮地区or MOF，必须存在水果分类
                        assert any(item.get('num') == "fruits" for item in
                                   category_lists), f"水果分类不存在， category_list={category_lists},{headers}"
                else:
                    assert False, f"首页分类组件object为空,{headers}"
            else:
                assert False, f"首页分类组件数据返回不正确，waterfall={content},{headers}"

    def home_new_category_assert(self, headers, content, sales_id, zipcode, language, store_key: str = None):
        # 首页新品分类接口断言
        with assume, step(
                f"检查首页特价分类， sales_id={sales_id}, zipcode={zipcode}, lang={language}, store_key={store_key}"):
            if isinstance(content, dict):
                object_content = content.get('object')
                if object_content:
                    products = object_content.get('products')
                    search_catalogue_num = object_content.get('search_catalogue_num')
                    assert products and len(products) >= 5, f"这个new分类返回数据异常，请确认,{object_content},{headers}"
                    assert search_catalogue_num == "new", f"这个new分类返回数据异常，请确认,{object_content},{headers}"
                else:
                    assert False, f"首页新品分类object为空,{headers}"
            else:
                assert False, f"首页新品分类数据返回不正确{content},{headers}"

    def home_trending_category_assert(self, headers, content, sales_id, zipcode, language, store_key: str = None):
        # 首页人气热卖分类接口断言
        with assume, step(
                f"检查首页人气热卖， sales_id={sales_id}, zipcode={zipcode}, lang={language}, store_key={store_key}"):
            if isinstance(content, dict):
                object_content = content.get('object')
                if object_content:
                    products = object_content.get('products')
                    search_catalogue_num = object_content.get('search_catalogue_num')
                    assert products and len(
                        products) >= 5, f"这个trending分类返回数据异常，请确认,{object_content},{headers}"
                    assert search_catalogue_num == "trending", f"这个trending分类返回数据异常，请确认,{object_content},{headers}"
                else:
                    assert False, f"首页人气热卖object为空,{headers}"
            else:
                assert False, f"首页人气热卖数据返回不正确{content},{headers}"

    def home_sale_category_assert(self, headers, content, sales_id, zipcode, language, store_key: str = None):
        # 首页特价分类接口断言
        with assume, step(
                f"检查首页特价分类， sales_id={sales_id}, zipcode={zipcode}, lang={language}, store_key={store_key}"):
            if isinstance(content, dict):
                object_content = content.get('object')
                if object_content:
                    # 使用.get()方法和提供默认值来避免KeyError
                    products = object_content.get('products')
                    search_catalogue_num = object_content.get('search_catalogue_num')
                    # 确保products是一个列表且长度大于5
                    assert isinstance(products, list) and len(
                        products) >= 5, f"这个sale分类返回数据异常，请确认,{object_content},{headers}"
                    # 检查search_catalogue_num是否为"sale"
                    assert search_catalogue_num == "sale", f"这个sale分类返回数据异常，请确认,{object_content},{headers}"
                else:
                    assert False, f"首页特价分类object为空,{headers}"
            else:
                assert False, f"首页特价分类数据返回不正确{content},{headers}"

    def home_editors_pick_assert(self, headers, content, sales_id, zipcode, language, store_key: str = None):
        # 首页editors_pick组件接口断言
        with assume, step(
                f"检查首页editors_pick， sales_id={sales_id}, zipcode={zipcode}, lang={language}, store_key={store_key}"):
            if isinstance(content, dict):
                object_content = content.get('object')
                if object_content:
                    total_count = object_content.get('total_count')
                    assert total_count > 0, f'首页editors_pick没有返回数据{object_content},{headers}'
                else:
                    assert False, f"首页editors_pick的object为空,{headers}"
            else:
                assert False, f"首页editors_pick数据返回不正确{content},{headers}"

    def home_basket_starter_assert(self, headers, content, sales_id, zipcode, language, store_key: str = None):
        # 首页新人专区(basket_starter)组件接口断言
        with assume, step(
                f"检查首页editors_pick， sales_id={sales_id}, zipcode={zipcode}, lang={language}, store_key={store_key}"):
            if isinstance(content, dict):
                object_content = content.get('object')
                if object_content:
                    # 使用字符串键而不是列表，并提供默认值0
                    total_count = object_content.get('total_count')
                    assert total_count > 0, f'basket_starter商品没有返回，请确认,{headers}'
                else:
                    assert False, f"首页editors_pick的object为空,{headers}"
            else:
                assert False, f"首页basket_starter数据返回不正确{content},{headers}"

    def home_review_assert(self, headers, content, sales_id, zipcode, language, store_key: str = None):
        # 首页晒单组件接口断言
        with assume, step(
                f"检查首页晒单， sales_id={sales_id}, zipcode={zipcode}, lang={language}, store_key={store_key}"):
            if isinstance(content, dict):
                object_content = content.get('object')
                if object_content:
                    total = object_content.get('total')
                    categories = object_content.get('categories')
                    list_items = object_content.get('list')
                    assert total > 0, f'PC首页晒单categories数据返回异常{content}，请确认,{headers}'
                    assert categories, f'PC首页晒单categories数据返回异常{content}，请确认,{headers}'
                    # assert list_items, f'PC首页晒单list数据返回异常{content}，请确认,{headers}'
                else:
                    assert False, "首页晒单的object为空"
            else:
                assert False, f"首页晒单数据返回不正确{content},{headers}"

    def category_page_assert(self, headers, content, category, sales_id, zipcode, language, store_key: str = None):
        # 分类各分类接口断言
        with assume, step(
                f"检查分类{category}， sales_id={sales_id}, zipcode={zipcode}, lang={language}, store_key={store_key}"):
            if isinstance(content, dict):
                object_content = content.get('object')
                if object_content:
                    contents = object_content.get('contents')
                    total_count = object_content.get('total_count')
                    categories = object_content.get('categories')
                    assert contents, f"sales_id={sales_id}, zipcode={zipcode}, lang={language}, store_key={store_key},分类{category}数据返回异常{content},{headers}"
                    assert categories, f"sales_id={sales_id}, zipcode={zipcode}, lang={language}, store_key={store_key},分类{category}数据返回异常{content},{headers}"
                    assert total_count > 0, f"sales_id={sales_id}, zipcode={zipcode}, lang={language}, store_key={store_key},分类{category}数据返回异常{content},{headers}"
                    # 断言不能返回餐馆菜分类
                    for catalogue_num in categories:
                        assert catalogue_num[
                                   "catalogue_num"] != "restaurant", f"分类{catalogue_num}数据返回异常{content},{headers}"
                    # fbw 商品可能会提前结帖，不能保证都有大于5个商品
                    if "freshgourmet" not in category and "freshbakery" not in category:
                        # 断言每个分类下必须存在normal商品,且不少于5条
                        self.category_product_assert(headers=headers, contents=contents, category=category)
                else:
                    assert False, f"分类{category}的object为空"
            else:
                assert False, f"分类{category}数据返回异常{content}"

    # 函数用于检查是否至少有5条特定的数据记录
    def category_product_assert(self, headers, contents, category):
        b_type = [item.get('data').get('biz_type') for item in contents]
        print(1)
        # 使用列表推导式来筛选满足条件的数据记录
        matching_records = [
            item['data'] for item in contents if
            isinstance(item, dict) and 'data' in item.keys() and isinstance(item['data'], dict) and
            item.get('type') == 'product'
            # and item['data'].get('biz_type') == 'normal' and
            # not item['data'].get('is_mkpl') and
            # not item['data'].get('is_pantry')
        ]

        # 断言至少有5条满足条件的记录
        assert len(matching_records) >= 5, f"分类{category}数据中普通商品少于5条，返回异常{contents},{headers}"

    def pdp_recommend_assert(self, headers, content, sales_id, zipcode, language, store_key: str = None):
        # pdp 页面推荐接口断言
        with assume, step(
                f"检查pdp 页面推荐， sales_id={sales_id}, zipcode={zipcode}, lang={language}, store_key={store_key}"):
            if isinstance(content, dict):
                object_content = content.get('object')
                if object_content:
                    modules = object_content.get('modules')
                    assert modules, f'商品：1380未返回相关商品+推荐搭配，请确认{object_content},{headers}'
                    for module in modules:
                        product_list = module.get("product_list")
                        assert product_list, f'商品：1380未返回相关商品+推荐搭配，请确认{object_content},{headers}'
                else:
                    assert False, f"pdp 页面推荐的object为空{content},{headers}"
            else:
                assert False, f"pdp 页面推荐数据返回不正确{content},{headers}"

    def pdp_waterfall_assert(self, headers, content, sales_id, zipcode, language, store_key: str = None):
        # pdp 页面waterfall接口断言
        with assume, step(
                f"检查pdp页面waterfall推荐， sales_id={sales_id}, zipcode={zipcode}, lang={language}, store_key={store_key}"):
            if isinstance(content, dict):
                assert len(content.get('object').get(
                    "contents")) >= 5, f"1380 pdp 页面waterfall数据返回不正确{content},{headers}"
            else:
                assert False, f"1380 pdp 页面waterfall数据返回不正确{content},{headers}"

    def pdp_review_assert(self, headers, content, sales_id, zipcode, language, store_key: str = None):
        # pdp 页面晒单接口断言
        with assume, step(
                f"检查pdp页面晒单， sales_id={sales_id}, zipcode={zipcode}, lang={language}, store_key={store_key}"):
            if isinstance(content, dict):
                object_content = content.get('object')
                if object_content:
                    total_count = object_content.get("total")
                    assert total_count > 0, f'pdp-1380 晒单数据返回异常，请确认{content},{headers}'
                else:
                    assert False, f"pdp页面晒单的object为空{content},{headers}"
            else:
                assert False, f"pdp 页面晒单数据返回不正确{content},{headers}"

    def pdp_video_assert(self, headers, content, sales_id, zipcode, language, store_key: str = None):
        # pdp 页面视频接口断言
        with assume, step(
                f"检查pdp页面视频， sales_id={sales_id}, zipcode={zipcode}, lang={language}, store_key={store_key}"):
            if isinstance(content, dict):
                object_content = content.get('object')
                if object_content:
                    total_count = object_content.get("total")
                    assert total_count > 0, f'pdp-1380 视频数据返回异常，请确认{content},{headers}'
                else:
                    assert False, f"pdp页面视频的object为空{content},{headers}"
            else:
                assert False, f"pdp 页面视频数据返回不正确{content},{headers}"

    def cart_trade_in_assert(self, headers, content, sales_id, zipcode, language, store_key: str = None):
        # 购物车换购接口断言
        with assume, step(
                f"检查换购数据， sales_id={sales_id}, zipcode={zipcode}, lang={language}, store_key={store_key}"):
            if isinstance(content, dict) and content.get('object'):
                total_count = content.get('object').get("total_count")
                products = content.get('object').get('products')
                assert total_count > 0, f"换购商品数<=0, trade_in_grocery={content},{headers}"
                assert products and len(products) >= 5, f"换购商品列表为空, trade_in_grocery={content},{headers}"
            else:
                assert False, f"换购数据返回不正确，trade_in_grocery={content},{headers}"

    def cart_preference_assert(self, headers, content, sales_id, zipcode, language, store_key: str = None):
        # 购物车猜你喜欢接口断言
        with assume, step(
                f"检查物车猜你喜欢， sales_id={sales_id}, zipcode={zipcode}, lang={language}, store_key={store_key}"):
            if isinstance(content, dict) and content.get('object'):
                total_count = content.get("object").get("total_count")
                products = content.get("object").get('products')
                assert total_count > 0, f'购物车猜你喜欢数据异常，total_count={total_count}，请确认~{content},{headers}'
                assert products and len(
                    products) >= 5, f'购物车猜你喜欢数据异常，total_count={total_count}，请确认~{content},{headers}'

            else:
                assert False, f"物车页面推荐数据返回不正确，content={content},{headers}"
            return products

    def cart_recommend_assert(self, headers, content, sales_id, zipcode, language, store_key: str = None):
        # 购物车去凑单接口断言
        with assume, step(
                f"检查购物车去凑单，sales_id={sales_id}, zipcode={zipcode}, lang={language}, store_key={store_key}"):
            if isinstance(content, dict) and content.get('object'):
                object_content = content.get('object')
                total_count = object_content.get("total_count")
                assert total_count > 0, f'去凑单页面数据异常，total_count={total_count}{content},{headers}'
                products = object_content.get("products")
                assert products, f'去凑单页面数据异常，products列表为空{content},{headers}'
            else:
                assert False, f"购物车去凑单数据返回不正确，content={content},{headers}"

    def topx_assert(self, headers, content, sales_id, zipcode, language, store_key: str = None):
        # topx接口断言
        with assume, step(
                f"检查topx，sales_id={sales_id}, zipcode={zipcode}, lang={language}, store_key={store_key}"):
            if isinstance(content, dict) and content.get('object'):
                topx_list = content.get("object").get('top_ranking_list')
                assert topx_list, f"topx数据异常：top_ranking_list为空或不存在{content},{headers}"
                # for item in topx_list:
                #     products = item.get("products")
                #     assert products, f"{item.get('key')}topx数据异常：某个top_ranking_list条目中products为空或不存在{content},{headers}"

            else:
                assert False, f"topx数据返回不正确：content={content},{headers}"

    def social_video_assert(self, headers, content, sales_id, zipcode, language, store_key: str = None):
        # 社区首页视频接口断言
        with assume, step(
                f"检查社区首页视频，sales_id={sales_id}, zipcode={zipcode}, lang={language}, store_key={store_key}"):
            if isinstance(content, dict) and content.get('object'):
                contents = content.get('object').get('contents')
                assert contents and len(
                    contents) >= 5, f"社区首页视频数据异常：contents列表为空或不存在{content},{headers}"
            else:
                assert False, f"社区首页视频数据返回不正确：content={content},{headers}"

    def social_topic_assert(self, headers, content, sales_id, zipcode, language, store_key: str = None):
        # 社区topic接口断言
        with assume, step(
                f"检查社区topic，sales_id={sales_id}, zipcode={zipcode}, lang={language}, store_key={store_key}"):
            if isinstance(content, dict) and content.get('object'):
                contents = content.get('object')
                assert contents, f"社区topic数据异常：contents列表为空或不存在{content},{headers}"
            else:
                assert False, f"社区topic数据返回不正确：content={content},{headers}"

    def social_event_assert(self, headers, content, sales_id, zipcode, language, store_key: str = None):
        # 社区event接口断言
        with assume, step(
                f"检查社区event，sales_id={sales_id}, zipcode={zipcode}, lang={language}, store_key={store_key}"):
            if isinstance(content, dict) and content.get('object'):
                contents = content.get('object')
                assert contents.get('total') > 0, f"社区event数据异常：contents列表为空或不存在{content},{headers}"
                assert contents.get('list'), f"社区event数据异常：contents列表为空或不存在{content},{headers}"
            else:
                assert False, f"社区event数据返回不正确：content={content},{headers}"
