# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_user.py
@Description    :
@CreateTime     :  2023/5/16 15:23
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 15:23
"""
import pytest
import weeeTest
from weeeTest import log
from test_dir.api.ec.ec_item.content.get_waterfall import GetHomeWaterfall
from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder
from test_dir.api.ec.ec_so.preorder.update_preorderline import UpdatePreOrderLine
from test_dir.api.ec.ec_social.post_info.post_info import PostInfo
import uuid

from test_dir.api_case.ec.common.comm_check_products_with_cart import CommCheckProductsWithCart
from test_dir.api_case.ec.common.comm_cms import CommCheckFunction
from test_dir.api_case.ec.common.remove_all_products_in_cart import RemoveAllProductsInCart


class TestHomeWaterfall(weeeTest.TestCase):
    @pytest.fixture(scope="class")
    def waterfall(self, ec_login_header):
        RemoveAllProductsInCart().remove_all_products_in_grocery_cart(headers=ec_login_header)

        random_uuid = uuid.uuid4()
        recommend_session = str(random_uuid)
        waterfall = {}
        contents = []
        for i in range(10):
            waterfall = GetHomeWaterfall().get_home_waterfall(
                headers=ec_login_header,
                recommend_session=recommend_session,
                page_num=i + 1)
            if waterfall['object']['contents']:
                contents.append(waterfall['object']['contents'])
            else:
                log.info(f"第{i + 1}页没有waterfall数据")
                break
        waterfall['object']['contents'] = [items for content in contents for items in content]
        yield waterfall
        log.info("waterfall用例执行完毕")

    @weeeTest.mark.list('waterfall_type_banners', 'Regression', 'Smoke', 'Transaction')
    def test_home_waterfall_type_banners(self, waterfall, ec_login_header):
        """ 首页-waterfall-banners卡片验证流程 """
        contents = [item for item in waterfall["object"]["contents"] if item['type'] == "banners"]
        if contents:
            for content in contents:
                banners = content["data"]["banners"]["carousel"]
                for banner in banners:
                    # 断言这个链接就是视频review的链接
                    assert banner["url"] is not None, f"waterfall里返回的banner信息{banner}"
                    assert banner["img"] is not None, f"waterfall里返回的banner信息{banner}"
                    assert banner["id"] is not None, f"waterfall里返回的banner信息{banner}"
                    assert banner["pos"] is not None, f"waterfall里返回的banner信息{banner}"
                    # 点击banner
                    CommCheckFunction().comm_check_link(banner["url"], headers=ec_login_header)

    @weeeTest.mark.list('Regression', 'Smoke', 'Transaction')
    def test_home_waterfall_type_item(self, waterfall, ec_login_header):
        """ 首页-waterfall-1P商品卡片验证流程 """
        assert waterfall["object"]["contents"], f"首页waterfall没有数据，接口返回结果为{waterfall}"
        # 卡片的数量
        # 卡片的类型至少几种
        # 卡片包含哪些元素
        # 断言接口

        contents = waterfall["object"]["contents"]
        # contents = [item for item in waterfall["object"]["contents"] if item['type'] == "item"]

        item_type = [item for item in contents if
                     item['type'] == "item" and item['data']['product']['sold_status'] == "available"]

        # 加购生鲜商品
        for index, item in enumerate(item_type):
            UpdatePreOrderLine().porder_items_v3(
                headers=ec_login_header,
                product_id=item["data"]["product"]["id"],
                biz_type=item["data"]["product"]["biz_type"],
                quantity=item['data']['product']['min_order_quantity'],
                source="mweb_home-cm_content_feed-null"
            )

            assert self.response["result"] is True and len(
                self.response["object"]["updateItems"]) > 0, f"product id is {item['data']['product']['id']}"
            CommCheckProductsWithCart().check_product_exists_in_cart(
                headers=ec_login_header,
                cart_domain="grocery",
                product_id=item["data"]["product"]["id"]
            )

            # 点击商品进pdp
            CommCheckFunction().comm_check_pdp_link(item["data"]["product"]["id"],
                                                    item["data"]["product"]["view_link"], headers=ec_login_header)

            # 如果有返回榜单，点击进榜单
            entrance_tag = item["data"]["product"]["entrance_tag"]
            if entrance_tag:
                CommCheckFunction().comm_check_link(entrance_tag["more_link"], headers=ec_login_header)

            if index == 3:
                break

    @weeeTest.mark.list('Regression', 'Smoke', 'Transaction')
    def test_home_waterfall_type_video(self, waterfall, ec_login_header):
        """ 首页-waterfall-视频卡片验证流程 """
        contents = [item for item in waterfall["object"]["contents"] if item['type'] == "normal_content_video"]
        if contents:
            for index, content in enumerate(contents):
                video_link = content["data"]["post"]["link"]
                # 断言这个链接就是视频review的链接
                assert "/social/video/"+str(content["data"]["post"]["id"]) in video_link, "这个链接返回的不是视频review的链接，请确认，视频链接为：{video_link}"
                # assert "/review/video" in video_link, "这个链接返回的不是视频review的链接，请确认，视频链接为：{video_link}"

                # 点击视频进入视频详情
                CommCheckFunction().comm_check_link(video_link, headers=ec_login_header)

                # 多次视频点赞会触发反刷机制，所以只点赞1次
                if index == 0:
                    PostInfo().praise_post(headers=ec_login_header, status="A",
                                           postId=content["data"]["post"]["id"])
                    # 补充加1
                    assert self.response["result"] is True, f"视频点赞失败，接口返回结果为{self.response}"

                    # 取消点赞
                    PostInfo().praise_post(headers=ec_login_header, status="C",
                                           postId=content["data"]["post"]["id"])

                    assert self.response["result"] is True, f"取消视频点赞失败，接口返回结果为：{self.response}"

    @weeeTest.mark.list('Regression-skip', 'product-skip', 'Transaction')
    def test_home_waterfall_type_recipe_content_video(self, waterfall, ec_login_header):
        """ 首页-waterfall-视频卡片验证流程 """
        # 移至weekly运行
        # item['type'] == "recipe_content_video"的数据不一定存在
        item_types = set([item['type'] for item in waterfall['object']['contents']])
        contents = [item for item in waterfall["object"]["contents"] if item['type'] == "recipe_content_video"]
        if contents:
            for index, content in enumerate(contents):
                # 表示这个是一个recipe视频
                video_link = content["data"]["post"]["link"]
                # 断言这个链接就是视频review的链接
                assert "/social/video" in video_link, f"这个链接返回的不是视频review的链接，链接为：{video_link}"
                # 点击视频进入视频详情
                CommCheckFunction().comm_check_link(video_link, headers=ec_login_header)

                if index == 0:
                    PostInfo().praise_post(headers=ec_login_header, status="A",
                                           postId=content["data"]["post"]["id"])
                    assert self.response["result"] is True, f"视频点赞失败，接口返回结果为{self.response}"

                    # 取消点赞
                    PostInfo().praise_post(headers=ec_login_header, status="C",
                                           postId=content["data"]["post"]["id"])
                    assert self.response["result"] is True, f"取消视频点赞失败，接口返回结果为：{self.response}"
        else:
            log.info("item['type'] == 'recipe_content_video'的数据不存在")
            pytest.skip("item.type==recipe_content_video的数据不存在")

    @weeeTest.mark.list('Regression', 'Smoke', 'Transaction')
    def test_home_waterfall_type_cm_theme(self, waterfall, ec_login_header):
        """ 首页-waterfall-主题卡片验证流程 """
        contents = [item for item in waterfall["object"]["contents"] if item['type'] == "cm_theme"]
        # 表示这个是主题主键, 经测试，数据不一定存在
        if contents:
            for content in contents:
                assert content["data"]["wf_theme"]["title"] is not None
                assert content["data"]["wf_theme"]["more_link_title"] is not None
                assert content["data"]["wf_theme"]["vertical_img"] is not None
                more_link = content["data"]["wf_theme"]["more_link"]
                assert "/promotion/theme_landing" in content["data"]["wf_theme"]["more_link"]
                # 点击查看更多进主题详情
                CommCheckFunction().comm_check_link(more_link, headers=ec_login_header)
                assert content["data"]["wf_theme"]["product_imgs"] is not None
                assert content["data"]["wf_theme"]["product_ids"] is not None
        else:
            log.info("item['type'] == 'cm_theme'的数据不存在")
            pytest.skip("item.type==cm_theme的数据不存在")

    @weeeTest.mark.list('Regression', 'Smoke', 'Transaction')
    def test_home_waterfall_type_wf_collection(self, waterfall, ec_login_header):
        """ 首页-waterfall-手工合集卡片验证流程 """
        contents = [item for item in waterfall["object"]["contents"] if item['type'] == "wf_collection"]
        if contents:
            for content in contents:
                more_link = content["data"]["wf_collection"]["more_link"]
                products = content["data"]["wf_collection"]["products"]
                # 点击查看更多进入合集详情
                CommCheckFunction().comm_check_link(more_link, headers=ec_login_header)
                assert products, f"当前wf_collection没有商品，contents为：{contents}"
                # assert "/promotion/collect/"+content["data"]["wf_collection"]["refer_id"] in content["data"]["wf_collection"]["more_link"]
                assert content["data"]["wf_collection"]["title"] is not None
                assert content["data"]["wf_collection"]["more_link_title"] is not None
                assert content["data"]["wf_collection"]["type"] is not None
                if content["data"]["wf_collection"]["is_mkpl_collection"] is False:
                    # 手工合集 or cms 合集
                    assert content["data"]["wf_collection"]["type"] == "manual_collection"
                elif content["data"]["wf_collection"]["is_mkpl_collection"] is True:
                    # mkpl 合集
                    assert content["data"]["wf_collection"]["type"] == "mkpl_collection"
                    assert all(product.get('biz_type') == "seller" for product in
                               products), "Not all biz_type values are 'seller'"

                for index, product in enumerate(products):

                    # 如果商品返回榜单tag，点击进入对应的topx
                    entrance_tag = product["entrance_tag"]
                    if entrance_tag:
                        more_link = entrance_tag["more_link"]
                        CommCheckFunction().comm_check_link(more_link, headers=ec_login_header)

                    # 点击组件里的商品进pdp
                    CommCheckFunction().comm_check_pdp_link(product['id'], product["view_link"], headers=ec_login_header)

                    if index == 2:
                        break

    @weeeTest.mark.list('Regression', 'Smoke', 'Transaction')
    def test_home_waterfall_type_wf_collection_v2(self, waterfall, ec_login_header):
        """ 首页-waterfall-MKPL合集卡片验证流程 """
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]

        contents = [item for item in waterfall["object"]["contents"] if item['type'] == "wf_collection_v2"]
        if contents:
            for content in contents:
                more_link = content["data"]["wf_collection_v2"]["more_link"]
                # 点击查看更多进入合集详情
                CommCheckFunction().comm_check_link(more_link, headers=ec_login_header)
                assert content["data"]["wf_collection_v2"]["title"] is not None
                assert content["data"]["wf_collection_v2"]["more_link_title"] is not None
                assert content["data"]["wf_collection_v2"]["type"] is not None
                assert content["data"]["wf_collection_v2"]["collection_type"] == "theme"
                assert content["data"]["wf_collection_v2"]["business_type"] == "mkpl"

                if content["data"]["wf_collection_v2"]["type"] == "topx":
                    assert "/promotion/top-x/" in content["data"]["wf_collection_v2"]["more_link"]

                products = content["data"]["wf_collection_v2"]["products"]
                assert len(products) == 4, f"当前wf_collection_v2没有商品，contents为：{contents}"
                assert products, f"当前wf_collection_v2没有商品，contents为：{contents}"

                for product in products:

                    # 如果商品返回榜单tag，点击进入对应的topx
                    entrance_tag = product["entrance_tag"]
                    if entrance_tag:
                        more_link = entrance_tag["more_link"]
                        CommCheckFunction().comm_check_link(more_link, headers=ec_login_header)

                    # 点击组件里的商品进pdp
                    CommCheckFunction().comm_check_pdp_link(product['id'], product["view_link"], headers=ec_login_header)

    @weeeTest.mark.list('Regression', 'Smoke', 'Transaction')
    def test_home_waterfall_type_wf_list(self, waterfall, ec_login_header):
        """ 首页-waterfall-推荐合集卡片验证流程 """
        contents = [item for item in waterfall["object"]["contents"] if item['type'] == "wf_list"]

        # 表示这个是推荐合集，如新品，onsale
        if contents:
            for content in contents:

                more_link = content["data"]["wf_list"]["more_link"]
                # 点击查看更多进入合集详情
                CommCheckFunction().comm_check_link(more_link, headers=ec_login_header)
                assert content["data"]["wf_list"]["title"] is not None, f'waterfall 内容{content}'
                assert content["data"]["wf_list"]["more_link_title"] is not None, f'waterfall 内容{content}'
                assert content["data"]["wf_list"]["type"] is not None, f'waterfall 内容{content}'
                assert content["data"]["wf_list"]["type"] == content["data"]["wf_list"][
                    "refer_id"], f'waterfall 内容{content}'
                if content["data"]["wf_list"]["type"] == "cm_item_new":
                    # 新品上架
                    assert "/category/new?filter_sub_category=new" in more_link, f'waterfall 内容{content}'
                elif content["data"]["wf_list"]["type"] == "cm_item_fbw_bakery":
                    # 每日现做
                    assert "/promotion/collection/fbwbakerycollection" in more_link, f'waterfall 内容{content}'
                elif content["data"]["wf_list"]["type"] == "cm_item_exposure_collection":
                    # 发现好货
                    assert "/promotion/collection/exposurecollection" in more_link, f'waterfall 内容{content}'
                elif content["data"]["wf_list"]["type"] == "cm_item_buy_it_again":
                    # 曾经购买
                    assert "/account/my-list?type=bought" in more_link, f'waterfall 内容{content}'

                # 还需要继续补充
                products = content["data"]["wf_list"]["products"]
                assert products, f"当前wf_list没有商品，contents为：{contents}"
                available_product = [item for item in products if item['sold_status'] == 'available']

                for product in available_product:

                    # 如果商品返回榜单tag，点击进入对应的topx
                    entrance_tag = product["entrance_tag"]
                    if entrance_tag is not None:
                        CommCheckFunction().comm_check_link(entrance_tag["more_link"], headers=ec_login_header)

                    CommCheckFunction().comm_check_pdp_link(product['id'], product["view_link"], headers=ec_login_header)

    @weeeTest.mark.list('Regression-skip', 'product-skip', 'Transaction')
    def test_home_waterfall_type_cm_lightning_deals(self, waterfall, ec_login_header):
        """ 首页-waterfall-秒杀卡片验证流程 """
        # 此用例一直是skip，2024-9-12去掉smoke标签，放在weekly里运行
        contents = [item for item in waterfall["object"]["contents"] if item['type'] == "cm_lightning_deals"]
        # 表示这个是生鲜/global+限时秒杀, 不一定每次有数据
        if contents:
            for content in contents:
                more_link = content["data"]["lightning_deals"]["more_link"]
                # 断言这个链接就是秒杀详情的链接
                assert "lightning-deals" in more_link, f"这个链接返回的不是秒杀详情链接，请确认.链接为：{more_link}"
                # 点击Buy now进入秒杀详情
                CommCheckFunction().comm_check_link(more_link, headers=ec_login_header)

                # 点击秒杀title也是进入秒杀详情
                detail_more_link = content["data"]["lightning_deals"]["component_metadata"]["more_link"]
                # 点击title进入秒杀详情
                CommCheckFunction().comm_check_link(detail_more_link, headers=ec_login_header)

                # 点击秒杀商品进入pdp
                products = content["data"]["lightning_deals"]["products"]
                for index, product in enumerate(products):
                    # 点击title进入秒杀详情
                    CommCheckFunction().comm_check_pdp_link(product['id'], product["view_link"], headers=ec_login_header)

                    if index == 2:
                        break
        else:
            log.info("item['type'] == 'cm_lightning_deals'的数据不存在")
            pytest.skip("item.type==cm_lightning_deals的数据不存在")

    @weeeTest.mark.list('invalidate', 'product-skip', 'Transaction')
    def test_home_waterfall_type_cm_rtg_lightning(self, waterfall, ec_login_header):
        """ 首页-waterfall-餐馆秒杀卡片验证流程 """
        # 移至weekly运行
        contents = [item for item in waterfall["object"]["contents"] if item['type'] == "cm_rtg_lightning"]
        # 表示这个是餐馆团送限时秒杀, 不一定有数据
        if contents:
            for content in contents:
                assert content["data"]["wf_rtg_lightning"]["title"] is not None
                assert content["data"]["wf_rtg_lightning"]["more_link_title"] is not None
                assert content["data"]["wf_rtg_lightning"]["bg_color"] is not None
                assert content["data"]["wf_rtg_lightning"]["end_time"] is not None
                assert content["data"]["wf_rtg_lightning"]["start_time"] is not None
                more_link = content["data"]["wf_rtg_lightning"]["more_link"]
                # 断言这个链接就是餐馆团送限时秒杀详情的链接
                assert "/rtg/topic" in more_link, f"这个链接返回的不是餐馆团送限时秒杀详情链接，请确认，链接为：{more_link}"
                # 点击立即抢购进入餐馆秒杀详情
                CommCheckFunction().comm_check_link(more_link, headers=ec_login_header)
                assert len(content["data"]["wf_rtg_lightning"]["products"]) == 4
        else:
            log.info("item['type'] == 'cm_rtg_lightning'的数据不存在")
            pytest.skip("item.type==cm_rtg_lightning的数据不存在")

    @weeeTest.mark.list('invalidate', 'product-skip', 'Transaction')
    def test_home_waterfall_type_cm_rtg(self, waterfall, ec_login_header):
        """ 首页-waterfall-餐馆团送卡片验证流程 """
        # 移至weekly运行
        contents = [item for item in waterfall["object"]["contents"] if item['type'] == "cm_rtg"]
        # 表示这个是餐馆团送, 不一定每次有数据
        if contents:
            for content in contents:
                assert content["data"]["wf_rtg"]["btn_title"] is not None, f'waterfall {content}'
                assert content["data"]["wf_rtg"]["more_link"] is not None, f'waterfall {content}'
                assert content["data"]["wf_rtg"]["more_link_title"] is not None, f'waterfall {content}'
                assert content["data"]["wf_rtg"]["sub_title"] is not None, f'waterfall {content}'
                assert content["data"]["wf_rtg"]["title"] is not None, f'waterfall {content}'
                more_link = content["data"]["wf_rtg"]["more_link"]
                assert "/rtg/stores?tab_key=scheduled" in more_link, f'waterfall {content}'
                # 点击查看更多进入餐馆详情
                CommCheckFunction().comm_check_link(more_link, headers=ec_login_header)

                merchants = content["data"]["wf_rtg"]["merchants"]
                for merchant in merchants:
                    # 点击单个餐馆进入对应餐馆详情
                    CommCheckFunction().comm_check_link(merchant["link"], headers=ec_login_header)
                    assert merchant["id"] is not None, f'waterfall {content}'
                    assert "/rtg/store?id=" + str(merchant["id"]) + "&tab_key=scheduled" in merchant[
                        "link"], f'waterfall {content}'
                    assert merchant["image_url"] is not None, f'waterfall {content}'
                    assert merchant["title"] is not None, f'waterfall {content}'

        else:
            log.info("item['type'] == 'cm_rtg'的数据不存在")
            pytest.skip("item.type==cm_rtg的数据不存在")
