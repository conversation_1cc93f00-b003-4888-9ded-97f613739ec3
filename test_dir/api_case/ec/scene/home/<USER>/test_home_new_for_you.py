# # !/usr/bin/python3
# # -*- coding: utf-8 -*-
# """
# <AUTHOR>  <PERSON><PERSON><PERSON> <PERSON>
# @Version        :  V1.0.0
# ------------------------------------
# @File           :  test_user.py
# @Description    :
# @CreateTime     :  2023/5/16 15:23
# @Software       :  PyCharm
# ------------------------------------
# @ModifyTime     :  2023/5/16 15:23
# """
# from typing import Any
#
# import requests
# import weeeTest
# from weeeTest import RequestHeader
#
# from test_dir.api.ec.ec_item.ds_collection_rest.ds_collection_home import DsCollectionHome
# from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder
# from test_dir.api_case.ec.common.comm_check_products_with_cart import CommCheckProductsWithCart
# from test_dir.api_case.ec.common.comm_cms import CommCheckFunction
#
#
# class TestHomeNewForYou(weeeTest.TestCase):
#     @weeeTest.data.file(file_name='ec_item_data.json', return_type='dict')
#     @weeeTest.mark.list('Regression', 'tb1',  'Transaction')
#     @weeeTest.mark.skip("该组件目前已下线")
#     def test_home_new_for_you(self, *args):
#         """ 推荐-首页“new_for_you”验证流程 """
#         # 获取用户的porder
#         porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=RequestHeader.ec_login_header)["object"]
#         cms_collection = DsCollectionHome().ds_collection_home(headers=RequestHeader.ec_login_header,
#                                                                scenes="exposurecollection",
#                                                                ds_url="cm_item_exposure_collection")
#
#         assert cms_collection["object"]['ds_collection_key_exist'] is True
#         assert cms_collection["object"][
#                    'ds_collection_key'] == "cm_item_exposure_collection", f'ds_collection_key:{cms_collection["object"]["ds_collection_key"]}'
#         assert cms_collection["object"][
#                    "total_count"] > 0, f'首页“发现好物”没有数据返回，请确认{cms_collection["object"]["object"]}'
#
#         self.featured_finds_product_assertion(porder["delivery_pickup_date"], cms_collection["object"]["products"])
#         # 验证点击more_link 跳转正常
#         more_link = cms_collection["object"]["component_metadata"]["more_link"]
#         CommCheckFunction().comm_check_link(more_link)
#         assert "promotion/collection/exposurecollection" in more_link, f'发现好物跳转链接不对：{more_link}'
#
#     def featured_finds_product_assertion(self, date, products: list | Any):
#         # 对返回结果商品操作及断言
#         for index, product in enumerate(products):
#             assert "weeecdn" in product["img"]
#             assert product["name"] is not None
#             assert product["price"] is not None
#             assert product["sold_status"] == "available", f'首页商品不是可售状态{product["sold_status"]}'
#
#             assert str(product["id"]) in product['slug'], "这个链接返回的不是这个产品的链接，请确认~"
#             # 产品标签tag
#             if product["entrance_tag"] is not None:
#                 more_link = product["entrance_tag"]["more_link"]
#                 assert product["entrance_tag"]['tag_name'] is not None
#                 # 验证点击more_link 跳转正常
#                 CommCheckFunction().comm_check_link(more_link)
#             if index == 3:
#                 break
#         # 点击商品卡片跳转pdp
#         CommCheckFunction().comm_check_pdp_link(products[-1]["id"], products[-1]["view_link"])
#         # 加购editor_pick下的商品
#         CommCheckProductsWithCart().products_add_to_cart(headers=RequestHeader.ec_login_header,
#                                                          products=products,
#                                                          porder_deal_date=date,
#                                                          product_source="mweb_home-cm_item_exposure_collection-null")
#
#
# if __name__ == '__main__':
#     weeeTest.main(base_url='https://api.tb1.sayweee.net', env='tb1')
