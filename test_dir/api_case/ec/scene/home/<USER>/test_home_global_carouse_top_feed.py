# # !/usr/bin/python3
# # -*- coding: utf-8 -*-
# """
# <AUTHOR>  <PERSON><PERSON><PERSON> <PERSON>
# @Version        :  V1.0.0
# ------------------------------------
# @File           :  test_user.py
# @Description    :
# @CreateTime     :  2023/5/16 15:23
# @Software       :  PyCharm
# ------------------------------------
# @ModifyTime     :  2023/5/16 15:23
# """
# import requests
# import weeeTest
# import logging
# 
# from weeeTest import RequestHeader
# 
# from test_dir.api.ec.ec_marketplace.home_carousel.home_mkpl_carousel import HomeMkplCarousel
# from test_dir.api.ec.ec_mkt.activity.lighting_deals_v1 import LightingDeals
# from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder
# from test_dir.api.ec.ec_so.preorder.update_preorderline import UpdatePreOrderLine
# 
# 
# 
# class TestHomeGlobalCarouse(weeeTest.TestCase):
#     @weeeTest.data.file(file_name='ec_item_data.json', return_type='dict')
#     @weeeTest.mark.list('Regression', 'tb1',  'Transaction')
#     @weeeTest.mark.skip("目前没有这个组件了")
#     def test_home_global_carouse_top_feed(self, *args):
#         """ 推荐-首页“全球购 · 年货集惠组件”验证流程 """
#         # 每日现做是面包+卤味
#         # 获取用户的porder
#         porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=RequestHeader.ec_login_header)["object"]
#         delivery_date = porder["delivery_pickup_date"]
# 
#         # 清除生鲜购物车
#         # RemoveAllProductsInCart().remove_all_products_in_grocery_cart(headers=headers)
#         # # 获取首页所有组件数据
#         # cms_data = CmsSayweeeMobileHome().cms_sayweee_mobile_home(headers=headers)
#         # assert cms_data["result"] is True
#         # layout = cms_data["object"]["layout"]
#         # components = cms_data["object"]["layout"]["sections"][0]["components"]
#         # 需要判断是否有mkpl 秒杀
#         # MKPL秒杀商品列表(首页)
#         mkpl_lighting = LightingDeals().mkpl_lighting(headers=RequestHeader.ec_login_header, date=delivery_date)
#         assert mkpl_lighting["result"] is True
#         # 如果首页存在MKPL秒杀
#         # 有秒杀的时候， 用2的组件位置展示global+组件
#         # 没有秒杀的时候，用1的组件位置展示global+组件
# 
#         if mkpl_lighting["object"] is not None:
#             position = 2
#             value = 1758326
#             # 有秒杀的时候， 用2的组件位置展示global+组件
#         else:
#             # 没有秒杀的时候，用1的组件位置展示global+组件
#             position = 1
#             value = 1758325
#         cms_collection = HomeMkplCarousel().top_feed(headers=RequestHeader.ec_login_header,
#                                                      dataobject_key="ds_item_mkpl_collection_" + str(
#                                                          position) + "_" + str(value), position=position)
# 
#         assert cms_collection["result"] is True
#         if cms_collection["object"]["total_count"] > 0:
#             products = cms_collection["object"]["products"]
#             component_metadata = cms_collection["object"]["component_metadata"]
#             tabs = cms_collection["object"]["tabs"]
#             # 切换tab
#             for tab in tabs:
#                 # 切换tab
#                 cms_tab = HomeMkplCarousel().top_feed(headers=RequestHeader.ec_login_header, position=position, key=tab["key"])
#                 assert cms_tab["result"] is True
# 
#                 # 点击查看每个tab下的查看更多按钮
#                 url = tab['more_link']
#                 response = requests.get(url)
#                 if response.status_code == 200:
#                     print(f"{url} is accessible")
#                 else:
#                     print(f"{url} is not accessible")
# 
#             # 获取fresh_daily下的第一个生鲜商品
#             for product in products:
#                 product_id = product["id"]
#                 # 加购搜索结果里的生鲜商品
#                 add_to_cart = UpdatePreOrderLine().porder_items_v3(headers=RequestHeader.ec_login_header, product_id=product_id,
#                                                                    date=porder["delivery_pickup_date"],
#                                                                    is_pantry=product["is_pantry"],
#                                                                    is_mkpl=product["is_mkpl"],
#                                                                    refer_type="normal",
#                                                                    source="portal-recommend")
#                 # 判断加购成功
#                 assert add_to_cart["result"] is True
#                 # 判断加入生鲜购物车成功
#                 # CheckProductsAddedToCartSuccess().check_product_add_to_cart_success(headers=headers,
#                 #                                                                     cart_domain="grocery",
#                 #                                                                     product_id=product_id)
#                 break
#         else:
#             logging.info("没有这个组件，请确认")
# 
# 
# if __name__ == '__main__':
#     weeeTest.main(base_url='https://api.tb1.sayweee.net', env='tb1')
