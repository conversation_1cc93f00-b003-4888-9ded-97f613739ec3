# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_user.py
@Description    :
@CreateTime     :  2023/5/16 15:23
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 15:23
"""
from typing import Any

import weeeTest
from test_dir.api.ec.ec_mkt.banner.get_banner_list import GetBannerList
from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder
from test_dir.api_case.ec.common.comm_cms import CommCheckFunction


class TestMeCarouselBanner(weeeTest.TestCase):

    @weeeTest.mark.list('Regression', 'Smoke',  'Transaction')
    def test_me_carousel_banner(self, ec_login_header):
        """ Banner-Account页面轮播图验证流程 """
        # 获取用户的porder
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]

        # 1、获取Account轮播图
        banner_list = GetBannerList().get_banner_list(headers=ec_login_header,
                                                      type="me_page",
                                                      dataobject_key="ds_main_banner",
                                                      sales_org_id=porder["sales_org_id"],
                                                      lang="en",
                                                      date=porder["delivery_pickup_date"])

        assert banner_list["object"] is not None
        carousel_banner = banner_list["object"]["carousel"]
        # 断言有首页轮播图,并访问轮播图链接
        self.me_page_banner_assertion(carousel_banner, headers=ec_login_header)

    def me_page_banner_assertion(self, carousel_banner: list | Any, headers):
        for carousel in carousel_banner:
            assert carousel['img_url'] is not None
            assert carousel['detail'] is not None
            # assert carousel['key'] == "portal_top"
            # 断言有轮播图,并访问轮播图链接
            url = carousel['detail']
            # 点击跳转
            CommCheckFunction().comm_check_link(url, headers=headers)

