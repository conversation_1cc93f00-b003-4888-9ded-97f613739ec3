# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_user.py
@Description    :
@CreateTime     :  2023/5/16 15:23
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 15:23
"""
import time

import requests
import weeeTest
from weeeTest import log

from test_dir.api.ec.ec_item.top_ranking.api_top_ranking import ApiTopRanking
from test_dir.api.ec.ec_marketplace.home_carousel.home_mkpl_carousel import HomeMkplCarousel
from test_dir.api.ec.ec_so.deliverydates.get_valid_delivery_dates import GetValidDeliveryDates
from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder
from test_dir.api.ec.ec_so.preorder.update_preorderline import UpdatePreOrderLine
from test_dir.api_case.ec.common.comm_check_products_with_cart import CommCheckProductsWithCart
from test_dir.api_case.ec.common.comm_cms import CommCheckFunction
from test_dir.api_case.ec.common.common_check import CommonCheck
from test_dir.api_case.ec.common.remove_all_products_in_cart import RemoveAllProductsInCart
from test_dir.api_case.ec.common.set_user_porder import SetUserPorder


class TestTopX(weeeTest.TestCase):
    def grocery_catalogue_list(self, headers):
        # 1. 排行榜分类列表
        catalogue_list = ApiTopRanking().top_ranking_catalogue_list(headers=headers)
        assert len(catalogue_list["object"]['category_list']) > 0, f"未获取排行榜分类，返回结果为：{catalogue_list}"
        return catalogue_list

    def grocery_top_ranking_list(self, catalogue_num, headers, collection_type: str = None):
        # 1. 排行榜分类列表
        top_ranking_list = ApiTopRanking().top_ranking_list(
            headers=headers,
            catalogue_num=catalogue_num,
            collection_type=collection_type
        )
        return top_ranking_list

    @weeeTest.mark.list('Regression', 'Smoke', 'Transaction', 'grocery_top_ranking')
    def test_grocery_top_ranking_catalogue_list(self, ec_login_header):
        """ Topx-生鲜排行榜分类数据验证流程 """
        catalogue_list = self.grocery_catalogue_list(headers=ec_login_header)
        for item in catalogue_list["object"]["category_list"]:
            assert item["num"] is not None, f'grocery 榜单分类数据异常{catalogue_list}'
            assert item["label"] is not None, f'grocery 榜单分类数据异常{catalogue_list}'
            assert item["active_img_url"] is not None, f'grocery 榜单分类数据异常{catalogue_list}'
            assert item["img_url"] is not None, f'grocery 榜单分类数据异常{catalogue_list}'
            assert item["thumbnail_img_url"] is not None, f'grocery 榜单分类数据异常{catalogue_list}'
            assert item["icon_url"] is not None, f'grocery 榜单分类数据异常{catalogue_list}'
            assert item["icon_active_url"] is not None, f'grocery 榜单分类数据异常{catalogue_list}'
            assert item["color"] is not None, f'grocery 榜单分类数据异常{catalogue_list}'
            assert item["url"] is not None, f'grocery 榜单分类数据异常{catalogue_list}'
            assert item["key"] is not None, f'grocery 榜单分类数据异常{catalogue_list}'
            assert item["name"] is not None, f'grocery 榜单分类数据异常{catalogue_list}'
            if item["key"] != "trending":
                assert item["collection_type_list"] is not None, f'grocery 榜单分类数据异常{catalogue_list}'

    @weeeTest.mark.list('Regression', 'Smoke', 'Transaction', 'grocery_top_ranking')
    def test_grocery_top_ranking(self, ec_login_header):
        """ Topx-生鲜排行榜相关验证流程 """
        SetUserPorder.set_user_header_porder(ec_login_header, "98011", "en")
        _porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]
        deal_date = _porder.get("delivery_pickup_date")
        RemoveAllProductsInCart().remove_all_products_in_grocery_cart(headers=ec_login_header)
        # 1. 排行榜分类列表
        catalogue_list = self.grocery_catalogue_list(headers=ec_login_header)
        assert len(catalogue_list["object"]['category_list']) > 0, f"未获取排行榜分类，返回结果为：{catalogue_list}"
        # 根据分类调用查询榜单列表
        for a_index, category_list in enumerate(catalogue_list["object"]["category_list"]):
            catalogue_num = category_list["num"]
            collection_type_list = category_list["collection_type_list"]
            if not collection_type_list:
                # 2. 根据分类 查询榜单列表
                top_ranking_list = self.grocery_top_ranking_list(catalogue_num=catalogue_num, headers=ec_login_header)

                assert len(top_ranking_list["object"][
                               'top_ranking_list']) > 0, f"未获取榜单列表，接口返回的结果为：{top_ranking_list}， catalogue_num为{catalogue_num}"

            else:
                for collection_type in collection_type_list:
                    # 5. 根据分类及collection_type 查询榜单列表
                    top_ranking_list = self.grocery_top_ranking_list(catalogue_num=catalogue_num,
                                                                     collection_type=collection_type["key"],
                                                                     headers=ec_login_header)

                    assert len(top_ranking_list["object"][
                                   'top_ranking_list']) > 0, f"未获取榜单列表，接口返回的结果为：{top_ranking_list}， catalogue_num为{catalogue_num}"

                    top_ranking_lists = top_ranking_list["object"]["top_ranking_list"]

                    for index, top_ranking in enumerate(top_ranking_lists):
                        assert top_ranking[
                                   "description"] is not None, f"未获取榜单列表，接口返回的结果为：{top_ranking_list}， catalogue_num为{catalogue_num}"
                        assert top_ranking[
                                   "key"] is not None, f"未获取榜单列表，接口返回的结果为：{top_ranking_list}， catalogue_num为{catalogue_num}"
                        assert top_ranking[
                                   "title"] is not None, f"未获取榜单列表，接口返回的结果为：{top_ranking_list}， catalogue_num为{catalogue_num}"

                        # 2.1 加购排行榜商品
                        available_product: list[dict] = [product for product in top_ranking['products'] if
                                                         product['sold_status'] == 'available']
                        for b_index, product in enumerate(available_product):
                            log.debug(f"商品的信息为：{product.get('id')}, {product.get('sold_status')}")
                            # 商品基础断言
                            CommonCheck().check_product_info(ec_login_header, product=product, category_type="others",
                                                             filters="others", source="topx")

                            UpdatePreOrderLine().porder_items_v3(
                                headers=ec_login_header,
                                product_id=product['id'],
                                quantity=product['min_order_quantity'],
                                date=deal_date
                            )
                            # 判断加购成功
                            assert self.response["result"] is True and len(
                                self.response["object"]["updateItems"]) > 0, f"product id is {product}"
                            CommCheckProductsWithCart().check_product_exists_in_cart(
                                headers=ec_login_header,
                                cart_domain="grocery",
                                product_id=product['id']
                            )

                            if b_index == 3:
                                break

                        key = top_ranking["key"]
                        # 3. 根据key，查询榜单关联榜单列表
                        related_top_ranking = ApiTopRanking().top_ranking_related_list(
                            headers=ec_login_header, key=key)

                        if not isinstance(related_top_ranking, dict):
                            time.sleep(3)
                            related_top_ranking = ApiTopRanking().top_ranking_related_list(
                                headers=ec_login_header, key=key)

                        assert len(related_top_ranking['object'][
                                       'top_ranking_list']) > 0, f"获取关联榜单列表失败，返回结果为：{related_top_ranking}, key为：{key}"
                        assert related_top_ranking['object'][
                                   'more_link'] is not None, f"获取关联榜单列表失败，返回结果为：{related_top_ranking}, key为：{key}"
                        assert "/promotion/top-x/chart" in related_top_ranking['object'][
                            'more_link'], f"获取关联榜单列表失败，返回结果为：{related_top_ranking}, key为：{key}"
                        # 验证more link转正常
                        if index % 3 == 0:
                            CommCheckFunction().comm_check_link(related_top_ranking['object']['more_link'], headers=ec_login_header)

                        # assert related_top_ranking["result"] is True and related_top_ranking['object'][
                        #     'top_ranking_list'], f"获取关联榜单列表失败，返回结果为：{self.response}, key为：{key}"

                        # 3.1 加购关联榜单商品
                        assert related_top_ranking['object']['top_ranking_list'], f"related_top_ranking={related_top_ranking}"
                        # available_related_product = [item for top_ranking in
                        #                              related_top_ranking['object']['top_ranking_list'] for item in
                        #                              top_ranking['products'] if top_ranking['products'] and item['sold_status'] == 'available']
                        available_related_product = []
                        for top_ranking in related_top_ranking['object']['top_ranking_list']:
                            assert top_ranking['products'], f"top_ranking={top_ranking}"
                            for item in top_ranking['products']:
                                if item['sold_status'] == 'available':
                                    available_related_product.append(item)

                        assert available_related_product, f"available_related_product={available_related_product}"
                        for r_index, related_product in enumerate(available_related_product):
                            UpdatePreOrderLine().porder_items_v3(
                                headers=ec_login_header,
                                product_id=related_product['id'],
                                quantity=related_product['min_order_quantity'],
                                date=deal_date
                            )
                            # 判断加购成功
                            assert self.response["result"] is True and len(
                                self.response["object"]["updateItems"]) > 0, f"product id is {related_product['id']}"
                            CommCheckProductsWithCart().check_product_exists_in_cart(
                                headers=ec_login_header,
                                cart_domain="grocery",
                                product_id=related_product['id']
                            )

                            if r_index == 2:
                                break

                        # 4. 根据key 查询榜单详情
                        ranking_detail = ApiTopRanking().top_ranking_detail(headers=ec_login_header,
                                                                            key=key)
                        assert ranking_detail["result"] is True and ranking_detail['object'][
                            'products'], f"根据key获取榜单详情失败，接口返回结果为：{ranking_detail}， key={key}"

            if a_index == 5:
                break

    def global_topx_feed(self, headers):
        global_topx = HomeMkplCarousel().top_feed(headers=headers)
        assert global_topx["result"] is True
        return global_topx

    @weeeTest.mark.list('Regression', 'Smoke', 'Transaction', 'test_global_topx_ranking')
    def test_global_topx_ranking(self, ec_login_header):
        """ Topx-首页global+排行榜相关验证流程 """
        global_topx = self.global_topx_feed(headers=ec_login_header)
        assert global_topx["object"]["total_count"] > 0, f'PC-waterfall topx 数据返回异常，{global_topx}'
        assert global_topx["object"]["type"] == "top_rank", f'PC-waterfall topx 数据返回异常，{global_topx}'
        assert len(global_topx["object"]["products"]) > 0, f'PC-waterfall topx 数据返回异常，{global_topx}'
        assert len(global_topx["object"]["tabs"]) > 0, f'PC-waterfall topx 数据返回异常，{global_topx}'
        component_metadata = global_topx["object"]["component_metadata"]
        assert component_metadata["event_key"] is not None, f'PC-waterfall topx 数据返回异常，{global_topx}'
        assert component_metadata["sub_title"] is not None, f'PC-waterfall topx 数据返回异常，{global_topx}'
        assert component_metadata["title"] is not None, f'PC-waterfall topx 数据返回异常，{global_topx}'

    @weeeTest.mark.list('Regression', 'Smoke', 'Transaction', 'test_global_topx_ranking')
    def test_global_topx_tab_ranking(self, ec_login_header):
        """ Topx-首页global+排行榜切换t磅单ab相关验证流程 """
        global_topx = self.global_topx_feed(headers=ec_login_header)
        tabs = global_topx["object"]["tabs"]
        for tab in tabs:
            assert tab["title"] is not None, f'mkpl topx 数据返回异常，{global_topx}'
            assert "/mkpl/top-items?tab=" + tab["key"] in tab[
                'more_link'], f'mkpl topx 数据返回异常，{global_topx}'

            # PC waterfall页面 topx 模块切换tab
            cms_tab = HomeMkplCarousel().top_feed(headers=ec_login_header,
                                                  key=tab["key"])

            assert cms_tab["object"]["total_count"] > 0, f'mkpl top 没有返回数据，{tab["key"]}下{cms_tab}，'

            # 点击查看每个tab下的查看更多按钮
            more_link = tab['more_link']
            response = requests.get(more_link)
            try:
                response.status_code == 200
            except Exception as e:
                log.info(f"{more_link} is not accessible" + str(e))
            # 点击see all 进入榜单详情页面
            global_ranking_top = HomeMkplCarousel().global_ranking_top_feed(headers=ec_login_header,
                                                                            tab=tab["key"])

            assert len(global_ranking_top["object"]) > 0, f'Global+ Top charts 页面数据异常{global_ranking_top}'
            for item in global_ranking_top["object"]:
                if item["type"] == tab["key"]:
                    assert len(item["contents"]) > 0, f'Global+ Top charts 页面数据异常{global_ranking_top}'
                    # assert len(item["tabs"]) > 0
                    assert item["selected"] is True, f'Global+ Top charts 页面数据异常{global_ranking_top}'
                    tabs2 = item["tabs"]
                    if len(item["tabs"]) > 0:
                        for tab2 in tabs2:
                            assert tab2[
                                       "icon_active_url"] is not None, f'Global+ Top charts 页面数据异常{global_ranking_top}'
                            assert tab2[
                                       "icon_url"] is not None, f'Global+ Top charts 页面数据异常{global_ranking_top}'
                            assert tab2[
                                       "icon_active_url"] is not None, f'Global+ Top charts 页面数据异常{global_ranking_top}'
                            # 再点击切换分类tab
                            tab2_ranking_top = HomeMkplCarousel().global_ranking_top_feed(
                                headers=ec_login_header,
                                tab=tab["key"], key=tab2["key"])
                            assert tab2_ranking_top[
                                       "object"] is not None, f'TAB{tab2["key"]}Global+ Top charts 页面数据异常{tab2_ranking_top}'

    @weeeTest.mark.list('Regression', 'Smoke', 'Transaction', 'test_global_topx_ranking')
    def test_global_topx_product(self, ec_login_header):
        """ Topx-首页global+排行榜商品相关验证流程 """
        # 清除生鲜购物车
        RemoveAllProductsInCart().remove_all_products_in_grocery_cart(headers=ec_login_header)
        # 获取用户的porder
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]
        global_topx = self.global_topx_feed(headers=ec_login_header)
        products = global_topx["object"]["products"]
        for index, item in enumerate(products):
            CommonCheck().check_product_info(product=item, category_type="global+", headers=ec_login_header)
            product_id = item["id"]
            # 加购搜索结果里的生鲜商品
            add_to_cart = UpdatePreOrderLine().porder_items_v3(headers=ec_login_header,
                                                               product_id=product_id,
                                                               date=porder["delivery_pickup_date"],
                                                               is_pantry=item["is_pantry"],
                                                               is_mkpl=item["is_mkpl"],
                                                               refer_type="normal",
                                                               source="portal-recommend")
            # 判断加购成功
            assert add_to_cart["result"] is True
            if index == 2:
                break

    # @weeeTest.mark.list('Regression', 'Smoke', 'Transaction', 'test_global_topx_ranking')
    # def test_global_topx_ranking(self):
    #     """ Topx-首页global+排行榜相关验证流程 """
    #     # 清除生鲜购物车
    #     RemoveAllProductsInCart().remove_all_products_in_grocery_cart(headers=RequestHeader.ec_login_header)
    #     # 获取用户的porder
    #     porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=RequestHeader.ec_login_header)["object"]
    #     cms_collection = HomeMkplCarousel().top_feed(headers=RequestHeader.ec_login_header)
    #
    #     # assert cms_collection["result"] is True
    #     assert cms_collection["object"]["total_count"] > 0, f'PC-waterfall topx 数据返回异常，{cms_collection}'
    #
    #     products = cms_collection["object"]["products"]
    #     component_metadata = cms_collection["object"]["component_metadata"]
    #     assert component_metadata["event_key"] is not None, f'PC-waterfall topx 数据返回异常，{cms_collection}'
    #     assert component_metadata["sub_title"] is not None, f'PC-waterfall topx 数据返回异常，{cms_collection}'
    #     assert component_metadata["title"] is not None, f'PC-waterfall topx 数据返回异常，{cms_collection}'
    #
    #     tabs = cms_collection["object"]["tabs"]
    #     # PC waterfall页面 topx 模块切换tab
    #     for tab in tabs:
    #         assert tab["title"] is not None, f'mkpl topx 数据返回异常，{cms_collection}'
    #         assert "/mkpl/top-items?tab=" + tab["key"] in tab[
    #             'more_link'], f'mkpl topx 数据返回异常，{cms_collection}'
    #
    #         # PC waterfall页面 topx 模块切换tab
    #         cms_tab = HomeMkplCarousel().top_feed(headers=RequestHeader.ec_login_header,
    #                                               key=tab["key"])
    #
    #         assert cms_tab["object"]["total_count"] > 0, f'mkpl top 没有返回数据，{tab["key"]}下{cms_tab}，'
    #
    #         # 点击查看每个tab下的查看更多按钮
    #         more_link = tab['more_link']
    #         response = requests.get(more_link)
    #         try:
    #             response.status_code == 200
    #         except Exception as e:
    #             log.info(f"{more_link} is not accessible" + str(e))
    #         # 点击see all 进入榜单详情页面
    #         global_ranking_top = HomeMkplCarousel().global_ranking_top_feed(headers=RequestHeader.ec_login_header,
    #                                                                         tab=tab["key"])
    #
    #         assert len(global_ranking_top["object"]) > 0, f'Global+ Top charts 页面数据异常{global_ranking_top}'
    #         for item in global_ranking_top["object"]:
    #             if item["type"] == tab["key"]:
    #                 assert len(item["contents"]) > 0, f'Global+ Top charts 页面数据异常{global_ranking_top}'
    #                 # assert len(item["tabs"]) > 0
    #                 assert item["selected"] is True, f'Global+ Top charts 页面数据异常{global_ranking_top}'
    #                 tabs2 = item["tabs"]
    #                 if len(item["tabs"]) > 0:
    #                     for tab2 in tabs2:
    #                         assert tab2[
    #                                    "icon_active_url"] is not None, f'Global+ Top charts 页面数据异常{global_ranking_top}'
    #                         assert tab2[
    #                                    "icon_url"] is not None, f'Global+ Top charts 页面数据异常{global_ranking_top}'
    #                         assert tab2[
    #                                    "icon_active_url"] is not None, f'Global+ Top charts 页面数据异常{global_ranking_top}'
    #                         # 再点击切换分类tab
    #                         tab2_ranking_top = HomeMkplCarousel().global_ranking_top_feed(
    #                             headers=RequestHeader.ec_login_header,
    #                             tab=tab["key"], key=tab2["key"])
    #                         assert tab2_ranking_top[
    #                                    "object"] is not None, f'TAB{tab2["key"]}Global+ Top charts 页面数据异常{tab2_ranking_top}'
    #
    #     # 获取topx下的第一个生鲜商品
    #     for d_index, product in enumerate(products):
    #         CommonCheck().check_product_info(product=product, category_type="others", filters="others", source="others")
    #         product_id = product["id"]
    #         # 加购搜索结果里的生鲜商品
    #         add_to_cart = UpdatePreOrderLine().porder_items_v3(headers=RequestHeader.ec_login_header,
    #                                                            product_id=product_id,
    #                                                            date=porder["delivery_pickup_date"],
    #                                                            is_pantry=product["is_pantry"],
    #                                                            is_mkpl=product["is_mkpl"],
    #                                                            refer_type="normal",
    #                                                            source="portal-recommend")
    #         # 判断加购成功
    #         assert add_to_cart["result"] is True
    #         # 判断加入生鲜购物车成功
    #         # CheckProductsAddedToCartSuccess().check_product_add_to_cart_success(headers=headers,
    #         #                                                                     cart_domain="grocery",
    #         #                                                                     product_id=product_id)
    #         if d_index == 10:
    #             break


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net', env='tb1')
