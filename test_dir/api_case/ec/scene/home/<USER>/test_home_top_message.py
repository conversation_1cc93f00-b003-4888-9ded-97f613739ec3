# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_user.py
@Description    :
@CreateTime     :  2023/5/16 15:23
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 15:23
"""
import random

import weeeTest

from test_dir.api.ec.ec_mkt.message.messager_list import HomeMessage
from test_dir.api.ec.ec_so.preorder.create_preorder import CreatePreOrder
from test_dir.api_case.ec.common.comm_cms import CommCheckFunction
from test_dir.api_case.ec.common.set_user_porder import SetUserPorder


class TestHomeTopMessage(weeeTest.TestCase):

    @weeeTest.mark.list('anony_user_message', 'Regression', 'Smoke', 'Transaction', 'tb1')
    def test_home_anony_user_message(self, ec_anony_header):
        """ Banner-匿名用户首页top_message验证流程 """
        # 匿名用户之前要创建porder
        CreatePreOrder().create_preorder(headers=ec_anony_header)
        # 获取用户的porder

        porder = SetUserPorder().set_user_porder(headers=ec_anony_header)

        self.home_message(headers=ec_anony_header, user_type="anony_user")

    @weeeTest.mark.list('new_user_message', 'Transaction', 'tb1')
    def test_home_new_user_message(self, ec_signup_header):
        # 获取sign_header, 邮箱用实参传入，否则获取不到邮箱
        email_real = f"{random.randint(10 ** 9, (10 ** 10) - 1)}@autest.com"
        # top_headers = copy.deepcopy(Header().signup_header(email=email_real))
        # 获取用户的porder
        porder = SetUserPorder().set_user_porder(headers=ec_signup_header)
        # 用户获取new_user_message
        new_user_message = HomeMessage().new_user_message(headers=ec_signup_header)
        assert new_user_message["result"] is True
        self.new_user_message_assert(new_user_message["object"])

    @weeeTest.mark.list('Smoke', 'Transaction', 'tb1','test_home_old_user_message')
    def test_home_old_user_message(self, ec_login_header):
        # 获取用户的porder
        porder = SetUserPorder().set_user_porder(headers=ec_login_header)
        # 用户获取home_header_message
        home_header_message = HomeMessage().home_header_message(headers=ec_login_header)
        assert home_header_message["result"] is True
        if home_header_message["object"]:
            for item in home_header_message["object"]:
                self.home_header_message_assert(item)


    def home_message(self, headers, user_type, email_real: str = None):
        # 用户获取top_message
        top_message = HomeMessage().top_message(headers=headers)
        # 用户获取new_user_message
        new_user_message = HomeMessage().new_user_message(headers=headers)

        # 用户获取home_header_message
        home_header_message = HomeMessage().home_header_message(headers=headers)

        # 用户获取get_message
        get_message = HomeMessage().get_message(headers=headers)
        # 用户获取show_message
        show_message = HomeMessage().show_message(headers=headers, value=email_real)
        # 用户获取get_safe_secure_tips
        secure_tips = HomeMessage().get_safe_secure_tips(headers=headers)
        if user_type == "anony_user":
            # 匿名用户
            # 邮箱注册用户
            assert top_message["object"]["id"] is None, f'{user_type}用户top message： {top_message["object"]}'

            # new_user_message断言
            assert new_user_message[
                       "object"] is not None, f'{user_type}用户new user message： {new_user_message["object"]}'

            # 首页轮播图下方的home_header_message断言，同get_message返回一致
            assert home_header_message[
                       "object"] is None, f'{user_type}用户new user message： {new_user_message["object"]}'

            # 首页轮播图下方的message断言
            assert len(
                get_message["object"]["vertical_list"]) == 0, f'{user_type}用户top message： {get_message["object"]}'
            assert len(
                get_message["object"]["beside_list"]) == 0, f'{user_type}用户top message： {get_message["object"]}'

        elif user_type == "signup_email_user":
            # 邮箱注册用户
            assert top_message["object"] is not None, f'{user_type}用户top message： {top_message["object"]}'

            # new_user_message断言
            assert new_user_message[
                       "object"] is not None, f'{user_type}用户new user message： {new_user_message["object"]}'
            assert new_user_message["object"][
                       "top_message"] is not None, f'{user_type}用户new user message： {new_user_message["object"]}'
            assert new_user_message["object"][
                       "top_message_title"] is not None, f'{user_type}用户new user message： {new_user_message["object"]}'

            # 首页轮播图下方的message断言，同get_message返回一致
            assert home_header_message[
                       "object"] is None, f'匿名用户home_header_message： {home_header_message["object"]}'
            assert home_header_message["object"][
                       "vertical_list"] is not None, f'{user_type}用户top message： {get_message["object"]}'
            for home_message in home_header_message["object"]["vertical_list"]:
                assert home_message["link"] is not None, f'{user_type}用户top message： {home_header_message["object"]}'
                assert home_message[
                           "message"] is not None, f'{user_type}用户top message： {home_header_message["object"]}'
                assert home_message[
                           "icon_img"] is not None, f'{user_type}用户top message： {home_header_message["object"]}'
                # 截单提醒 message
                CommCheckFunction().comm_check_link(home_message["link"])
                # 优先显示绑定手机号message
                assert home_message["link"] is not None, f'{user_type}用户top message： {get_message["object"]}'
                assert "/account/phone-connect" in home_message[
                    "link"], f'{user_type}用户top message： {get_message["object"]}'

            # 首页轮播图下方的message断言
            assert get_message["object"][
                       "vertical_list"] is not None, f'{user_type}用户top message： {get_message["object"]}'
            for vertical_list in get_message["object"]["vertical_list"]:
                assert vertical_list["content"][
                           "short_message"] is not None, f'{user_type}用户top message： {get_message["object"]}'
                assert vertical_list["content"][
                           "sub_message"] is not None, f'{user_type}用户top message： {get_message["object"]}'
                assert vertical_list["icon_img"] is not None, f'{user_type}用户top message： {get_message["object"]}'
                # 绑定手机号message
                assert vertical_list["link"] is not None, f'{user_type}用户top message： {get_message["object"]}'
                assert "/account/phone-connect" in vertical_list[
                    "link"], f'{user_type}用户top message： {get_message["object"]}'

            # 邮箱注册后show message 断言
            assert show_message["object"] is not None, f'{user_type}用户top message： {show_message["object"]}'
            assert show_message["object"][
                       "is_first_time_user"] is True, f'{user_type}用户top message： {show_message["object"]}'

        elif user_type == "old_user":
            # 老用户
            assert top_message["object"] is not None, f'{user_type}用户top message： {top_message["object"]}'

            # new_user_message断言
            assert new_user_message[
                       "object"] is not None, f'{user_type}用户new user message： {new_user_message["object"]}'
            assert new_user_message["object"][
                       "top_message"] is not None, f'{user_type}用户new user message： {new_user_message["object"]}'
            assert new_user_message["object"][
                       "top_message_title"] is not None, f'{user_type}用户new user message： {new_user_message["object"]}'

            # 首页轮播图下方的message断言，同get_message返回一致
            if home_header_message["object"] is not None:
                for home_message in home_header_message["object"]["vertical_list"]:
                    assert home_message[
                               "link"] is not None, f'{user_type}用户top message： {home_header_message["object"]}'
                    assert home_message[
                               "message"] is not None, f'{user_type}用户top message： {home_header_message["object"]}'
                    assert home_message[
                               "icon_img"] is not None, f'{user_type}用户top message： {home_header_message["object"]}'
                    # 截单提醒 message
                    CommCheckFunction().comm_check_link(home_message["link"])

            # 首页轮播图下方的message断言
            if len(get_message["object"]["vertical_list"]) > 0:
                for vertical_list in get_message["object"]["vertical_list"]:
                    assert vertical_list["content"][
                               "short_message"] is not None, f'{user_type}用户top message： {get_message["object"]}'
                    assert vertical_list["content"][
                               "sub_message"] is not None, f'{user_type}用户top message： {get_message["object"]}'
                    assert vertical_list["icon_img"] is not None, f'{user_type}用户top message： {get_message["object"]}'
                    assert vertical_list["icon_img"] is not None, f'{user_type}用户top message： {get_message["object"]}'
                    # 截单提醒 message
                    assert vertical_list["link"] is not None, f'{user_type}用户top message： {get_message["object"]}'
                    CommCheckFunction().comm_check_link(vertical_list["link"])

        # 安全可靠支付message 断言
        assert secure_tips["object"] is not None, f"{user_type}用户返回的message是{secure_tips}"
        assert len(secure_tips["object"]) > 0, f"{user_type}用户返回的message是{secure_tips}"
        for message in secure_tips["object"]:
            assert message["short_message"] is not None, f"{user_type}用户返回的message是{secure_tips}"
            assert message["sub_message"] is not None, f"{user_type}用户返回的message是{secure_tips}"

    def new_user_message_assert(self, message):
        assert message["h5_RFMCoupon_home"]
        assert message["h5_RFMCoupon_home"]["bg_color"] is not None
        assert message["h5_RFMCoupon_home"]["content"] is not None
        assert message["h5_RFMCoupon_home"]["link"] is not None
        assert "/account/my-coupons" in message["h5_RFMCoupon_home"]["link"]
        # 新人前2单共可享
        assert message["h5_bindEmailTitle_home"] is not None
        assert message["h5_bindEmailTitle_home"]["content"] is not None
        # 如果您跳过电话认证，您将无法收到该优惠券
        assert message["h5_bindPhoneTip_phone_connect"] is not None
        assert message["h5_bindPhoneTip_phone_connect"]["content"] is not None
        # 绑定手机号可获取$20红包
        assert message["h5_bindPhoneTitle_phone_connect"] is not None
        assert message["h5_bindPhoneTitle_phone_connect"]["content"] is not None
        # 新人前2单共可享$20优惠
        assert message["h5_content_onboarding"] is not None
        assert message["h5_content_onboarding"]["content"] is not None
        # 首单免 $10
        assert message["h5_onboardingDiscount_onboarding"] is not None
        assert message["h5_onboardingDiscount_onboarding"]["content"] is not None
        # 首单满$__FREEFEE__立减$10 & 免运费
        assert message["h5_title_onboarding_experiment"] is not None
        assert message["h5_title_onboarding_experiment"]["content"] is not None
        # 绑定手机号您可获取额外$10红包！
        assert message["h5_topMessageBindPhoneTitle"] is not None
        assert message["h5_topMessageBindPhoneTitle"]["content"] is not None
        # 首单免 $10 < / strong > + 满__FREEFEE__免运费
        assert message["h5_topMessageDelivery_product"] is not None
        assert message["h5_topMessageDelivery_product"]["content"] is not None

        assert message["h5_topMessageShip_product"] is not None
        assert message["h5_topMessageShip_product"]["content"] is not None

        assert message["h5_user_coupon_second_order_home"] is not None
        assert message["h5_user_coupon_second_order_home"]["content"] is not None

        assert message["normalMessage"] == "normalMessage"
        assert message["top_message"] is not None
        assert message["top_message_title"] is not None

    def home_header_message_assert(self, message):
        assert message["background"]["color"] is not None
        assert message["countdown"]["end_time"] is not None
        assert message["icon_img"] is not None
        assert message["link"] is not None
        assert message["message"]["short_message"] is not None
        assert message["open_type"] is not None
        assert message["right_cta"]["img"] is not None
        assert message["type"] is not None
