# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_user.py
@Description    :
@CreateTime     :  2023/5/16 15:23
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 15:23
"""
import copy
import json
from typing import Any

import pytest
import weeeTest
from weeeTest import jmespath, log

from test_dir.api.ec.ec_item.search_v3.search_by_keyword_v3 import SearchByKeywordV3
from test_dir.api.ec.ec_item.suggest.search_hot_keywords import SearchHotKeywords
from test_dir.api.ec.ec_item.suggest.search_suggestion import SearchSuggestion
from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder
from test_dir.api_case.ec.common.comm_check_products_with_cart import CommCheckProductsWithCart
from test_dir.api_case.ec.common.comm_cms import CommCheckFunction
from test_dir.api_case.ec.common.set_user_porder import SetUserPorder


class TestHomeGrocerySearch(weeeTest.TestCase):

    @pytest.fixture(scope='class', autouse=True)
    def setup(self, ec_login_header):
        # 切换用户语言为en
        SetUserPorder().set_user_porder(headers=ec_login_header)

    @weeeTest.mark.list('grocery_history_key_search', 'Regression', 'Smoke', 'Transaction')
    def test_home_grocery_history_key_search(self, ec_login_header):
        """ 搜索-首页大搜索历史词搜索验证流程 """
        # 获取用户的porder
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]

        search_res = SearchByKeywordV3().search_by_keyword_v3(headers=ec_login_header,
                                                              filter_key_word="tofu", lang="en",
                                                              zipcode=porder["zipcode"],
                                                              trigger_type="search_history",
                                                              date=porder["delivery_pickup_date"])
        # 断言搜索结果必有产品
        assert search_res["object"]["total_count"] > 0, f'搜索数据异常{search_res}'

    @weeeTest.mark.list('B2B', 'Regression', 'Smoke', 'Transaction')
    def test_home_grocery_hot_key_search(self, ec_login_header):
        """ 搜索-首页大搜索搜索热词验证流程 """
        # 切换用户语言为en
        SetUserPorder().set_user_porder(headers=ec_login_header)

        # 获取用户的porder
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]
        # 获取热词，热词搜索
        SearchHotKeywords().search_hot_keywords(headers=ec_login_header)
        hot_key = jmespath(self.response, "object.keywords")
        # 断言返回的hotkey 不为空
        assert len(hot_key) > 0, f'搜索数据异常{hot_key}'
        hot_key_check = [
            "Fresh bakery", "Fresh Gourmet"
        ]
        for item in hot_key_check:
            assert item in hot_key_check, f'搜索数据异常{hot_key}'
        # print(hot_key)
        # 根据热词循环进行搜索--拿最后一个热词进行搜索断言
        search_res = SearchByKeywordV3().search_by_keyword_v3(
            headers=ec_login_header,
            filter_key_word=hot_key[-1], lang="en",
            zipcode=porder["zipcode"],
            date=porder["delivery_pickup_date"])["object"]

        # 断言搜索结果必有产品
        assert search_res["total_count"] > 0, f'这个搜索词{hot_key[-1]}返回结果为空，请确认'
        # 断言搜索name 包含热词
        products = search_res["products"]
        # 对返回结果商品进行断言
        self.search_product_assertion(key=hot_key[-1], products=products, headers=ec_login_header)
        # 点击商品卡片,跳转
        CommCheckFunction().comm_check_pdp_link(product_id=products[1]["id"], view_link=products[1]['view_link'], headers=ec_login_header)
        # 验证搜索词建议包含搜索词
        suggestions = SearchSuggestion().search_suggestion(headers=ec_login_header, term=hot_key[-1])
        self.search_suggestions_assertion(key=hot_key[-1], suggestions=suggestions)
        # 加购搜索结果里的商品及断言加购成功
        CommCheckProductsWithCart().products_add_to_cart(headers=ec_login_header, products=products,
                                                         porder_deal_date=porder["delivery_pickup_date"],
                                                         product_source="shopping-search")
        # 对前3个带火标识的特殊热词进行校验
        for index, key in enumerate(hot_key):
            search_res = SearchByKeywordV3().search_by_keyword_v3(
                headers=ec_login_header,
                filter_key_word=key, lang="en",
                zipcode=porder["zipcode"],
                date=porder["delivery_pickup_date"])["object"]

            # 断言搜索结果必有产品
            assert search_res["total_count"] > 0, f'这个搜索词{key}返回结果为空，请确认'
            self.search_hotkey_assertion(key=key, search_res=search_res, headers=ec_login_header)

            if index == 3:
                break

    @weeeTest.mark.list('Regression', 'Transaction')
    def test_home_mo_grocery_hot_key_search(self, ec_login_header):
        """ 搜索-MO地区首页大搜索搜索热词验证流程 """
        # 切换用户zipcode 到99348
        hm_header = copy.deepcopy(ec_login_header)
        SetUserPorder().set_user_zipcode(headers=hm_header, zipcode=99348)

        # 获取用户的porder
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=hm_header)["object"]
        # 获取热词，热词搜索
        SearchHotKeywords().search_hot_keywords(headers=hm_header)
        hot_key = jmespath(self.response, "object.keywords")
        try:
            SetUserPorder().set_user_zipcode(headers=hm_header, zipcode="98011")
        except Exception as e:
            log.info("切换到98011失败" + str(e))
            SetUserPorder().set_user_zipcode(headers=hm_header, zipcode="98011")
        # 断言返回的hotkey 不为空
        assert len(hot_key) == 0

    @weeeTest.mark.list('B2B', 'Regression', 'Smoke', 'Transaction')
    def test_home_grocery_search_sorts(self, ec_login_header):
        """ 搜索-首页大搜索sort验证流程 """
        # 获取用户的porder
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]

        search_res = SearchByKeywordV3().search_by_keyword_v3(headers=ec_login_header,
                                                              filter_key_word="tofu", lang="en",
                                                              zipcode=porder["zipcode"],
                                                              date=porder["delivery_pickup_date"])
        # 断言搜索结果必有产品
        assert search_res["object"]["total_count"] > 0
        sorts = search_res["object"]["sorts"]
        # 对返回结果sorts 排序操作及断言
        self.search_sorts_product_assertion("tofu", porder["zipcode"], porder["delivery_pickup_date"], sorts, headers=ec_login_header)

    @weeeTest.mark.list('B2B', 'Regression', 'Smoke', 'Transaction')
    def test_home_grocery_search_categories(self, ec_login_header):
        """ 搜索-首页大搜索切换子分类tab验证流程 """
        # 获取用户的porder
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]

        search_res = SearchByKeywordV3().search_by_keyword_v3(headers=ec_login_header,
                                                              filter_key_word="tofu", lang="en",
                                                              zipcode=porder["zipcode"],
                                                              date=porder["delivery_pickup_date"])
        # 断言搜索结果必有产品
        assert search_res["object"]["total_count"] > 0
        categories = search_res["object"]["categories"]
        # 对返回结果子分类进行切换操作及断言
        self.search_categories_assertion("tofu", porder["zipcode"], porder["delivery_pickup_date"], categories, headers=ec_login_header)

    @weeeTest.mark.list('B2B', 'Regression', 'Transaction')
    def test_home_grocery_search_filters(self, ec_login_header):
        """ 搜索-首页大搜索filter验证流程 """
        # 获取用户的porder
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]

        search_res = SearchByKeywordV3().search_by_keyword_v3(headers=ec_login_header,
                                                              filter_key_word="tofu", lang="en",
                                                              zipcode=porder["zipcode"],
                                                              date=porder["delivery_pickup_date"])
        # 断言搜索结果必有产品
        assert search_res["object"]["total_count"] > 0
        filters = search_res["object"]["filters"]
        # 对返回结果商品进行filter = delivery_type 操作及断言
        self.search_filters_product_assertion("tofu", porder["zipcode"], porder["delivery_pickup_date"],
                                              filters, headers=ec_login_header)


    def search_suggestions_assertion(self, key, suggestions: dict | Any):

        if key in (
                "Fresh bakery", "Fresh Gourmet", "Blossom Fest", "Matcha Season", "Everday Value", "Summer Essentials"):
            # 断言, 断言不成立， Fresh bakery就没有suggestions 0628手工测试验证
            # 不做推荐断言
            pass

        else:
            # 断言搜索词建议必有数据
            assert len(suggestions["object"]["suggestions"]) > 0 or True
            # 断言搜索建议里返回搜索词 suggestions为None，下面断言忽略
            # assert key in suggestions["object"]["suggestions"][0]["label"], f"Not labels contain {key}"

    def search_hotkey_assertion(self, key, search_res: dict | Any, headers):
        # direct_page_url = search_res["direct_page_url"]
        if key == "Fresh bakery":
            direct_page_url = search_res["direct_page_url"]
            # 断言url 正确
            assert direct_page_url.endswith(
                "/mkpl/bakery/landing"), f'"URL不是以/mkpl/bakery/landing结尾:{direct_page_url}'
            # 点击Matcha Season热词跳转落地页
            CommCheckFunction().comm_check_link(direct_page_url, headers=headers)

        elif key == "Fresh Gourmet":
            direct_page_url = search_res["direct_page_url"]
            # 断言url 正确
            assert direct_page_url.endswith(
                "/mkpl/freshdeli/landing"), f'URL不是以/mkpl/freshdeli/landing结尾:{direct_page_url}'
            # 点击Matcha Season热词跳转落地页
            CommCheckFunction().comm_check_link(direct_page_url, headers=headers)
        elif key == "Everday Value":
            direct_page_url = search_res["direct_page_url"]
            # 断言url 正确
            assert direct_page_url.endswith(
                "/cms/page/activity/globaleverydayvalue"), f'URL不是以/cms/page/activity/globaleverydayvalue结尾:{direct_page_url}'
            # 点击Matcha Season热词跳转落地页
            CommCheckFunction().comm_check_link(direct_page_url, headers=headers)
        elif key == "Summer Essentials":
            direct_page_url = search_res["direct_page_url"]
            # 断言url 正确
            assert direct_page_url.endswith(
                "/cms/page/activity/sunscreen"), f'URL不是以/cms/page/activity/sunscreen结尾:{direct_page_url}'
            # 点击Matcha Season热词跳转落地页
            CommCheckFunction().comm_check_link(direct_page_url, headers=headers)

    def search_product_assertion(self, key, products: list | Any, headers):
        assert any(key.lower() in product["name"].lower() for product in products), f"{key} not found in product name"

        # 对返回结果商品操作及断言
        for index, product in enumerate(products):
            assert "weeecdn" in product["img"]
            assert product["name"] is not None
            assert product["price"] is not None
            assert str(product["id"]) in product['slug'], "这个链接返回的不是这个产品的链接，请确认~"
            # 产品标签tag
            if product["entrance_tag"] is not None:
                more_link = product["entrance_tag"]["more_link"]
                assert product["entrance_tag"]['tag_name'] is not None
                # 验证点击more_link 跳转正常
                CommCheckFunction().comm_check_link(more_link, headers=headers)
            if index == 3:
                break

    def search_categories_assertion(self, key, zipcode, date, categories: list | Any, headers):
        # 对返回结果子分类操作及断言
        for index, categorie in enumerate(categories):
            catalogue_num = categorie["catalogue_num"]

            # 切换分类
            search_res = SearchByKeywordV3().search_by_keyword_v3(headers=headers,
                                                                  filter_key_word=key, lang="en",
                                                                  zipcode=zipcode, date=date,
                                                                  filters=json.dumps({"catalogue_num": catalogue_num}),
                                                                  trigger_type="taxonomy_" + catalogue_num
                                                                  )
            # 断言搜索结果必有产品
            assert search_res["object"]["total_count"] > 0
            products = search_res["object"]["products"]
            # 断言这个商品的分类正确
            assert products[-1]["parent_category"] == catalogue_num
            assert catalogue_num in products[0][
                "category"], f'商品分类不对：{catalogue_num} not in {products[0]["category"]}'
            if index == 3:
                break

    def search_sorts_product_assertion(self, key, zipcode, date, sorts: list | Any, headers):
        # 对返回结果sorts 排序操作及断言
        for sort in sorts:
            # 过滤filter
            sorts_res = SearchByKeywordV3().search_by_keyword_v3(headers=headers,
                                                                 filter_key_word=key, lang="en",
                                                                 zipcode=zipcode, date=date,
                                                                 sort=sort["sort_key"],
                                                                 trigger_type="filter")

            assert sorts_res["object"]["total_count"] > 0
            products = sorts_res["object"]["products"]
            sort_1 = products[0]["price"]
            sort_2 = products[-1]["price"]
            if sort["sort_key"] == "price_asc":
                # 断言商品"价格：低到高"
                assert sort_1 <= sort_2, f"商品价格：低到高 排序不对：{sort_1}<={sort_2}"
            elif sort["sort_key"] == "price_desc":
                # print(products)
                # 断言商品"价格：高到低"
                assert sort_1 >= sort_2, f"商品价格：高到低排序不对：{sort_1}>={sort_2}"

    def search_filters_product_assertion(self, key, zipcode, date, filters: list | Any, headers):
        # 对返回结果Filter进行操作及断言
        for filter in filters:
            property_key = filter["property_key"]
            property_values = filter["property_values"]
            for property_value in property_values:
                filters_res = SearchByKeywordV3().search_by_keyword_v3(headers=headers,
                                                                       filter_key_word=key, lang="en",
                                                                       zipcode=zipcode, date=date,
                                                                       filters=json.dumps(
                                                                           {property_key: property_value["value_key"]}),
                                                                       trigger_type="filter")

                assert filters_res["object"] is not None
                products = filters_res["object"]["products"]
                # delivery_type
                if filter["property_key"] == "delivery_type":
                    assert filters_res["object"]["total_count"] > 0

                    if property_value["value_key"] == "delivery_type_local_mof":
                        # 断言MOF地区的本地配送商品
                        assert products[-1]["is_mkpl"] is False, f"商品返回不是本地配送，请确认~"
                        assert products[-1]["is_pantry"] is False, f"商品返回不是本地配送，请确认~"
                    elif property_value["value_key"] == "delivery_type_fbw":
                        # 断言MOF商品是本地配送商品
                        assert products[-1]["is_mkpl"] is False, f"商品返回不是本地配送，请确认~"
                        assert products[-1]["is_pantry"] is False, f"商品返回不是本地配送，请确认~"
                    elif property_value["value_key"] == "delivery_type_local":
                        # 断言非MOF地区的本地配送商品
                        assert products[-1]["is_mkpl"] is False, f"商品返回不是本地配送，请确认~"
                        assert products[-1]["is_pantry"] is False, f"商品返回不是本地配送，请确认~"
                    elif property_value["value_key"] == "delivery_type_pantry":
                        # 断言商品是pantry 商品
                        assert products[-1]["is_pantry"] is True, f"商品返回不是pantry商品，请确认~"
                    elif property_value["value_key"] == "delivery_type_global":
                        # 断言商品是global+ 商品
                        assert products[-1]["is_mkpl"] is True, f"商品返回不是global商品，请确认~"
                        assert products[-1]["biz_type"] == "seller", f"商品返回不是global商品，请确认~"

                # product_type
                elif filter["property_key"] == "product_type":
                    if property_value["value_key"] == "product_type_sale":
                        # 断言商品是on sale商品,有折扣价
                        if property_value["value_key"] == "product_type_sale":
                            # 断言商品是on sale商品,有折扣价
                            for index, product in enumerate(products):
                                if product["volume_price_support"] is True:
                                    assert product["price"] < product["volume_price"]
                                else:
                                    assert product[
                                               "base_price"] is not None, f'商品{product["id"]}base_price返回为空，请确认{product}'
                                    assert product["base_price"] > product[
                                        "price"], f'商品{product["id"]}:base_price{product["base_price"]},price:{product["price"]}'
                                if index == 2:
                                    break

                        # assert products[-1]["base_price"] is not None, f'{products[-1]["id"]}商品断言失败，请确认~'
                    elif property_value["value_key"] == "product_type_new":
                        # 断言商品是new 商品
                        # 检查label_list中是否有任何一个标签的label_key是'new'
                        assert any('new' == label['label_key'] for label in products[-1]['label_list']), f'{products[-1]["id"]}商品断言失败，请确认~'
                    elif property_value["value_key"] == "product_type_cold_pack":
                        # 断言商品是 冷链商品
                        assert products[-1]["is_colding_package"] is True, f'{products[-1]["id"]}商品断言失败，请确认~'
                # 产地过滤
                elif filter["property_key"] == "5":
                    assert filters_res["object"]["total_count"] > 0, f'{products[-1]["id"]}商品断言失败，请确认~'

                # 价格区间
                elif filter["property_key"] == "6":
                    assert filters_res["object"]["total_count"] > 0
                    # $ 价格区间小于$5
                    if property_value["value_key"] == "1":
                        assert all(product["price"] < 5 for product in
                                   filters_res['object']['products']), f"Not all prices are less than $5"
                    # $ 价格区间 $5-10
                    elif property_value["value_key"] == "2":
                        assert all(5 <= product["price"] < 10 for product in
                                   filters_res['object']['products']), f"Not all prices are in $5-10"

                    # $ 价格区间 $10-15
                    elif property_value["value_key"] == "3":
                        assert all(10 <= product["price"] < 15 for product in
                                   filters_res['object']['products']), f"Not all prices are in $10-15"

                    # $ 价格区间 $15-25
                    elif property_value["value_key"] == "4":
                        assert all(15 <= product["price"] < 25 for product in
                                   filters_res['object']['products']), f"Not all prices are in $15-25"
                    # $ 价格区间高于 $25
                    elif property_value["value_key"] == "5":
                        assert all(product["price"] >= 25 for product in
                                   filters_res['object']['products']), f"Not all prices are more than $25"
                # 商家过滤
                elif filter["property_key"] == "8":
                    assert filters_res["object"]["total_count"] > 0
                    # 断言商品是第三方商家商品
                    if property_value["value_key"] != "0":
                        assert all(str(product["vender_id"]) == property_value["value_key"] for product in
                                   filters_res['object'][
                                       'products']), f'不是所有商品都是这个商家的 {property_value["value_key"]}'


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net', env='tb1')
