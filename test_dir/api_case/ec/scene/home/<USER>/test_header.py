import copy

import pytest
import weeeTest

from test_dir.api.ec.ec_item.store.api_store import ApiStore
from test_dir.api.ec.ec_so.preorder.update_zipcode import UpdateZipcode
from test_dir.api.ec.ec_customer.language_rest.language_rest import LanguageRest
from test_dir.api.ec.ec_so.deliverydates.get_valid_delivery_dates import GetValidDeliveryDates
from test_dir.api.ec.ec_so.preorder.porder_date import PorderDate
from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder

# from allure_commons._allure import step
sales_data = [
    (1, "94501"),
    (2, "90001")]


class TestHeader(weeeTest.TestCase):
    @pytest.mark.parametrize("language", ["en", "zh", "zh-Hant", "ja", "ko", "vi"])
    @pytest.mark.parametrize('sales_id, zipcode', sales_data)
    # @pytest.mark.monitor
    def test_data_check_on_home_page_non_en(self, sales_id, zipcode, language, monitor_header):
        _monitor_header = copy.deepcopy(monitor_header)
        # 1. 切销售组织，更新zipcode
        update_zipcode = UpdateZipcode().update_zipcode_v1(headers=_monitor_header, zipcode=zipcode)
        assert update_zipcode.get("object") == "Success", f'切換zipcode失败，请确认{update_zipcode["object"]}'
        # 1.1 更新header里的zipcode
        _monitor_header.update({
            "Weee-Zipcode": zipcode,
            "Zipcode": zipcode
        })
        # print(_monitor_header)
        # 2. 获取最新日期
        delivery_res = GetValidDeliveryDates().so_delivery_date(headers=_monitor_header)
        # 2.1 改porder 默认拿第一天
        delivery_date = ""
        if delivery_res.get('object').get('delivery'):
            # 默认拿第一天
            delivery_date = delivery_res.get('object').get('delivery')[0].get('date')
            # 最后一天
            new_date = delivery_res.get('object').get('delivery')[-1].get('date')
            # 切换日期
            porder_date = PorderDate().porder_date(headers=_monitor_header,
                                                   delivery_pickup_date=delivery_date)
            # 断言
            assert porder_date["result"] is True
        # 2.2 更新header里的日期
        _monitor_header.update({
            "Weee-Date": delivery_date
        })
        # print(_monitor_header)
        # 3. 更新语言
        _language = LanguageRest().update_account_language(headers=_monitor_header, lang=language)
        assert _language.get("result") is True
        # 3.1 更新header的语言
        _monitor_header.update({
            "Lang": language
        })
        # print(_monitor_header)
        if language == 'en':
            # 4.获取store id
            store_res = ApiStore().store_list(headers=_monitor_header, zipcode=zipcode)
            store = [(item['store_id'], item['store_key']) for item in store_res['object']]
            for store_id, store_key in store:
                # 4.1 更新store
                ApiStore().store_select(headers=_monitor_header, store_id=store_id, zipcode=int(zipcode))
                # 4.2 更新header里的store
                _monitor_header.update({
                    "Weee-Store": store_key
                })
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=_monitor_header)["object"]
