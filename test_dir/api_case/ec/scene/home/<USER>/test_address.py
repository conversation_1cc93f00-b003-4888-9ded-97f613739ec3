# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_user.py
@Description    :
@CreateTime     :  2023/5/16 15:23
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 15:23
"""
import json
import random

import weeeTest

from test_dir.api.ec.ec_so.address.add_or_edit_user_address import AddOrEditUserAddress
from test_dir.api.ec.ec_so.address.delete_address import DeleteUserAddress
from test_dir.api.ec.ec_so.address.query_user_address_list import QueryUserAddressList
from test_dir.api.ec.ec_so.preorder.update_zipcode import UpdateZipcode
from test_dir.api_case.ec.common.common_check import CommonCheck


class TestAddress(weeeTest.TestCase):
    @weeeTest.mark.list('Regression', 'Smoke',  'Transaction')
    def test_query_address(self, ec_login_header):
        """ 地址-query地址功能验证流程 """
        # 查询用户地址列表
        QueryUserAddressList().address_list(headers=ec_login_header)

        # 断言
        assert self.response["result"] is True and self.response[
            'object'], f"用户地址列表为空，返回结果为：{self.response}"
        for addr in self.response['object']:
            CommonCheck.list_check(
                ['address_id', 'address', 'addr_firstname', 'addr_lastname', 'addr_zipcode', 'phone'], addr.keys())

    @weeeTest.mark.list('Regression', 'Transaction')
    def test_add_edit_delete_address(self, ec_login_header):
        """新增-编辑-删除地址"""
        # 1. 新增地址
        AddOrEditUserAddress().so_address(headers=ec_login_header)
        assert self.response["result"] is True and self.response['object'][
            'address_id'], f"新增地址失败，返回结果为：{self.response}"
        CommonCheck.list_check(['address_id', 'address', 'addr_firstname', 'addr_lastname', 'addr_zipcode', 'phone'],
                               self.response['object'].keys())
        address_id = self.response["object"]["address_id"]
        # 应用地址
        update = UpdateZipcode().update_zipcode_v1(headers=ec_login_header,zipcode="98011",
                                                   address_id=address_id)
        assert update["object"] == "Success"
        # 再次查询用户地址列表
        addr_list = QueryUserAddressList().address_list(headers=ec_login_header)
        assert address_id in [item['address_id'] for item in addr_list[
            'object']], f"新增的地址不在地址列表中，address_list为：{addr_list['object']}， 新增地址Id为{address_id}"
        # 2. 修改手机号，姓名，备注等
        AddOrEditUserAddress().so_address(headers=ec_login_header, address_id=address_id,
                                          phone=str(random.randint(2000000000, 9999999999)))
        assert self.response["result"] is True and address_id == self.response["object"][
            "address_id"], f"修改地址后，id不一致，修改前id为{address_id}，修改后为：{self.response['object']['address_id']}"

        # 3. 删除地址
        DeleteUserAddress().delete_address(headers=ec_login_header, address_id=address_id)
        assert self.response['object'] == 'success', f"删除地址失败，id为{address_id}"

        addr_list_after_del = QueryUserAddressList().address_list(headers=ec_login_header)
        assert address_id not in [item['address_id'] for item in addr_list_after_del[
            'object']], f"删除后的地址仍然在地址列表中，address_list为：{addr_list_after_del['object']}， 删除的地址Id为{address_id}"


    @weeeTest.mark.list('B2B','Regression', 'Transaction')
    def test_b2b_add_edit_delete_address(self, ec_login_header):
        """新增-编辑-删除地址"""
        # 1. 新增地址
        AddOrEditUserAddress().so_address(headers=ec_login_header)
        assert self.response["result"] is True and self.response['object'][
            'address_id'], f"新增地址失败，返回结果为：{self.response}"
        CommonCheck.list_check(['address_id', 'address', 'addr_firstname', 'addr_lastname', 'addr_zipcode', 'phone'],
                               self.response['object'].keys())
        address_id = self.response["object"]["address_id"]
        # 应用地址
        update = UpdateZipcode().update_zipcode_v1(headers=ec_login_header,zipcode="98011",
                                                   address_id=address_id)
        assert update["object"] == "Success"
        # 再次查询用户地址列表
        addr_list = QueryUserAddressList().address_list(headers=ec_login_header)
        assert address_id in [item['address_id'] for item in addr_list[
            'object']], f"新增的地址不在地址列表中，address_list为：{addr_list['object']}， 新增地址Id为{address_id}"
        # 2. 修改手机号，姓名，备注等
        AddOrEditUserAddress().so_address(headers=ec_login_header, address_id=address_id,
                                          phone=str(random.randint(2000000000, 9999999999)))
        assert self.response["result"] is True and address_id == self.response["object"][
            "address_id"], f"修改地址后，id不一致，修改前id为{address_id}，修改后为：{self.response['object']['address_id']}"

        # 3. 删除地址
        DeleteUserAddress().delete_address(headers=ec_login_header, address_id=address_id)
        assert self.response['object'] == 'success', f"删除地址失败，id为{address_id}"

        addr_list_after_del = QueryUserAddressList().address_list(headers=ec_login_header)
        assert address_id not in [item['address_id'] for item in addr_list_after_del[
            'object']], f"删除后的地址仍然在地址列表中，address_list为：{addr_list_after_del['object']}， 删除的地址Id为{address_id}"
