# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_user.py
@Description    :
@CreateTime     :  2023/5/16 15:23
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 15:23
"""
import json

import weeeTest

from test_dir.api.ec.ec_item.lighting_deals.lightning_deals_v2 import ApiLightningDeals
from test_dir.api.ec.ec_item.product.pdp_detail import PdpDetail
from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder
from test_dir.api_case.ec.common.comm_check_products_with_cart import CommCheckProductsWithCart
from test_dir.api_case.ec.common.comm_cms import CommCheckFunction


class TestHomeGroceryLightDealsV2(weeeTest.TestCase):

    @weeeTest.data.file(file_name='ec_item_data.json', return_type='dict')
    @weeeTest.mark.list('grocery_lighting', 'Regression',  'Transaction', 'product', 'lighting')
    def test_home_grocery_lighting_deals_v2(self, *args, ec_login_header):
        """ 秒杀-秒杀相关验证流程 """
        # 清除生鲜购物车
        # RemoveAllProductsInCart().remove_all_products_in_grocery_cart(headers=headers)
        # SetUserPorder().set_user_zipcode(headers=RequestHeader.ec_login_header, zipcode=10026)
        # 获取用户的porder
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]

        sales_org_id = porder["sales_org_id"]
        # print(sales_org_id)
        deal_date = porder["delivery_pickup_date"]
        delivery_date = porder["delivery_pickup_date"]
        zipcode = porder["zipcode"]
        data = args[0]["category"]["search_by_catalogue"]
        deals_info = ApiLightningDeals().lightning_deals_info(headers=ec_login_header,
                                                              date=delivery_date, zipcode=zipcode)
        assert deals_info["object"] is not None
        # 有正在进行的秒杀
        if deals_info["object"]["ongoing_exist"] is True:

            # 秒杀商品列表(首页)
            deals_home = ApiLightningDeals().lightning_deals_home(headers=ec_login_header,
                                                                  date=delivery_date, zipcode=zipcode)
            assert deals_home["object"]["total_count"] > 0
            assert deals_home["object"]["title"] is not None
            compon = deals_home["object"]["component_metadata"]
            assert compon["icon_url"] is not None
            assert compon["link_url"] is not None
            assert compon["more_link"] is not None
            assert compon["title"] is not None
            products = deals_home["object"]["products"]
            # 首页查看更多
            more_link = deals_home["object"]["more_link"]
            assert "/promotion/lightning-deals" in more_link

            # 返回第一个秒杀商品pdp的所有信息
            pdp_detail = PdpDetail().pdp_detail(headers=ec_login_header,
                                                product_id=products[0]["id"],
                                                zipcode=porder["zipcode"], sales_org_id=porder["sales_org_id"])
            assert pdp_detail["object"][
                       "product"] is not None, f'该商品{products[0]["id"]}未返回商品信息，请确认{pdp_detail["object"]}'
            # 断言pdp 秒杀倒计时
            special_price_today = pdp_detail["object"]["product"]["special_price_today"]
            # 断言pdp 秒杀已开始
            assert special_price_today[
                       "status"] == 1, f'该商品{products[0]["id"]}未返回商品信息，请确认{pdp_detail["object"]}'
            assert special_price_today["start_time"] < special_price_today[
                "current_timestamp"], f'该商品{products[0]["id"]}未返回商品信息，请确认{pdp_detail["object"]}'
            assert special_price_today["end_time"] > special_price_today[
                "current_timestamp"], f'该商品{products[0]["id"]} 未返回商品信息，请确认{pdp_detail["object"]}'
            if pdp_detail["object"]["product"]["volume_price_support"] is True:
                assert pdp_detail["object"]["product"]["volume_price"] > pdp_detail["object"]["product"][
                    "price"], f'该商品{products[0]["id"]} 未返回商品信息，请确认{pdp_detail["object"]}'
            else:
                assert special_price_today["base_price"] > special_price_today[
                    "special_price"], f'该商品{products[0]["id"]} 未返回商品信息，请确认{pdp_detail["object"]}'
            # log.debug(pdp_detail["object"])
            assert special_price_today[
                       "show_progress"] is False, f'该商品{products[0]["id"]}未返回商品信息，请确认{pdp_detail["object"]}'

            # 点击跳转
            CommCheckFunction().comm_check_link(more_link, headers=ec_login_header)
            # 首页秒杀商品断言及操作
            self.lighting_deals_product_assertion(products=deals_home["object"]["products"], headers=ec_login_header)
            # 秒杀商品列表查询 - 列表页
            started = ApiLightningDeals().lightning_deals_product_list(headers=ec_login_header,
                                                                       status=1, date=delivery_date, zipcode=zipcode)
            assert started["object"]["total_count"] > 0, f'秒杀没有返回商品：{started["object"]}'
            # 切换秒杀分类
            categories = started["object"]["categories"]
            for categorie in categories:
                started = ApiLightningDeals().lightning_deals_product_list(headers=ec_login_header,
                                                                           status=1, date=delivery_date,
                                                                           zipcode=zipcode,
                                                                           filter=json.dumps(
                                                                               {"catalogue_num": categorie[
                                                                                   "catalogue_num"]})
                                                                           )
                assert started["result"] is True
                assert started["object"] is not None and started["object"]["products"]
                assert started["object"]["total_count"] > 0, f'{categorie["catalogue_num"]}秒杀没有返回商品：{started["object"]}'

            # 存在秒杀加购
            CommCheckProductsWithCart().products_add_to_cart(headers=ec_login_header,
                                                             products=started["object"]["products"],
                                                             porder_deal_date=porder["delivery_pickup_date"],
                                                             product_source="special")

        # 存在未开始的秒杀
        elif deals_info["object"]["begoing_exist"] is True:
            # 秒杀商品列表(首页)
            deals_home = ApiLightningDeals().lightning_deals_home(headers=ec_login_header,
                                                                  date=delivery_date, zipcode=zipcode)
            # 未开始，首页就不会返回秒杀
            assert deals_home["object"]["total_count"] is None
            # 查询未开始的秒杀-列表页面未开始还是会返回
            not_start = ApiLightningDeals().lightning_deals_product_list(headers=ec_login_header,
                                                                         status=0, date=delivery_date, zipcode=zipcode)
            assert not_start["object"]["total_count"] > 0, f'秒杀未开始，列表页接口未返回数据{not_start}'

            # 存在秒杀
            products = not_start["object"]["products"]
            for product in products:
                # 秒杀提醒
                remind_me = ApiLightningDeals().lightning_deals_remind_me_v2(headers=ec_login_header,
                                                                             product_id=product["id"])
                assert remind_me["result"] is True
                # 取消秒杀提醒
                ApiLightningDeals().lightning_deals_remind_me_v2(headers=ec_login_header,
                                                                 product_id=product["id"], status="X")
                assert self.response["result"] is True
                # 秒杀提醒
                ApiLightningDeals().lightning_deals_remind_me_v2(headers=ec_login_header,
                                                                 product_id=product["id"])
                assert self.response["result"] is True

        else:
            print("没有配置秒杀，请先配置")

    def lighting_deals_product_assertion(self, products: list, headers):
        # 对返回结果商品操作及断言
        for product in products:
            assert product["name"] is not None, f'商品{product["id"]}name返回为空，请确认{product}'
            assert product["img"] is not None, f'商品{product["id"]}img返回为空，请确认{product}'
            assert "weeecdn" in product["img"]
            assert product["square_img_url"] is not None, f'商品{product["id"]}square_img_url返回为空，请确认{product}'
            assert product["media_urls"] is not None, f'商品{product["id"]}media_urls返回为空，请确认{product}'
            assert product["price"] is not None, f'商品{product["id"]}price返回为空，请确认{product}'
            if product["volume_price_support"] is True:
                assert product["price"] < product["volume_price"]
            else:
                assert product["base_price"] is not None, f'商品{product["id"]}base_price返回为空，请确认{product}'
                assert product["base_price"] > product[
                    "price"], f'商品{product["id"]}:base_price{product["base_price"]},price:{product["price"]}'
            assert product["sold_status"] == "available", f'商品{product["id"]}sold_status不对，请确认{product}'
            assert product["status"] == "ongoing", f'商品{product["id"]}status 不等于"ongoing"，请确认{product}'
            assert product["end_time"] is not None, f'商品{product["id"]}end_time返回为空，请确认{product}'
            assert any(item["label_key"] == "off" for item in
                       product["label_list"]), f'商品{product["id"]}:label_list:{product["label_list"]}，请确认{product}'
            # 点击秒杀
            # 点击跳转
            CommCheckFunction().comm_check_link(product["view_link"], headers=headers)
            # 点击产品卡片
            # 点击跳转
            CommCheckFunction().comm_check_link(product["link"], headers=headers)



