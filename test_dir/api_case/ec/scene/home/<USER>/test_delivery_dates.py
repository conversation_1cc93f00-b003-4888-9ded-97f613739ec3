# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_user.py
@Description    :
@CreateTime     :  2023/5/16 15:23
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 15:23
"""
import copy
import json
from datetime import datetime, timedelta

import pytest
import weeeTest

from test_dir.api.ec.ec_item.search_v3.search_by_catalogue_content import SearchByCatalogueContent
from test_dir.api.ec.ec_so.deliverydates.get_valid_delivery_dates import GetValidDeliveryDates
from test_dir.api.ec.ec_so.preorder.porder_date import PorderDate
from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder
from test_dir.api_case.ec.common.common_check import CommonCheck
from test_dir.api_case.ec.common.set_user_porder import SetUserPorder


class TestDeliveryDates(weeeTest.TestCase):
    mof_header = None
    @pytest.fixture(scope="function", autouse=True)
    def setup_header(self):
        yield
        SetUserPorder().set_user_zipcode(headers=self.mof_header, zipcode="98011")


    @weeeTest.mark.list('Regression', 'Smoke',  'Transaction')
    def test_change_delivery_dates(self, ec_login_header):
        """ 各模块切换日期验证流程 """
        # 切换用户zipcode 到98011
        self.mof_header = copy.deepcopy(ec_login_header)
        SetUserPorder().set_user_zipcode(headers=self.mof_header, zipcode="98011")
        # 获取用户的porder
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]
        reset_delivery_pick_up_date = porder['delivery_pickup_date']
        # 获取new 分类数据
        new = SearchByCatalogueContent().search_by_catalogue_content(
            headers=ec_login_header,
            filter_sub_category="new",
            filters=json.dumps({"delivery": True})
        )
        product_ids = [item['data']['id'] for item in new["object"]["contents"] if
                       item['type'] == 'product' and item['data']['sold_status'] == 'available']
        assert product_ids, f"category下没有可用商品，response={new}"
        # 查询可用的配送日期
        delivery_date = GetValidDeliveryDates().so_delivery_date(headers=ec_login_header)
        # 断言
        assert delivery_date["result"] is True and delivery_date['object'][
            'delivery'], f"获取用户delivery date失败，返回结果为{delivery_date}"
        assert CommonCheck.list_check(['pickup', 'delivery', 'shipping_free_fee_global', 'tip'],
                                      delivery_date['object'].keys())
        delivery = delivery_date["object"]["delivery"]

        # 首页/购物车/结算页/再来一单 切换日期用的是同一个接口
        for date in delivery:
            porder_date = PorderDate().porder_date(headers=ec_login_header,
                                                   delivery_pickup_date=date["date"])
            # 断言
            assert porder_date["result"] is True, f"切换购物车日期失败，接口返回结果为：{porder_date}"

        # 重置送货日期，以防止影响其他用例
        PorderDate().porder_date(headers=ec_login_header,
                                 delivery_pickup_date=reset_delivery_pick_up_date)
        assert self.response["result"] is True, f"切回购物车日期失败，接口返回结果为：{self.response}"

        # 查询商品的可用的配送日期（product_id是否用动态id?)
        GetValidDeliveryDates().so_delivery_date_item(headers=ec_login_header, product_id=product_ids[0])
        # 断言
        assert self.response["result"] is True and self.response['object'][
            'delivery'], f'切换商品{product_ids[0]}的配送日期失败，response={self.response}'
        # 商品的切换日期
        delivery = self.response["object"]["delivery"]
        # 切换日期
        for date in delivery:
            PorderDate().porder_date(headers=ec_login_header, delivery_pickup_date=date["date"])
            # 断言
            assert self.response["result"] is True, f"切换商品61600购物车日期失败，接口返回结果为：{self.response}"
        # 重置送货日期，以防止影响其他用例
        PorderDate().porder_date(headers=ec_login_header,
                                 delivery_pickup_date=delivery[0]['date'])
        assert self.response["result"] is True, f"切回购物车日期失败，接口返回结果为：{self.response}"

    @weeeTest.mark.list('B2B','Regression', 'Smoke',  'Transaction')
    def test_b2b_change_delivery_dates(self, ec_login_header):
        """ 各模块切换日期验证流程 """
        # 切换用户zipcode 到98011
        self.mof_header = copy.deepcopy(ec_login_header)
        SetUserPorder().set_user_zipcode(headers=self.mof_header, zipcode="94538")
        # 获取用户的porder
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]
        reset_delivery_pick_up_date = porder['delivery_pickup_date']
        # 获取new 分类数据
        new = SearchByCatalogueContent().search_by_catalogue_content(
            headers=ec_login_header,
            filter_sub_category="new",
            filters=json.dumps({"delivery": True})
        )
        product_ids = [item['data']['id'] for item in new["object"]["contents"] if
                       item['type'] == 'product' and item['data']['sold_status'] == 'available']
        assert product_ids, f"category下没有可用商品，response={new}"
        # 查询可用的配送日期
        delivery_date = GetValidDeliveryDates().so_delivery_date(headers=ec_login_header)
        # 断言
        assert delivery_date["result"] is True and delivery_date['object'][
            'delivery'], f"获取用户delivery date失败，返回结果为{delivery_date}"
        assert CommonCheck.list_check(['pickup', 'delivery', 'shipping_free_fee_global', 'tip'],
                                      delivery_date['object'].keys())
        delivery = delivery_date["object"]["delivery"]

        # 首页/购物车/结算页/再来一单 切换日期用的是同一个接口
        for date in delivery:
            porder_date = PorderDate().porder_date(headers=ec_login_header,
                                                   delivery_pickup_date=date["date"])
            # 断言
            assert porder_date["result"] is True, f"切换购物车日期失败，接口返回结果为：{porder_date}"

        # 重置送货日期，以防止影响其他用例
        PorderDate().porder_date(headers=ec_login_header,
                                 delivery_pickup_date=reset_delivery_pick_up_date)
        assert self.response["result"] is True, f"切回购物车日期失败，接口返回结果为：{self.response}"

        # 查询商品的可用的配送日期（product_id是否用动态id?)
        GetValidDeliveryDates().so_delivery_date_item(headers=ec_login_header, product_id=product_ids[0])
        # 断言
        assert self.response["result"] is True and self.response['object'][
            'delivery'], f'切换商品{product_ids[0]}的配送日期失败，response={self.response}'
        # 商品的切换日期
        delivery = self.response["object"]["delivery"]
        # 切换日期
        for date in delivery:
            PorderDate().porder_date(headers=ec_login_header, delivery_pickup_date=date["date"])
            # 断言
            assert self.response["result"] is True, f"切换商品61600购物车日期失败，接口返回结果为：{self.response}"
        # 重置送货日期，以防止影响其他用例
        PorderDate().porder_date(headers=ec_login_header,
                                 delivery_pickup_date=delivery[0]['date'])
        assert self.response["result"] is True, f"切回购物车日期失败，接口返回结果为：{self.response}"

    @weeeTest.mark.list('Regression', 'Smoke',  'Transaction')
    # mof需求变动，待恢复
    def test_mof_delivery_dates(self, ec_login_header):
        """ MOF日期显示逻辑验证 """
        # 切换zipcode 至MOF
        # 切换用户zipcode 到99348
        self.mof_header = copy.deepcopy(ec_login_header)
        SetUserPorder().set_user_zipcode(headers=self.mof_header, zipcode="49417")
        # 获取用户的porder
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=self.mof_header)["object"]
        delivery_pickup_date = porder["delivery_pickup_date"]
        delivery_date = porder["delivery_date"]
        # 将日期字符串转换为 datetime 对象
        delivery_date = datetime.strptime(delivery_date, "%Y-%m-%d")
        delivery_pickup_date = datetime.strptime(delivery_pickup_date, "%Y-%m-%d")

        # 计算 delivery_date 加一天的日期
        day_after_delivery_date = delivery_date + timedelta(days=1)

        # 断言 day_after_delivery_date 等于 delivery_pickup_date
        assert day_after_delivery_date == delivery_pickup_date, "断言失败：delivery_date 加一天不等于 delivery_pickup_date"

        # 查询可用的配送日期
        delivery_date = GetValidDeliveryDates().so_delivery_date(headers=self.mof_header)

        assert delivery_date["result"] is True, f"接口返回结果为：{delivery_date}"

        assert len(delivery_date["object"]["delivery"]) > 1, f'接口返回结果为：{delivery_date}'

    @weeeTest.mark.list('mo_delivery_dates', 'Regression', 'Smoke',  'Transaction')
    def test_mo_delivery_dates(self, ec_login_header):
        """ MO日期显示逻辑验证 """
        # 切换zipcode 至MO
        # 切换用户zipcode 到99348
        self.mof_header = copy.deepcopy(ec_login_header)
        SetUserPorder().set_user_zipcode(headers=self.mof_header, zipcode="99348")
        # 获取用户的porder
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=self.mof_header)["object"]
        # 查询可用的配送日期
        delivery_date = GetValidDeliveryDates().so_delivery_date(headers=self.mof_header)
        assert delivery_date["result"] is True, f"接口返回结果为：{delivery_date}"
        # 断言MO 的日期只返回以一个日期
        assert len(delivery_date["object"]["delivery"]) == 1, f'接口返回结果为：{delivery_date}'

    @weeeTest.mark.list('B2B','Regression', 'Smoke',  'Transaction')
    def test_normal_delivery_dates(self, ec_login_header):
        """ 非MO/MOF地区日期显示逻辑验证 """
        # 切换zipcode 至MOF
        # 切换用户zipcode 到99348
        self.mof_header = copy.deepcopy(ec_login_header)
        SetUserPorder().set_user_zipcode(headers=self.mof_header, zipcode="94538")
        # 获取用户的porder
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]
        delivery_pickup_date = porder["delivery_pickup_date"]
        delivery_date = porder["delivery_date"]
        assert delivery_date == delivery_pickup_date
        # 查询可用的配送日期
        delivery_date = GetValidDeliveryDates().so_delivery_date(headers=ec_login_header)

        assert delivery_date["result"] is True, f"接口返回结果为：{delivery_date}"
        assert 1 < len(delivery_date["object"]["delivery"]) < 15, f'接口返回结果为：{delivery_date}'
