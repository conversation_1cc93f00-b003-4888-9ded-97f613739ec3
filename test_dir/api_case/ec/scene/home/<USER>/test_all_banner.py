# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_user.py
@Description    :
@CreateTime     :  2023/5/16 15:23
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 15:23
"""
from typing import Any

import weeeTest
from weeeTest import log
import json

from qa_config.ec_sql import EcDB
from test_dir.api.ec.ec_customer.language_rest.language_rest import LanguageRest
from test_dir.api.ec.ec_item.search_v3.search_by_catalogue_content import SearchByCatalogueContent
from test_dir.api.ec.ec_mkt.banner.get_banner_list import Get<PERSON>annerList
from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder
from test_dir.api.ec.ec_so.preorder.update_zipcode import UpdateZipcode
from test_dir.api_case.ec.common.comm_cms import CommCheckFunction


class TestAllBanner(weeeTest.TestCase):

    @weeeTest.data.file(file_name='ec_item_data.json', return_type='dict')
    @weeeTest.mark.list('test_all_banners','Transaction','tb1')
    def test_all_banners(self, *args, ec_login_header):
        """ Bnaner-根据不同销售组织/语言/访问各模块的banner验证流程 """
        # 1.获取登录header
        # 获取不同销售组织下不同zipcode
        sales_zipcode = json.loads(EcDB().get_sales_org_id_zipcode())
        sales_data = args[0]["sales_data_main"]
        for sale_data in sales_data:
            zipcode = sale_data["zipcode"]
            # 切换不同销售组织的zipcode
            update_zipcode = UpdateZipcode().update_zipcode_v1(headers=ec_login_header, zipcode=zipcode)
            if update_zipcode["message_id"] == "SO90029":
                print("这个地区没有帖子")
            else:
                # 断言
                assert update_zipcode["object"] == 'Success'
                # 切换语言
                lang = ["zh", "zh-Hant", "ja", "ko", "vi", "en"]
                for lang in lang:
                    print(f'"当前zipcode:"{zipcode},"语言："{lang}')
                    lang = LanguageRest().update_account_language(headers=ec_login_header, lang=lang)

                    # 获取用户的preorder
                    porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)[
                        "object"]

                    # 1、获取首页轮播图
                    banner_list = GetBannerList().get_banner_list(headers=ec_login_header,
                                                                  type="carousel",
                                                                  dataobject_key="ds_main_banner",
                                                                  sales_org_id=porder["sales_org_id"],
                                                                  lang=lang,
                                                                  date=porder["delivery_pickup_date"])
                    assert banner_list["object"] is not None

                    carousel_banner = banner_list["object"]["carousel"]
                    self.carousel_banner_assertion(carousel_banner)

                    # 2.1 获取H5首页广告位
                    banner_list = GetBannerList().get_banner_list(headers=ec_login_header,
                                                                  type="portal_banner",
                                                                  sales_org_id=porder["sales_org_id"],
                                                                  lang=lang, date=porder["delivery_pickup_date"])
                    assert banner_list["result"] is True
                    if banner_list["object"]["carousel"] is None:
                        log.info("请确认是否有配置H5广告位")
                    else:
                        self.portal_banner_assertion(banner_list["object"]["carousel"])

                    # 2.2 获取PC首页广告位
                    banner_list = GetBannerList().get_banner_list(headers=ec_login_header,
                                                                  type="portal_top",
                                                                  dataobject_key="ds_notice_banner",
                                                                  sales_org_id=porder["sales_org_id"], lang=lang,
                                                                  date=porder["delivery_pickup_date"])
                    assert banner_list["result"] is True
                    if banner_list["object"]["carousel"] is None:
                        log.info("请确认是否有配置PC广告位")
                    else:
                        self.portal_top_banner_assertion(banner_list["object"]["carousel"])

                    # 3、获取2*2  banner
                    banner_list = GetBannerList().banner_component(headers=ec_login_header,
                                                                   zipcode=porder["zipcode"], lang=lang,
                                                                   date=porder["delivery_pickup_date"])
                    assert banner_list["object"] is not None, f'2x2 banner 信息{banner_list["object"]}'
                    if len(banner_list["object"]["data"]) == 0:
                        assert banner_list["object"]["rows"] == 0
                        log.info("请确认是否有配置2*2 banner")
                    else:
                        # 断言有2*2 banner,并访问轮播图链接
                        self.component_banner_assertion(banner_list["object"]["rows"], banner_list["object"]["data"])

                    # 4、获取分类banner
                    categories = ["trending", "new", "sale"]
                    for item in categories:
                        category = SearchByCatalogueContent().search_by_catalogue_content(
                            headers=ec_login_header,
                            filter_sub_category=item,
                            date=porder["delivery_pickup_date"],
                            zipcode=porder["zipcode"])
                        assert category["object"] is not None
                        # banner
                        self.category_banner_assertion(category["object"]["contents"])

                    # 5、获取Account  banner
                    banner_list = GetBannerList().get_banner_list(headers=ec_login_header,
                                                                  type="me_page",
                                                                  dataobject_key="ds_main_banner",
                                                                  sales_org_id=porder["sales_org_id"],
                                                                  lang="en",
                                                                  date=porder["delivery_pickup_date"])

                    assert banner_list["object"] is not None
                    carousel_banner = banner_list["object"]["carousel"]
                    # 断言有首页轮播图,并访问轮播图链接
                    self.me_page_banner_assertion(carousel_banner)

    def carousel_banner_assertion(self, carousel_banner: list | Any):
        for carousel in carousel_banner:
            assert carousel['img_url'] is not None
            assert carousel['detail'] is not None
            # assert carousel['type'] == "activity"
            # 断言有首页轮播图,并访问轮播图链接
            url = carousel['detail']
            # 点击跳转
            CommCheckFunction().comm_check_link(url)

    def portal_banner_assertion(self, carousel_banner: list | Any):
        for carousel in carousel_banner:
            assert carousel['img_url'] is not None
            assert carousel['link_url'] is not None
            assert carousel['key'] == "portal_top"
            # 断言有轮播图,并访问轮播图链接
            url = carousel['link_url']
            # 点击跳转
            CommCheckFunction().comm_check_link(url)

    def portal_top_banner_assertion(self, carousel_banner: list | Any):
        for carousel in carousel_banner:
            assert carousel['img_url'] is not None
            assert carousel['link_url'] is not None
            assert carousel['key'] == "portal_top"
            # 断言有轮播图,并访问轮播图链接
            url = carousel['link_url']
            # 点击跳转
            CommCheckFunction().comm_check_link(url)

    def component_banner_assertion(self, rows, carousel_banner: list | Any):
        for carousel in carousel_banner:
            assert carousel['img_url'] is not None
            assert carousel['link_url'] is not None
            # 断言有2*2 banner,并访问轮播图链接
            url = carousel['link_url']
            # 点击跳转
            CommCheckFunction().comm_check_link(url)
        # 断言banner 返回行数
        if len(carousel_banner) <= 2:
            assert rows == 1
        else:
            assert rows == 2

    def category_banner_assertion(self, contents: dict | Any):

        for content in contents:
            if content["type"] == "carousel":
                data = content["data"]
                for carousel in data:
                    assert carousel['img_url'] is not None
                    assert carousel['url'] is not None
                    url = carousel['url']
                    # 并访问轮播图链接
                    CommCheckFunction().comm_check_link(url)

    def theme_banner_assertion(self, carousel: dict | Any):
        assert carousel['img_url'] is not None
        assert carousel['detail'] is not None
        assert carousel['type'] == "activity"
        # 断言有主题banner,并访问轮播图链接
        url = carousel['detail']
        # 点击跳转
        CommCheckFunction().comm_check_link(url)

    def billboard_banner_assertion(self, carousel_banner: list | Any):
        for carousel in carousel_banner:
            assert carousel['img_url'] is not None
            assert carousel['detail'] is not None
            # 断言有首页轮播图,并访问轮播图链接
            url = carousel['detail']
            # 点击跳转
            CommCheckFunction().comm_check_link(url)

    def me_page_banner_assertion(self, carousel_banner: list | Any):
        for carousel in carousel_banner:
            assert carousel['img_url'] is not None
            assert carousel['detail'] is not None
            # assert carousel['key'] == "portal_top"
            # 断言有轮播图,并访问轮播图链接
            url = carousel['detail']
            # 点击跳转
            CommCheckFunction().comm_check_link(url)







if __name__ == '__main__':
    weeeTest.main(base_url='https://api.sayweee.net', env='pro')
