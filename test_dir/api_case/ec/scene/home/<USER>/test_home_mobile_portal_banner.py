# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_user.py
@Description    :
@CreateTime     :  2023/5/16 15:23
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 15:23
"""
from typing import Any

import weeeTest
from weeeTest import log
from test_dir.api.ec.ec_mkt.banner.get_banner_list import GetBannerList
from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder
from test_dir.api_case.ec.common.comm_cms import CommCheckFunction


class TestHomeMobilePortalBanner(weeeTest.TestCase):

    @weeeTest.mark.list('Regression', 'Smoke',  'Transaction')
    def test_home_mobile_portal_banner(self, ec_login_header):
        """ Banner-Mobile首页广告位验证流程 """
        # 获取用户的porder
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]
        # 获取H5首页广告位
        # 根据销售组织，语言获取轮播图，待优化
        banner_list = GetBannerList().get_banner_list(headers=ec_login_header,
                                                      type="portal_banner",
                                                      sales_org_id=porder["sales_org_id"],
                                                      lang="en", date=porder["delivery_pickup_date"])
        assert banner_list["result"] is True
        assert banner_list["object"]["carousel"],f'没有配置H5广告位'
        if banner_list["object"]["carousel"] is None:
            log.info("请确认是否有配置H5广告位")
        else:
            self.portal_banner_assertion(banner_list["object"]["carousel"], headers=ec_login_header)

    def portal_banner_assertion(self, carousel_banner: list | Any, headers):
        for carousel in carousel_banner:
            assert carousel['img_url'] is not None
            assert carousel['link_url'] is not None
            assert carousel['key'] == "portal_top"
            # 断言有轮播图,并访问轮播图链接
            url = carousel['link_url']
            # 点击跳转
            CommCheckFunction().comm_check_link(url, headers=headers)


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net', env='tb1')
