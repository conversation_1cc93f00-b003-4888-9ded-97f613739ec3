# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_user.py
@Description    :
@CreateTime     :  2023/5/16 15:23
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/16 15:23
"""
from typing import Any

import weeeTest
from weeeTest import log
from test_dir.api.ec.ec_mkt.banner.get_banner_list import GetBannerList
from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder
from test_dir.api_case.ec.common.comm_cms import CommCheckFunction


class TestHome2X2Banner(weeeTest.TestCase):

    @weeeTest.mark.list('home_2x2_banner','Regression', 'Smoke',  'Transaction')
    def test_home_2x2_banner(self, ec_login_header):
        """ Banner-首页2x2 banner验证流程 """
        # 获取用户的porder
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=ec_login_header)["object"]
        # 获取2*2  banner
        banner_list = GetBannerList().banner_component(headers=ec_login_header,
                                                       zipcode=porder["zipcode"],
                                                       date=porder["delivery_pickup_date"])
        assert banner_list["object"] is not None,f'2x2 banner 信息{banner_list["object"]}'
        if len(banner_list["object"]["data"]) == 0:
            assert banner_list["object"]["rows"] == 0
            log.info("请确认是否有配置2*2 banner")
        else:
            # 断言有2*2 banner,并访问轮播图链接
            self.component_banner_assertion(banner_list["object"]["rows"], banner_list["object"]["data"])

    def component_banner_assertion(self, rows, carousel_banner: list | Any):
        for carousel in carousel_banner:
            assert carousel['img_url'] is not None
            assert carousel['link_url'] is not None
            # 断言有2*2 banner,并访问轮播图链接
            # 点击跳转
            CommCheckFunction().comm_check_link(carousel['link_url'])
        # 断言banner 返回行数
        if len(carousel_banner) <= 2:
            assert rows == 1
        else:
            assert rows == 2


