"""
<AUTHOR>  suqin
@Version        :  V2.0.0
------------------------------------
@File           :  test_all_sales_regions_data_check_v2.py
@Description    :  优化版本的销售区域数据检查测试
@CreateTime     :  2023/11/20 16:52
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2024/07/10 10:00
"""
import json
import time

import pytest
import weeeTest
from weeeTest import log

from test_dir.api.ec.ec_inventory.api_inventory import ApiInventory
from test_dir.api.ec.ec_item.product.pdp_detail import PdpDetail
from test_dir.api.ec.ec_item.search_v3.search_by_catalogue_content import SearchByCatalogueContent
from test_dir.api.ec.ec_so.preorder.query_simple_preorder import QuerySimplePreOrder
from test_dir.api.ec.ec_so.preorder.update_preorderline import UpdatePreOrderLine
from test_dir.api_case.ec.common.comm_check_products_with_cart import CommCheckProductsWithCart
from test_dir.api_case.ec.common.remove_all_products_in_cart import RemoveAllProductsInCart
from test_dir.api_case.ec.common.set_user_porder import SetUserPorder

# 销售区域数据，包含区域ID、邮编和区域名称
sales_data = [
    (1, "32751", "Orlando"),
    (2, "33714", "Tampa"),
    (3, "33468", "Miami"),
    (4, "30014", "Atlanta"),
    (5, "94110", "BA-R1a"),
    (6, "95196", "BA-R1b"),
    (7, "95813", "BA-SAC"),
    (8, "95131", "BA-R2"),
    (9, "85066", "LA-PHX"),
    (10, "92103", "LA-SD"),
    (11, "91307", "LA-SB"),
    (12, "91403", "LA-LAX"),
    (13, "89146", "LA-LAS"),
    (14, "11420", "NewYork"),
    (15, "23292", "WashingtonDC"),
    (16, "02919", "Boston"),
    (17, "06824", "CT-Connecticut"),
    (18, "98034", "Seattle"),
    (19, "97202", "Portland"),
    (20, "77501", "Houston"),
    (21, "78716", "Austin"),
    (22, "75181", "Dallas"),
    (23, "78220", "TX-SanAntonio"),
    (24, "60143", "Chicago"),
    (25, "53717", "WI-Milwaukee"),
    (26, "47405", "IN-Indianapolis"),
    (27, "48823", "MI-Detroit"),
    (28, "63146", "MO-StLouis"),
    (38, "10016", "Manhattan"),
    (41, "92844", "LA-OC"),
    (42, "92331", "LA-East"),
    (56, "43082", "OH-Ohio"),
    (404, "32080", "JAX1"),
    (849, "95234", "BA-R1c"),
    (955, "15221", "PIT1"),
    (1047, "07946", "New-Jersey"),
    (1395, "73008", "OK-OKC"),
    (1446, "18938", "PA-Philadelphia"),
    (29, "57266", "MO-WEST"),
    (30, "04291", "MO-EAST"),
    (31, "17967", "MOF-NJ"),
    (32, "60401", "MOF-Midwest"),
    (39, "27242", "MOF-NE-2-days"),
    (1291, "94555", "B2B-SFBayArea"),
    (1513, "91008", "B2B-LA"),
    (1514, "92187", "B2B-SD")
]

# 特殊区域的商品ID映射
special_region_products = {
    14: 70952,    # NewYork
    15: 43576,    # WashingtonDC
    16: 44895,    # Boston
    17: 23971,    # CT-Connecticut
    38: 41142,    # Manhattan
    955: 90553,   # PIT1
    1047: 10895,  # New-Jersey
    1446: 100333  # PA-Philadelphia
}


class TestAllSalesRegionsCheckOut(weeeTest.TestCase):
    @pytest.fixture(scope='class', autouse=True)
    def setup_class(self, region_monitor_header):
        # 清空购物车
        # 1.2 清空购物车
        RemoveAllProductsInCart().remove_all_products_in_grocery_cart(headers=region_monitor_header)

    @pytest.mark.parametrize('region_id, zipcode, title', sales_data)
    @pytest.mark.region_monitor
    def test_all_sales_regions_data_check(self, region_id, zipcode, title, region_monitor_header):
        """
        不同销售组织下不同region下单数据验证
        """
        # 切换地址
        SetUserPorder.set_user_header_porder(headers=region_monitor_header, zipcode=zipcode, language="en")
        porder = QuerySimplePreOrder().query_simple_preorder_v1(headers=region_monitor_header)["object"]
        deal_date = porder.get("delivery_pickup_date")
        deal_id = porder.get("deal_id")
        sales_org_id = porder.get("sales_org_id")
        zipcode = porder.get("zipcode")

        # 如果您需要对这些变量进行断言，确保它们不是 None 或者提供默认值
        assert deal_date is not None, "delivery_pickup_date is missing in the response"
        assert deal_id is not None, "deal_id is missing in the response"
        assert sales_org_id is not None, "sales_org_id is missing in the response"
        assert zipcode is not None, "zipcode is missing in the response"

        # 获取sale特殊分类本地可售商品
        self._check_and_add_sale_products(region_monitor_header, zipcode, deal_date, sales_org_id)

        # 特殊地区履约商品检查
        if region_id in special_region_products:
            self._check_special_region_product(
                region_monitor_header, 
                region_id, 
                zipcode, 
                sales_org_id, 
                deal_date
            )

        # # 加购Upsell商品
        # CommonBuy.buy_upsell_product(ec_login_header)
        #
        # # 使用参数化支付方式，产生一个待支付的订单
        # order_info = CommonPayment.pay_with_all_method(headers=region_monitor_header,
        #                                                payment_category="P", is_point=False)
        # order_ids = order_info["order_ids"]
        # assert order_ids, f"使用paypal支付，未产生订单号，接口返回结果为：{order_ids}"
        # # 取消待支付订单
        # cancel_order = CancelOrder().cancel_unpaid_order_new(headers=region_monitor_header, order_ids=order_ids)
        # assert cancel_order['object'] == 'success'

    def _check_and_add_sale_products(self, headers, zipcode, deal_date, sales_org_id):
        """检查并添加特价商品到购物车"""
        # 获取sale特殊分类本地可售商品
        normal = self._get_sale_products_with_retry(headers, zipcode, deal_date)
        
        # 筛选可售商品
        available_grocery_products = [
            [item['data']['id'], item['data']['sales_min_order_quantity']] 
            for item in normal["object"]["contents"] 
            if item['type'] != 'carousel' and item["data"]["sold_status"] == "available"
        ]

        assert available_grocery_products, f"当前{zipcode}sale分类下没有本地售卖商品，请检查，为{normal['object']}"
        
        # 加购生鲜商品
        for index, item in enumerate(available_grocery_products):
            self._add_product_to_cart(headers, item[0], item[1])
            if index == 1:  # 只添加两个商品
                break

    def _get_sale_products_with_retry(self, headers, zipcode, deal_date, max_retries=1):
        """获取特价商品，失败时重试"""
        try:
            normal = SearchByCatalogueContent().search_by_catalogue_content(
                headers=headers,
                zipcode=zipcode, 
                date=deal_date,
                filter_sub_category="sale",
                filters=json.dumps({"delivery_type": "delivery_type_local"})
            )
            assert normal["object"]["contents"], f"当前sale分类下没有本地售卖商品，请检查，为{normal['object']}"
            return normal
        except Exception as e:
            log.info(f"获取sale分类商品失败，将重试: {str(e)}")
            if max_retries > 0:
                time.sleep(60)
                normal = SearchByCatalogueContent().search_by_catalogue_content(
                    headers=headers,
                    zipcode=zipcode, 
                    date=deal_date,
                    filter_sub_category="sale",
                    filters=json.dumps({"delivery_type": "delivery_type_local"})
                )
                assert normal["object"]["contents"], f"当前sale分类下没有本地售卖商品，为{normal['object']}，exception={str(e)}"
                return normal
            raise

    def _add_product_to_cart(self, headers, product_id, quantity):
        """添加商品到购物车并验证"""
        porder_items = UpdatePreOrderLine().porder_items_v3(
            headers=headers,
            product_id=product_id,
            quantity=quantity
        )
        assert porder_items["result"] is True and len(
            porder_items["object"]["updateItems"]) > 0, f"product id is {product_id}, response is {porder_items}"
        
        # 验证商品已添加到购物车
        CommCheckProductsWithCart().check_product_exists_in_cart(
            headers=headers,
            cart_domain="grocery",
            product_id=product_id
        )

    def _check_special_region_product(self, headers, region_id, zipcode, sales_org_id, deal_date):
        """检查特殊地区的特定商品"""
        product_id = special_region_products[region_id]
        
        # 获取商品详情和库存信息
        pdp_detail = PdpDetail().pdp_detail(
            headers=headers,
            product_id=product_id,
            zipcode=zipcode, 
            sales_org_id=sales_org_id
        )

        inventory_v5 = ApiInventory().ec_inventory_query_v5(
            headers=headers, 
            product_id=product_id,
            zipcode=zipcode, 
            sales_org_id=sales_org_id,
            date=deal_date
        )
        
        # 验证商品和库存信息
        assert pdp_detail["object"]["product"] is not None, f'该商品{product_id}未返回商品信息，请确认'
        assert inventory_v5["object"]["inventory"] is not None, f'该商品{product_id}未返回库存信息，请确认'
        
        # 如果有库存，验证商品状态并添加到购物车
        if inventory_v5["object"]["inventory"]["qty"] > 0:
            assert pdp_detail["object"]["product"]["sold_status"] == "available", f'该商品{product_id}未返回商品信息，请确认'
            assert inventory_v5["object"]["is_sold_out"] is False, f'该商品{product_id}未返回库存信息，请确认'
            
            # 加购履约商品
            self._add_product_to_cart(
                headers=headers,
                product_id=product_id, 
                quantity=pdp_detail["object"]["product"]["min_order_quantity"]
            )
        else:
            # 验证无库存商品状态
            assert pdp_detail["object"]["product"]["sold_status"] in ["sold_out", "change_other_day"], \
                f'该商品{product_id}未返回商品信息，请确认'
