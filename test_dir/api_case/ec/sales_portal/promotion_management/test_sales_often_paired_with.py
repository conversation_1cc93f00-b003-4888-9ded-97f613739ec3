# !/usr/bin/python3
# -*- coding: utf-8 -*-


import weeeTest

from test_dir.api.ec.central_portal.central_promotion import CentralPromotion


class TestSalesOftenPairedWith(weeeTest.TestCase):

    @weeeTest.mark.list('sales', 'Transaction',  'sales_portal', 'sales_portal_tb1')
    def test_sales_often_paired_with(self, sales_header):
        """ # 促销管理-推荐搭配-查询推荐搭配页面数据 """
        recommend_product_list = CentralPromotion().recommend_product_list(headers=sales_header)

        assert len(recommend_product_list["object"]["data"]) > 0, f'查询killer deal页面数据常{recommend_product_list}'
        # 增加filter
        recommend_product_list = CentralPromotion().recommend_product_list(headers=sales_header,
                                                                           catalogue_num="050104", status="Y")
        assert recommend_product_list["result"] is True, f'查询killer deal页面数据常{recommend_product_list}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_often_paired_with_by_status_enabled(self, sales_header):
        """ # 促销管理-推荐搭配-按状态Y查询推荐搭配商品 """
        recommend_result = CentralPromotion().recommend_product_list(
            headers=sales_header,
            status="Y"
        )
        assert recommend_result["result"] is True, f'按状态Y查询推荐搭配失败: {recommend_result}'

        # 验证返回的数据状态是否正确
        if len(recommend_result["object"]["data"]) > 0:
            for item in recommend_result["object"]["data"]:
                if "status" in item:
                    assert item["status"] == "Y", f'返回的推荐搭配状态不匹配，期望Y，实际{item["status"]}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_often_paired_with_by_status_disabled(self, sales_header):
        """ # 促销管理-推荐搭配-按状态N查询推荐搭配商品 """
        recommend_result = CentralPromotion().recommend_product_list(
            headers=sales_header,
            status="N"
        )
        assert recommend_result["result"] is True, f'按状态N查询推荐搭配失败: {recommend_result}'

        # 验证返回的数据状态是否正确
        if len(recommend_result["object"]["data"]) > 0:
            for item in recommend_result["object"]["data"]:
                if "status" in item:
                    assert item["status"] == "N", f'返回的推荐搭配状态不匹配，期望N，实际{item["status"]}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_often_paired_with_by_catalogue_num(self, sales_header):
        """ # 促销管理-推荐搭配-按目录编号查询推荐搭配商品 """
        # 使用常见的目录编号进行测试
        catalogue_nums = ["050104", "050101", "050102"]

        for catalogue_num in catalogue_nums:
            recommend_result = CentralPromotion().recommend_product_list(
                headers=sales_header,
                catalogue_num=catalogue_num
            )
            assert recommend_result["result"] is True, f'按目录编号{catalogue_num}查询推荐搭配失败: {recommend_result}'

            # 验证返回的数据目录编号是否正确
            if len(recommend_result["object"]["data"]) > 0:
                for item in recommend_result["object"]["data"]:
                    if "catalogue_num" in item:
                        assert item["catalogue_num"] == catalogue_num, \
                            f'返回的目录编号不匹配，期望{catalogue_num}，实际{item["catalogue_num"]}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_often_paired_with_by_product_id(self, sales_header):
        """ # 促销管理-推荐搭配-按商品ID查询推荐搭配商品 """
        # 先获取一个商品ID
        basic_result = CentralPromotion().recommend_product_list(headers=sales_header)
        assert basic_result["result"] is True, "获取推荐搭配列表失败"

        if len(basic_result["object"]["data"]) > 0:
            product_id = basic_result["object"]["data"][0]["product_id"]

            # 使用获取到的商品ID进行查询
            product_result = CentralPromotion().recommend_product_list(
                headers=sales_header,
                product_id=str(product_id)
            )
            assert product_result["result"] is True, f'按商品ID查询推荐搭配失败: {product_result}'

            # 验证返回的数据商品ID是否正确
            if len(product_result["object"]["data"]) > 0:
                found = False
                for item in product_result["object"]["data"]:
                    if item["product_id"] == product_id:
                        found = True
                        break
                assert found, f'返回的推荐搭配中未找到指定的商品ID{product_id}'


    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_often_paired_with_combined_filters(self, sales_header):
        """ # 促销管理-推荐搭配-组合条件查询推荐搭配商品 """
        # 组合多个查询条件
        combined_result = CentralPromotion().recommend_product_list(
            headers=sales_header,
            status="Y",
            catalogue_num="050104",
            offset=0,
            limit=10
        )
        assert combined_result["result"] is True, f'组合条件查询推荐搭配失败: {combined_result}'

        # 验证返回的数据符合查询条件
        if len(combined_result["object"]["data"]) > 0:
            for item in combined_result["object"]["data"]:
                if "status" in item:
                    assert item["status"] == "Y", f'返回的状态不匹配，期望Y，实际{item["status"]}'
                if "catalogue_num" in item:
                    assert item["catalogue_num"] == "050104", \
                        f'返回的目录编号不匹配，期望050104，实际{item["catalogue_num"]}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_often_paired_with_required_fields(self, sales_header):
        """ # 促销管理-推荐搭配-验证返回数据必要字段 """
        recommend_result = CentralPromotion().recommend_product_list(headers=sales_header)
        assert recommend_result["result"] is True, f'查询推荐搭配接口返回失败: {recommend_result}'

        # 验证返回结构
        assert "object" in recommend_result, "返回结果缺少object字段"
        assert "data" in recommend_result["object"], "返回结果缺少data字段"

        # 验证推荐搭配的必要字段
        if len(recommend_result["object"]["data"]) > 0:
            required_fields = ["id", "product_id", "recommend_product_id", "status"]
            for item in recommend_result["object"]["data"][:3]:  # 只检查前3条数据
                for field in required_fields:
                    assert field in item, f'推荐搭配数据缺少必要字段{field}: {item}'




    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_often_paired_with_nonexistent_catalogue_num(self, sales_header):
        """ # 促销管理-推荐搭配-不存在的目录编号查询推荐搭配商品 """
        # 使用不存在的目录编号
        nonexistent_result = CentralPromotion().recommend_product_list(
            headers=sales_header,
            catalogue_num="999999"
        )
        assert nonexistent_result["result"] is True, f'不存在目录编号查询推荐搭配失败: {nonexistent_result}'

        # 不存在的目录编号应该返回空列表
        assert len(nonexistent_result["object"]["data"]) == 0, \
            "不存在的目录编号应该返回空的推荐搭配列表"

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_often_paired_with_data_consistency(self, sales_header):
        """ # 促销管理-推荐搭配-数据一致性验证 """
        # 获取启用状态的推荐搭配
        enabled_result = CentralPromotion().recommend_product_list(
            headers=sales_header,
            status="Y"
        )
        assert enabled_result["result"] is True, "获取启用状态推荐搭配失败"

        # 获取禁用状态的推荐搭配
        disabled_result = CentralPromotion().recommend_product_list(
            headers=sales_header,
            status="N"
        )
        assert disabled_result["result"] is True, "获取禁用状态推荐搭配失败"

        # 验证启用和禁用的推荐搭配不重复
        if len(enabled_result["object"]["data"]) > 0 and len(disabled_result["object"]["data"]) > 0:
            enabled_ids = {item["id"] for item in enabled_result["object"]["data"]}
            disabled_ids = {item["id"] for item in disabled_result["object"]["data"]}

            # 启用和禁用的推荐搭配ID不应该重复
            overlap = enabled_ids.intersection(disabled_ids)
            assert len(overlap) == 0, f'启用和禁用状态的推荐搭配存在重复ID: {overlap}'
