# !/usr/bin/python3
# -*- coding: utf-8 -*-


import weeeTest

from test_dir.api.ec.central_portal.central_promotion import CentralPromotion


class TestSalesOftenPairedWith(weeeTest.TestCase):

    @weeeTest.mark.list('sales', 'Transaction', 'test_sales_banner_query_banner')
    def test_sales_often_paired_with(self, sales_header):
        """ # 促销管理-推荐搭配-查询推荐搭配页面数据 """
        recommend_product_list = CentralPromotion().recommend_product_list(headers=sales_header)

        assert len(recommend_product_list["object"]["data"]) > 0, f'查询killer deal页面数据常{recommend_product_list}'
        # 增加filter
        recommend_product_list = CentralPromotion().recommend_product_list(headers=sales_header,
                                                                           catalogue_num="050104", status="Y")
        assert recommend_product_list["result"] is True, f'查询killer deal页面数据常{recommend_product_list}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_often_paired_with_by_status_enabled(self, sales_header):
        """ # 促销管理-推荐搭配-按状态Y查询推荐搭配商品 """
        recommend_result = CentralPromotion().recommend_product_list(
            headers=sales_header,
            status="Y"
        )
        assert recommend_result["result"] is True, f'按状态Y查询推荐搭配失败: {recommend_result}'

        # 验证返回的数据状态是否正确
        if len(recommend_result["object"]["data"]) > 0:
            for item in recommend_result["object"]["data"]:
                if "status" in item:
                    assert item["status"] == "Y", f'返回的推荐搭配状态不匹配，期望Y，实际{item["status"]}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_often_paired_with_by_status_disabled(self, sales_header):
        """ # 促销管理-推荐搭配-按状态N查询推荐搭配商品 """
        recommend_result = CentralPromotion().recommend_product_list(
            headers=sales_header,
            status="N"
        )
        assert recommend_result["result"] is True, f'按状态N查询推荐搭配失败: {recommend_result}'

        # 验证返回的数据状态是否正确
        if len(recommend_result["object"]["data"]) > 0:
            for item in recommend_result["object"]["data"]:
                if "status" in item:
                    assert item["status"] == "N", f'返回的推荐搭配状态不匹配，期望N，实际{item["status"]}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_often_paired_with_by_catalogue_num(self, sales_header):
        """ # 促销管理-推荐搭配-按目录编号查询推荐搭配商品 """
        # 使用常见的目录编号进行测试
        catalogue_nums = ["050104", "050101", "050102"]

        for catalogue_num in catalogue_nums:
            recommend_result = CentralPromotion().recommend_product_list(
                headers=sales_header,
                catalogue_num=catalogue_num
            )
            assert recommend_result["result"] is True, f'按目录编号{catalogue_num}查询推荐搭配失败: {recommend_result}'

            # 验证返回的数据目录编号是否正确
            if len(recommend_result["object"]["data"]) > 0:
                for item in recommend_result["object"]["data"]:
                    if "catalogue_num" in item:
                        assert item["catalogue_num"] == catalogue_num, \
                            f'返回的目录编号不匹配，期望{catalogue_num}，实际{item["catalogue_num"]}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_often_paired_with_by_product_id(self, sales_header):
        """ # 促销管理-推荐搭配-按商品ID查询推荐搭配商品 """
        # 先获取一个商品ID
        basic_result = CentralPromotion().recommend_product_list(headers=sales_header)
        assert basic_result["result"] is True, "获取推荐搭配列表失败"

        if len(basic_result["object"]["data"]) > 0:
            product_id = basic_result["object"]["data"][0]["product_id"]

            # 使用获取到的商品ID进行查询
            product_result = CentralPromotion().recommend_product_list(
                headers=sales_header,
                product_id=str(product_id)
            )
            assert product_result["result"] is True, f'按商品ID查询推荐搭配失败: {product_result}'

            # 验证返回的数据商品ID是否正确
            if len(product_result["object"]["data"]) > 0:
                found = False
                for item in product_result["object"]["data"]:
                    if item["product_id"] == product_id:
                        found = True
                        break
                assert found, f'返回的推荐搭配中未找到指定的商品ID{product_id}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_often_paired_with_pagination(self, sales_header):
        """ # 促销管理-推荐搭配-分页查询推荐搭配商品 """
        # 测试第一页，每页5条
        page1_result = CentralPromotion().recommend_product_list(
            headers=sales_header,
            offset=0,
            limit=5
        )
        assert page1_result["result"] is True, f'分页查询第一页失败: {page1_result}'

        # 验证分页信息
        assert len(page1_result["object"]["data"]) <= 5, \
            f'返回的数据量超过限制，实际{len(page1_result["object"]["data"])}'

        # 如果有足够数据，测试第二页
        if len(page1_result["object"]["data"]) == 5:
            page2_result = CentralPromotion().recommend_product_list(
                headers=sales_header,
                offset=5,
                limit=5
            )
            assert page2_result["result"] is True, f'分页查询第二页失败: {page2_result}'
            assert len(page2_result["object"]["data"]) <= 5, \
                f'第二页返回的数据量超过限制，实际{len(page2_result["object"]["data"])}'
