# !/usr/bin/python3
# -*- coding: utf-8 -*-


import weeeTest
from test_dir.api.ec.central_portal.central_growth import CentralGrowth
from test_dir.api.ec.central_portal.central_promotion import CentralPromotion


class TestSalesCouponMgmt(weeeTest.TestCase):
    @weeeTest.mark.list('sales', 'Transaction',  'sales_portal', 'sales_portal_tb1')
    def test_sales_coupon_plan_list(self, sales_header):
        """ # 活动管理-优惠券管理-优惠券计划-查询优惠券计划页面数据 """
        # 满减、免邮、赠品、百分比
        type = ["D", "F", "C", "Z"]
        for item in type:
            coupon_plan_list = CentralPromotion().coupon_plan_list(headers=sales_header, type=item)
            assert len(coupon_plan_list["object"]["result"]) > 0, f'查询优惠券计划页面数据{coupon_plan_list}'


    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_coupon_plan_list_by_group_id(self, sales_header):
        """ # 活动管理-优惠券管理-优惠券计划-按组ID查询优惠券计划 """
        # 先获取一个优惠券组ID
        basic_result = CentralPromotion().coupon_plan_list(headers=sales_header)
        assert basic_result["result"] is True, "获取优惠券计划列表失败"

        if len(basic_result["object"]["result"]) > 0:
            first_plan = basic_result["object"]["result"][0]
            if "group_id" in first_plan and first_plan["group_id"]:
                group_id = str(first_plan["group_id"])

                # 使用获取到的组ID进行查询
                group_result = CentralPromotion().coupon_plan_list(
                    headers=sales_header,
                    group_id=group_id
                )
                assert group_result["result"] is True, f'按组ID查询优惠券计划失败: {group_result}'

                # 验证返回的数据组ID是否正确
                if len(group_result["object"]["result"]) > 0:
                    for plan in group_result["object"]["result"]:
                        if "group_id" in plan:
                            assert str(plan["group_id"]) == group_id, \
                                f'返回的优惠券计划组ID不匹配，期望{group_id}，实际{plan["group_id"]}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_coupon_plan_list_by_status(self, sales_header):
        """ # 活动管理-优惠券管理-优惠券计划-按状态查询优惠券计划 """
        # 测试所有状态：A:可用 G:已提交 C:待提交 R:已拒绝
        status_list = ["A", "G", "C", "R"]

        for status in status_list:
            status_result = CentralPromotion().coupon_plan_list(
                headers=sales_header,
                status=status
            )
            assert status_result["result"] is True, f'按状态{status}查询优惠券计划失败: {status_result}'

            # 验证返回的数据状态是否正确
            if len(status_result["object"]["result"]) > 0:
                for plan in status_result["object"]["result"]:
                    if "status" in plan:
                        assert plan["status"] == status, \
                            f'返回的优惠券计划状态不匹配，期望{status}，实际{plan["status"]}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_coupon_plan_list_by_remark(self, sales_header):
        """ # 活动管理-优惠券管理-优惠券计划-按备注名称查询优惠券计划 """
        # 先获取一个优惠券计划的备注名称
        basic_result = CentralPromotion().coupon_plan_list(headers=sales_header)
        assert basic_result["result"] is True, "获取优惠券计划列表失败"

        if len(basic_result["object"]["result"]) > 0:
            first_plan = basic_result["object"]["result"][0]
            if "remark" in first_plan and first_plan["remark"]:
                remark = first_plan["remark"]

                # 使用获取到的备注进行查询
                remark_result = CentralPromotion().coupon_plan_list(
                    headers=sales_header,
                    remark=remark
                )
                assert remark_result["result"] is True, f'按备注查询优惠券计划失败: {remark_result}'

                # 验证返回的数据备注是否正确
                if len(remark_result["object"]["result"]) > 0:
                    found = False
                    for plan in remark_result["object"]["result"]:
                        if "remark" in plan and plan["remark"] == remark:
                            found = True
                            break
                    assert found, f'返回的优惠券计划备注不匹配，期望{remark}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_coupon_plan_list_by_time_range(self, sales_header):
        """ # 活动管理-优惠券管理-优惠券计划-按时间范围查询优惠券计划 """
        import datetime

        # 设置时间范围：过去7天到未来7天
        today = datetime.datetime.now()
        start_time = (today - datetime.timedelta(days=7)).strftime('%Y-%m-%d')
        end_time = (today + datetime.timedelta(days=7)).strftime('%Y-%m-%d')

        time_result = CentralPromotion().coupon_plan_list(
            headers=sales_header,
            start_time=start_time,
            end_time=end_time
        )
        assert time_result["result"] is True, f'按时间范围查询优惠券计划失败: {time_result}'



    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_coupon_plan_list_combined_filters(self, sales_header):
        """ # 活动管理-优惠券管理-优惠券计划-组合条件查询优惠券计划 """
        # 组合多个查询条件
        combined_result = CentralPromotion().coupon_plan_list(
            headers=sales_header,
            type="D",  # 满减券
            status="A",  # 可用状态
            page_no=1,
            page_size=10
        )
        assert combined_result["result"] is True, f'组合条件查询优惠券计划失败: {combined_result}'

        # 验证返回的数据符合查询条件
        if len(combined_result["object"]["result"]) > 0:
            for plan in combined_result["object"]["result"]:
                if "type" in plan:
                    assert plan["type"] == "D", f'返回的优惠券类型不匹配，期望D，实际{plan["type"]}'
                if "status" in plan:
                    assert plan["status"] == "A", f'返回的优惠券状态不匹配，期望A，实际{plan["status"]}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_coupon_plan_list_required_fields(self, sales_header):
        """ # 活动管理-优惠券管理-优惠券计划-验证返回数据必要字段 """
        plan_result = CentralPromotion().coupon_plan_list(headers=sales_header)
        assert plan_result["result"] is True, f'查询优惠券计划接口返回失败: {plan_result}'

        # 验证返回结构
        assert "object" in plan_result, "返回结果缺少object字段"
        assert "result" in plan_result["object"], "返回结果缺少result字段"
        assert "page_no" in plan_result["object"], "返回结果缺少page_no字段"
        assert "page_size" in plan_result["object"], "返回结果缺少page_size字段"
        assert "total" in plan_result["object"], "返回结果缺少total字段"

        # 验证优惠券计划的必要字段
        if len(plan_result["object"]["result"]) > 0:
            required_fields = ["id", "type", "status", "remark", "rec_create_time"]
            for plan in plan_result["object"]["result"][:3]:  # 只检查前3条数据
                for field in required_fields:
                    assert field in plan, f'优惠券计划缺少必要字段{field}: {plan}'





    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal_tb1')
    def test_sales_coupon_plan_update(self, sales_header):
        """ # 活动管理-优惠券管理-优惠券计划-更新优惠券计划页面数据 """
        coupon_plan_list = CentralPromotion().coupon_plan_list(headers=sales_header)
        # 如果是待提交状态，则提交
        for item in coupon_plan_list["object"]["result"]:
            status = item["status"]
            update_status = None
            if status == "C":
                update_status = "G"

            coupon_plan_update = CentralPromotion().coupon_plan_update(headers=sales_header,
                                                                       id=item["id"],
                                                                       status=update_status)

            assert coupon_plan_update["object"] == "success", f'更新优惠券计划{coupon_plan_update}'

    @weeeTest.mark.list('sales', 'Transaction', 'test_sales_banner_query_type_info')
    def test_sales_coupon_internal_list(self, sales_header):
        """ # 活动管理-优惠券管理-优惠券-查询用户优惠券 """
        status = ["A", "U", "E"]
        for item in status:
            coupon_internal_list = CentralPromotion().coupon_internal_list(headers=sales_header, status=item)
            assert len(coupon_internal_list["object"]["result"]) > 0, f'查询用户优惠券数据为空{coupon_internal_list}'

    @weeeTest.mark.list('sales', 'Transaction', 'test_sales_banner_query_type_info')
    def test_sales_coupon_issuance_list(self, sales_header):
        """ # 活动管理-优惠券管理-优惠券发放-查询用户优惠券 """

        coupon_issuance_list = CentralPromotion().coupon_issuance_list(headers=sales_header)
        assert len(coupon_issuance_list["object"]["result"]) > 0, f'查询用户优惠券数据为空{coupon_issuance_list}'