# !/usr/bin/python3
# -*- coding: utf-8 -*-


import weeeTest
from test_dir.api.ec.central_portal.central_growth import CentralGrowth
from test_dir.api.ec.central_portal.central_promotion import CentralPromotion


class TestSalesCouponMgmt(weeeTest.TestCase):
    @weeeTest.mark.list('sales', 'Transaction',  'sales_portal', 'sales_portal_tb1')
    def test_sales_coupon_plan_list(self, sales_header):
        """ # 活动管理-优惠券管理-优惠券计划-查询优惠券计划页面数据 """
        # 满减、免邮、赠品、百分比
        type = ["D", "F", "C", "Z"]
        for item in type:
            coupon_plan_list = CentralPromotion().coupon_plan_list(headers=sales_header, type=item)
            assert len(coupon_plan_list["object"]["result"]) > 0, f'查询优惠券计划页面数据{coupon_plan_list}'


    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_coupon_plan_list_by_group_id(self, sales_header):
        """ # 活动管理-优惠券管理-优惠券计划-按组ID查询优惠券计划 """
        # 先获取一个优惠券组ID
        basic_result = CentralPromotion().coupon_plan_list(headers=sales_header)
        assert basic_result["result"] is True, "获取优惠券计划列表失败"

        if len(basic_result["object"]["result"]) > 0:
            first_plan = basic_result["object"]["result"][0]
            if "group_id" in first_plan and first_plan["group_id"]:
                group_id = str(first_plan["group_id"])

                # 使用获取到的组ID进行查询
                group_result = CentralPromotion().coupon_plan_list(
                    headers=sales_header,
                    group_id=group_id
                )
                assert group_result["result"] is True, f'按组ID查询优惠券计划失败: {group_result}'

                # 验证返回的数据组ID是否正确
                if len(group_result["object"]["result"]) > 0:
                    for plan in group_result["object"]["result"]:
                        if "group_id" in plan:
                            assert str(plan["group_id"]) == group_id, \
                                f'返回的优惠券计划组ID不匹配，期望{group_id}，实际{plan["group_id"]}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_coupon_plan_list_by_status(self, sales_header):
        """ # 活动管理-优惠券管理-优惠券计划-按状态查询优惠券计划 """
        # 测试所有状态：A:可用 G:已提交 C:待提交 R:已拒绝
        status_list = ["A", "G", "C", "R"]

        for status in status_list:
            status_result = CentralPromotion().coupon_plan_list(
                headers=sales_header,
                status=status
            )
            assert status_result["result"] is True, f'按状态{status}查询优惠券计划失败: {status_result}'

            # 验证返回的数据状态是否正确
            if len(status_result["object"]["result"]) > 0:
                for plan in status_result["object"]["result"]:
                    if "status" in plan:
                        assert plan["status"] == status, \
                            f'返回的优惠券计划状态不匹配，期望{status}，实际{plan["status"]}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_coupon_plan_list_by_remark(self, sales_header):
        """ # 活动管理-优惠券管理-优惠券计划-按备注名称查询优惠券计划 """
        # 先获取一个优惠券计划的备注名称
        basic_result = CentralPromotion().coupon_plan_list(headers=sales_header)
        assert basic_result["result"] is True, "获取优惠券计划列表失败"

        if len(basic_result["object"]["result"]) > 0:
            first_plan = basic_result["object"]["result"][0]
            if "remark" in first_plan and first_plan["remark"]:
                remark = first_plan["remark"]

                # 使用获取到的备注进行查询
                remark_result = CentralPromotion().coupon_plan_list(
                    headers=sales_header,
                    remark=remark
                )
                assert remark_result["result"] is True, f'按备注查询优惠券计划失败: {remark_result}'

                # 验证返回的数据备注是否正确
                if len(remark_result["object"]["result"]) > 0:
                    found = False
                    for plan in remark_result["object"]["result"]:
                        if "remark" in plan and plan["remark"] == remark:
                            found = True
                            break
                    assert found, f'返回的优惠券计划备注不匹配，期望{remark}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_coupon_plan_list_by_time_range(self, sales_header):
        """ # 活动管理-优惠券管理-优惠券计划-按时间范围查询优惠券计划 """
        import datetime

        # 设置时间范围：过去7天到未来7天
        today = datetime.datetime.now()
        start_time = (today - datetime.timedelta(days=7)).strftime('%Y-%m-%d')
        end_time = (today + datetime.timedelta(days=7)).strftime('%Y-%m-%d')

        time_result = CentralPromotion().coupon_plan_list(
            headers=sales_header,
            start_time=start_time,
            end_time=end_time
        )
        assert time_result["result"] is True, f'按时间范围查询优惠券计划失败: {time_result}'



    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_coupon_plan_list_combined_filters(self, sales_header):
        """ # 活动管理-优惠券管理-优惠券计划-组合条件查询优惠券计划 """
        # 组合多个查询条件
        combined_result = CentralPromotion().coupon_plan_list(
            headers=sales_header,
            type="D",  # 满减券
            status="A",  # 可用状态
            page_no=1,
            page_size=10
        )
        assert combined_result["result"] is True, f'组合条件查询优惠券计划失败: {combined_result}'

        # 验证返回的数据符合查询条件
        if len(combined_result["object"]["result"]) > 0:
            for plan in combined_result["object"]["result"]:
                if "type" in plan:
                    assert plan["type"] == "D", f'返回的优惠券类型不匹配，期望D，实际{plan["type"]}'
                if "status" in plan:
                    assert plan["status"] == "A", f'返回的优惠券状态不匹配，期望A，实际{plan["status"]}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_coupon_plan_list_required_fields(self, sales_header):
        """ # 活动管理-优惠券管理-优惠券计划-验证返回数据必要字段 """
        plan_result = CentralPromotion().coupon_plan_list(headers=sales_header)
        assert plan_result["result"] is True, f'查询优惠券计划接口返回失败: {plan_result}'

        # 验证返回结构
        assert "object" in plan_result, "返回结果缺少object字段"
        assert "result" in plan_result["object"], "返回结果缺少result字段"
        assert "page_no" in plan_result["object"], "返回结果缺少page_no字段"
        assert "page_size" in plan_result["object"], "返回结果缺少page_size字段"
        assert "total" in plan_result["object"], "返回结果缺少total字段"

        # 验证优惠券计划的必要字段
        if len(plan_result["object"]["result"]) > 0:
            required_fields = ["id", "type", "status", "remark", "rec_create_time"]
            for plan in plan_result["object"]["result"][:3]:  # 只检查前3条数据
                for field in required_fields:
                    assert field in plan, f'优惠券计划缺少必要字段{field}: {plan}'





    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal_tb1')
    def test_sales_coupon_plan_update(self, sales_header):
        """ # 活动管理-优惠券管理-优惠券计划-更新优惠券计划页面数据 """
        coupon_plan_list = CentralPromotion().coupon_plan_list(headers=sales_header)
        # 如果是待提交状态，则提交
        for item in coupon_plan_list["object"]["result"]:
            status = item["status"]
            update_status = None
            if status == "C":
                update_status = "G"

            coupon_plan_update = CentralPromotion().coupon_plan_update(headers=sales_header,
                                                                       id=item["id"],
                                                                       status=update_status)

            assert coupon_plan_update["object"] == "success", f'更新优惠券计划{coupon_plan_update}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_coupon_plan_update_status_to_submitted(self, sales_header):
        """ # 活动管理-优惠券管理-优惠券计划-更新状态为已提交 """
        # 先获取待提交状态的优惠券计划
        plan_list = CentralPromotion().coupon_plan_list(headers=sales_header, status="C")
        assert plan_list["result"] is True, "获取待提交状态优惠券计划失败"

        if len(plan_list["object"]["result"]) > 0:
            plan_id = plan_list["object"]["result"][0]["id"]

            # 更新状态为已提交
            update_result = CentralPromotion().coupon_plan_update(
                headers=sales_header,
                id=str(plan_id),
                status="G"
            )
            assert update_result["result"] is True, f'更新优惠券计划状态为已提交失败: {update_result}'
            assert update_result["object"] == "success", f'更新优惠券计划状态返回结果异常: {update_result}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_coupon_plan_update_status_to_available(self, sales_header):
        """ # 活动管理-优惠券管理-优惠券计划-更新状态为可用 """
        # 先获取已提交状态的优惠券计划
        plan_list = CentralPromotion().coupon_plan_list(headers=sales_header, status="G")
        assert plan_list["result"] is True, "获取已提交状态优惠券计划失败"

        if len(plan_list["object"]["result"]) > 0:
            plan_id = plan_list["object"]["result"][0]["id"]

            # 更新状态为可用
            update_result = CentralPromotion().coupon_plan_update(
                headers=sales_header,
                id=str(plan_id),
                status="A"
            )
            assert update_result["result"] is True, f'更新优惠券计划状态为可用失败: {update_result}'
            assert update_result["object"] == "success", f'更新优惠券计划状态返回结果异常: {update_result}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_coupon_plan_update_status_to_rejected(self, sales_header):
        """ # 活动管理-优惠券管理-优惠券计划-更新状态为已拒绝 """
        # 先获取已提交状态的优惠券计划
        plan_list = CentralPromotion().coupon_plan_list(headers=sales_header, status="G")
        assert plan_list["result"] is True, "获取已提交状态优惠券计划失败"

        if len(plan_list["object"]["result"]) > 0:
            plan_id = plan_list["object"]["result"][0]["id"]

            # 更新状态为已拒绝
            update_result = CentralPromotion().coupon_plan_update(
                headers=sales_header,
                id=str(plan_id),
                status="R"
            )
            assert update_result["result"] is True, f'更新优惠券计划状态为已拒绝失败: {update_result}'
            assert update_result["object"] == "success", f'更新优惠券计划状态返回结果异常: {update_result}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_coupon_plan_update_status_to_pending(self, sales_header):
        """ # 活动管理-优惠券管理-优惠券计划-更新状态为待提交 """
        # 先获取可用状态的优惠券计划
        plan_list = CentralPromotion().coupon_plan_list(headers=sales_header, status="A")
        assert plan_list["result"] is True, "获取可用状态优惠券计划失败"

        if len(plan_list["object"]["result"]) > 0:
            plan_id = plan_list["object"]["result"][0]["id"]

            # 更新状态为待提交
            update_result = CentralPromotion().coupon_plan_update(
                headers=sales_header,
                id=str(plan_id),
                status="C"
            )
            assert update_result["result"] is True, f'更新优惠券计划状态为待提交失败: {update_result}'
            assert update_result["object"] == "success", f'更新优惠券计划状态返回结果异常: {update_result}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_coupon_plan_update_batch_status(self, sales_header):
        """ # 活动管理-优惠券管理-优惠券计划-批量更新状态 """
        # 获取待提交状态的优惠券计划列表
        plan_list = CentralPromotion().coupon_plan_list(headers=sales_header, status="C")
        assert plan_list["result"] is True, "获取待提交状态优惠券计划失败"

        if len(plan_list["object"]["result"]) > 0:
            # 批量更新前3个计划的状态
            plans_to_update = plan_list["object"]["result"][:3]

            for plan in plans_to_update:
                plan_id = plan["id"]

                # 更新状态为已提交
                update_result = CentralPromotion().coupon_plan_update(
                    headers=sales_header,
                    id=str(plan_id),
                    status="G"
                )
                assert update_result["result"] is True, f'批量更新优惠券计划ID{plan_id}失败: {update_result}'
                assert update_result["object"] == "success", f'批量更新优惠券计划ID{plan_id}返回结果异常: {update_result}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_coupon_plan_update_status_validation(self, sales_header):
        """ # 活动管理-优惠券管理-优惠券计划-状态更新验证 """
        # 先获取一个优惠券计划
        plan_list = CentralPromotion().coupon_plan_list(headers=sales_header)
        assert plan_list["result"] is True, "获取优惠券计划列表失败"

        if len(plan_list["object"]["result"]) > 0:
            plan_id = plan_list["object"]["result"][0]["id"]
            original_status = plan_list["object"]["result"][0]["status"]

            # 测试所有有效状态
            valid_statuses = ["A", "G", "C", "R"]

            for status in valid_statuses:
                update_result = CentralPromotion().coupon_plan_update(
                    headers=sales_header,
                    id=str(plan_id),
                    status=status
                )
                assert update_result["result"] is True, f'更新优惠券计划状态为{status}失败: {update_result}'
                assert update_result["object"] == "success", f'更新优惠券计划状态为{status}返回结果异常: {update_result}'

            # 恢复原始状态
            restore_result = CentralPromotion().coupon_plan_update(
                headers=sales_header,
                id=str(plan_id),
                status=original_status
            )
            assert restore_result["result"] is True, f'恢复优惠券计划原始状态失败: {restore_result}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_coupon_plan_update_invalid_id(self, sales_header):
        """ # 活动管理-优惠券管理-优惠券计划-无效ID更新状态 """
        # 使用不存在的优惠券计划ID
        invalid_result = CentralPromotion().coupon_plan_update(
            headers=sales_header,
            id="999999999",
            status="A"
        )
        # 接口应该能处理无效ID，通常返回错误
        assert invalid_result["result"] is not None, "无效ID更新应该有响应"

        # 如果接口返回失败，验证错误信息
        if invalid_result["result"] is False:
            assert "message" in invalid_result, "错误响应应该包含错误信息"

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_coupon_plan_update_invalid_status(self, sales_header):
        """ # 活动管理-优惠券管理-优惠券计划-无效状态更新 """
        # 先获取一个优惠券计划ID
        plan_list = CentralPromotion().coupon_plan_list(headers=sales_header)
        assert plan_list["result"] is True, "获取优惠券计划列表失败"

        if len(plan_list["object"]["result"]) > 0:
            plan_id = plan_list["object"]["result"][0]["id"]

            # 使用无效的状态值
            invalid_result = CentralPromotion().coupon_plan_update(
                headers=sales_header,
                id=str(plan_id),
                status="INVALID"
            )
            # 接口应该能处理无效状态，通常返回错误
            assert invalid_result["result"] is not None, "无效状态更新应该有响应"

            # 如果接口返回失败，验证错误信息
            if invalid_result["result"] is False:
                assert "message" in invalid_result, "错误响应应该包含错误信息"

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_coupon_plan_update_empty_params(self, sales_header):
        """ # 活动管理-优惠券管理-优惠券计划-空参数更新 """
        # 测试空ID参数
        empty_id_result = CentralPromotion().coupon_plan_update(
            headers=sales_header,
            id=None,
            status="A"
        )
        # 接口应该能处理空ID，通常返回错误
        assert empty_id_result["result"] is not None, "空ID更新应该有响应"

        # 测试空状态参数
        plan_list = CentralPromotion().coupon_plan_list(headers=sales_header)
        if plan_list["result"] is True and len(plan_list["object"]["result"]) > 0:
            plan_id = plan_list["object"]["result"][0]["id"]

            empty_status_result = CentralPromotion().coupon_plan_update(
                headers=sales_header,
                id=str(plan_id),
                status=None
            )
            # 接口应该能处理空状态，通常返回错误
            assert empty_status_result["result"] is not None, "空状态更新应该有响应"

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_coupon_plan_update_status_flow_validation(self, sales_header):
        """ # 活动管理-优惠券管理-优惠券计划-状态流转验证 """
        # 先获取一个待提交状态的优惠券计划
        plan_list = CentralPromotion().coupon_plan_list(headers=sales_header, status="C")
        assert plan_list["result"] is True, "获取待提交状态优惠券计划失败"

        if len(plan_list["object"]["result"]) > 0:
            plan_id = plan_list["object"]["result"][0]["id"]

            # 状态流转：待提交 -> 已提交 -> 可用
            # 步骤1: 待提交 -> 已提交
            step1_result = CentralPromotion().coupon_plan_update(
                headers=sales_header,
                id=str(plan_id),
                status="G"
            )
            assert step1_result["result"] is True, f'状态流转步骤1失败: {step1_result}'
            assert step1_result["object"] == "success", f'状态流转步骤1返回结果异常: {step1_result}'

            # 步骤2: 已提交 -> 可用
            step2_result = CentralPromotion().coupon_plan_update(
                headers=sales_header,
                id=str(plan_id),
                status="A"
            )
            assert step2_result["result"] is True, f'状态流转步骤2失败: {step2_result}'
            assert step2_result["object"] == "success", f'状态流转步骤2返回结果异常: {step2_result}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_coupon_plan_update_different_types(self, sales_header):
        """ # 活动管理-优惠券管理-优惠券计划-不同类型优惠券状态更新 """
        # 测试不同类型的优惠券计划状态更新
        coupon_types = ["D", "F", "C", "Z"]  # 满减、免邮、满减券、满折

        for coupon_type in coupon_types:
            type_plan_list = CentralPromotion().coupon_plan_list(
                headers=sales_header,
                type=coupon_type
            )
            assert type_plan_list["result"] is True, f"获取{coupon_type}类型优惠券计划失败"

            if len(type_plan_list["object"]["result"]) > 0:
                plan_id = type_plan_list["object"]["result"][0]["id"]
                original_status = type_plan_list["object"]["result"][0]["status"]

                # 更新状态为已提交
                update_result = CentralPromotion().coupon_plan_update(
                    headers=sales_header,
                    id=str(plan_id),
                    status="G"
                )
                assert update_result["result"] is True, f'{coupon_type}类型优惠券计划状态更新失败: {update_result}'
                assert update_result["object"] == "success", f'{coupon_type}类型优惠券计划状态更新返回结果异常: {update_result}'

                # 恢复原始状态
                restore_result = CentralPromotion().coupon_plan_update(
                    headers=sales_header,
                    id=str(plan_id),
                    status=original_status
                )
                assert restore_result["result"] is True, f'恢复{coupon_type}类型优惠券计划原始状态失败: {restore_result}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_coupon_plan_update_response_validation(self, sales_header):
        """ # 活动管理-优惠券管理-优惠券计划-更新响应验证 """
        # 先获取一个优惠券计划
        plan_list = CentralPromotion().coupon_plan_list(headers=sales_header)
        assert plan_list["result"] is True, "获取优惠券计划列表失败"

        if len(plan_list["object"]["result"]) > 0:
            plan_id = plan_list["object"]["result"][0]["id"]

            # 执行状态更新
            update_result = CentralPromotion().coupon_plan_update(
                headers=sales_header,
                id=str(plan_id),
                status="G"
            )

            # 验证响应结构
            assert "result" in update_result, "返回结果缺少result字段"
            assert "object" in update_result, "返回结果缺少object字段"
            assert "message_id" in update_result, "返回结果缺少message_id字段"

            # 验证成功响应的内容
            if update_result["result"] is True:
                assert update_result["object"] == "success", f'成功响应的object字段应该为success，实际为{update_result["object"]}'

    @weeeTest.mark.list('sales', 'Transaction', 'test_sales_banner_query_type_info')
    def test_sales_coupon_internal_list(self, sales_header):
        """ # 活动管理-优惠券管理-优惠券-查询用户优惠券 """
        status = ["A", "U", "E"]
        for item in status:
            coupon_internal_list = CentralPromotion().coupon_internal_list(headers=sales_header, status=item)
            assert len(coupon_internal_list["object"]["result"]) > 0, f'查询用户优惠券数据为空{coupon_internal_list}'

    @weeeTest.mark.list('sales', 'Transaction', 'test_sales_banner_query_type_info')
    def test_sales_coupon_issuance_list(self, sales_header):
        """ # 活动管理-优惠券管理-优惠券发放-查询用户优惠券 """

        coupon_issuance_list = CentralPromotion().coupon_issuance_list(headers=sales_header)
        assert len(coupon_issuance_list["object"]["result"]) > 0, f'查询用户优惠券数据为空{coupon_issuance_list}'