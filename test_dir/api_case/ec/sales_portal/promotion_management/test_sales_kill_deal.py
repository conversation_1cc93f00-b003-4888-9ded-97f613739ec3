# !/usr/bin/python3
# -*- coding: utf-8 -*-


import weeeTest
from test_dir.api.ec.central_portal.central_promotion import CentralPromotion


class TestSalesKillDeal(weeeTest.TestCase):

    @weeeTest.mark.list('sales', 'Transaction', 'test_sales_banner_query_banner')
    def test_sales_kill_deal(self, sales_header):
        """ # 促销管理-Killer deal-查询killer deal页面数据 """
        kill_deal = CentralPromotion().event_code_products_query(headers=sales_header,
                                                                 event_code="202309_Crazy_all_forevery")

        assert len(kill_deal["object"]["event_product_list"]) > 0, f'查询killer deal页面数据常{kill_deal}'

    @weeeTest.mark.list('sales', 'Transaction', 'test_sales_banner_query_banner')
    def test_sales_kill_deal_filter(self, sales_header):
        """ # 促销管理-Killer deal-查询killer deal页面filter数据 """
        event_code_option = CentralPromotion().event_code_option(headers=sales_header)
        event_code = event_code_option["object"]["event_code"]
        event_code_type_list = event_code_option["object"]["event_code_type_list"]
        sales_region_list = event_code_option["object"]["sales_region_list"]
        sku_types = event_code_option["object"]["sku_types"]

        assert len(event_code) > 0, f'查询killer deal页面filter数据{event_code_option}'
        assert len(sales_region_list) > 0, f'查询killer deal页面filter数据{sales_region_list}'
        assert len(sku_types) > 0, f'查询killer deal页面filter数据{sku_types}'
        assert event_code_type_list is not None, f'查询killer deal页面filter数据{event_code_type_list}'
        for item in sku_types:
            kill_deal = CentralPromotion().event_code_products_query(headers=sales_header,
                                                                     event_code="202309_Crazy_all_forevery",
                                                                     sku_type=item["key"]
                                                                     )
            assert kill_deal["result"] is True, f'查询killer deal页面数据常{kill_deal}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_kill_deal_by_sales_org_id(self, sales_header):
        """ # 促销管理-Killer deal-按销售组织ID查询killer deal商品 """
        # 先获取可用的销售组织
        event_code_option = CentralPromotion().event_code_option(headers=sales_header)
        assert event_code_option["result"] is True, "获取killer deal filter选项失败"

        sales_region_list = event_code_option["object"]["sales_region_list"]
        assert len(sales_region_list) > 0, "没有可用的销售组织"

        # 测试前几个销售组织
        for region in sales_region_list[:3]:  # 只测试前3个
            kill_deal = CentralPromotion().event_code_products_query(
                headers=sales_header,
                event_code="202309_Crazy_all_forevery",
                sales_org_id=region["id"]
            )
            assert kill_deal["result"] is True, f'按销售组织ID{region["id"]}查询killer deal失败: {kill_deal}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_kill_deal_by_product_id(self, sales_header):
        """ # 促销管理-Killer deal-按商品ID查询killer deal商品 """
        # 先获取一个商品ID
        basic_result = CentralPromotion().event_code_products_query(
            headers=sales_header,
            event_code="202309_Crazy_all_forevery"
        )
        assert basic_result["result"] is True, "获取killer deal商品列表失败"

        if len(basic_result["object"]["event_product_list"]) > 0:
            product_id = basic_result["object"]["event_product_list"][0]["product_id"]

            # 使用获取到的商品ID进行查询
            product_result = CentralPromotion().event_code_products_query(
                headers=sales_header,
                event_code="202309_Crazy_all_forevery",
                product_id=str(product_id)
            )
            assert product_result["result"] is True, f'按商品ID查询killer deal失败: {product_result}'

            # 验证返回的商品ID是否正确
            if len(product_result["object"]["event_product_list"]) > 0:
                found = False
                for product in product_result["object"]["event_product_list"]:
                    if product["product_id"] == product_id:
                        found = True
                        break
                assert found, f'返回的商品中未找到指定的商品ID{product_id}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_kill_deal_by_type_id(self, sales_header):
        """ # 促销管理-Killer deal-按类型ID查询killer deal商品 """
        # 先获取可用的类型ID
        event_code_option = CentralPromotion().event_code_option(headers=sales_header)
        assert event_code_option["result"] is True, "获取killer deal filter选项失败"

        event_code_type_list = event_code_option["object"]["event_code_type_list"]
        if event_code_type_list and len(event_code_type_list) > 0:
            type_id = event_code_type_list[0]["id"]

            # 使用获取到的类型ID进行查询
            type_result = CentralPromotion().event_code_products_query(
                headers=sales_header,
                event_code="202309_Crazy_all_forevery",
                type_id=str(type_id)
            )
            assert type_result["result"] is True, f'按类型ID查询killer deal失败: {type_result}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_kill_deal_pagination(self, sales_header):
        """ # 促销管理-Killer deal-分页查询killer deal商品 """
        # 测试第一页，每页5条
        page1_result = CentralPromotion().event_code_products_query(
            headers=sales_header,
            event_code="202309_Crazy_all_forevery",
            page_no=1,
            page_size=5
        )
        assert page1_result["result"] is True, f'分页查询第一页失败: {page1_result}'

        # 验证分页信息
        assert page1_result["object"]["page_no"] == 1, f'返回的页码不正确，期望1，实际{page1_result["object"]["page_no"]}'
        assert page1_result["object"]["page_size"] == 5, f'返回的页大小不正确，期望5，实际{page1_result["object"]["page_size"]}'

        # 如果有足够数据，测试第二页
        if page1_result["object"]["total"] > 5:
            page2_result = CentralPromotion().event_code_products_query(
                headers=sales_header,
                event_code="202309_Crazy_all_forevery",
                page_no=2,
                page_size=5
            )
            assert page2_result["result"] is True, f'分页查询第二页失败: {page2_result}'
            assert page2_result["object"]["page_no"] == 2, f'返回的页码不正确，期望2，实际{page2_result["object"]["page_no"]}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_kill_deal_with_timestamp(self, sales_header):
        """ # 促销管理-Killer deal-带时间戳查询killer deal商品 """
        import time
        current_timestamp = str(int(time.time() * 1000))  # 当前时间戳（毫秒）

        timestamp_result = CentralPromotion().event_code_products_query(
            headers=sales_header,
            event_code="202309_Crazy_all_forevery",
            timeStamp=current_timestamp
        )
        assert timestamp_result["result"] is True, f'带时间戳查询killer deal失败: {timestamp_result}'
