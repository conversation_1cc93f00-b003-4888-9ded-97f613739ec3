# !/usr/bin/python3
# -*- coding: utf-8 -*-


import weeeTest
from test_dir.api.ec.central_portal.central_promotion import CentralPromotion


class TestSalesKillDeal(weeeTest.TestCase):

    @weeeTest.mark.list('sales', 'Transaction', 'test_sales_banner_query_banner')
    def test_sales_kill_deal(self, sales_header):
        """ # 促销管理-Killer deal-查询killer deal页面数据 """
        kill_deal = CentralPromotion().event_code_products_query(headers=sales_header,
                                                                 event_code="202309_Crazy_all_forevery")

        assert len(kill_deal["object"]["event_product_list"]) > 0, f'查询killer deal页面数据常{kill_deal}'

    @weeeTest.mark.list('sales', 'Transaction', 'test_sales_banner_query_banner')
    def test_sales_kill_deal_filter(self, sales_header):
        """ # 促销管理-Killer deal-查询killer deal页面filter数据 """
        event_code_option = CentralPromotion().event_code_option(headers=sales_header)
        event_code = event_code_option["object"]["event_code"]
        event_code_type_list = event_code_option["object"]["event_code_type_list"]
        sales_region_list = event_code_option["object"]["sales_region_list"]
        sku_types = event_code_option["object"]["sku_types"]

        assert len(event_code) > 0, f'查询killer deal页面filter数据{event_code_option}'
        assert len(sales_region_list) > 0, f'查询killer deal页面filter数据{sales_region_list}'
        assert len(sku_types) > 0, f'查询killer deal页面filter数据{sku_types}'
        assert event_code_type_list is not None, f'查询killer deal页面filter数据{event_code_type_list}'
        for item in sku_types:
            kill_deal = CentralPromotion().event_code_products_query(headers=sales_header,
                                                                     event_code="202309_Crazy_all_forevery",
                                                                     sku_type=item["key"]
                                                                     )
            assert kill_deal["result"] is True, f'查询killer deal页面数据常{kill_deal}'
