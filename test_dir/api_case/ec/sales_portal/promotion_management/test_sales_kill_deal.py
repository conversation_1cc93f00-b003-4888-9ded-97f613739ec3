# !/usr/bin/python3
# -*- coding: utf-8 -*-


import weeeTest
from test_dir.api.ec.central_portal.central_promotion import CentralPromotion


class TestSalesKillDeal(weeeTest.TestCase):

    @weeeTest.mark.list('sales', 'Transaction',  'sales_portal', 'sales_portal_tb1')
    def test_sales_kill_deal(self, sales_header):
        """ # 促销管理-Killer deal-查询killer deal页面数据 """
        kill_deal = CentralPromotion().event_code_products_query(headers=sales_header,
                                                                 event_code="202309_Crazy_all_forevery")

        assert len(kill_deal["object"]["event_product_list"]) > 0, f'查询killer deal页面数据常{kill_deal}'

    @weeeTest.mark.list('sales', 'Transaction',  'sales_portal', 'sales_portal_tb1')
    def test_sales_kill_deal_filter(self, sales_header):
        """ # 促销管理-Killer deal-查询killer deal页面filter数据 """
        event_code_option = CentralPromotion().event_code_option(headers=sales_header)
        event_code = event_code_option["object"]["event_code"]
        event_code_type_list = event_code_option["object"]["event_code_type_list"]
        sales_region_list = event_code_option["object"]["sales_region_list"]
        sku_types = event_code_option["object"]["sku_types"]

        assert len(event_code) > 0, f'查询killer deal页面filter数据{event_code_option}'
        assert len(sales_region_list) > 0, f'查询killer deal页面filter数据{sales_region_list}'
        assert len(sku_types) > 0, f'查询killer deal页面filter数据{sku_types}'
        assert event_code_type_list is not None, f'查询killer deal页面filter数据{event_code_type_list}'
        for item in sku_types:
            kill_deal = CentralPromotion().event_code_products_query(headers=sales_header,
                                                                     event_code="202309_Crazy_all_forevery",
                                                                     sku_type=item["key"]
                                                                     )
            assert kill_deal["result"] is True, f'查询killer deal页面数据常{kill_deal}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_kill_deal_by_sales_org_id(self, sales_header):
        """ # 促销管理-Killer deal-按销售组织ID查询killer deal商品 """
        # 先获取可用的销售组织
        event_code_option = CentralPromotion().event_code_option(headers=sales_header)
        assert event_code_option["result"] is True, "获取killer deal filter选项失败"

        sales_region_list = event_code_option["object"]["sales_region_list"]
        assert len(sales_region_list) > 0, "没有可用的销售组织"

        # 测试前几个销售组织
        for region in sales_region_list[:3]:  # 只测试前3个
            kill_deal = CentralPromotion().event_code_products_query(
                headers=sales_header,
                event_code="202309_Crazy_all_forevery",
                sales_org_id=region["id"]
            )
            assert kill_deal["result"] is True, f'按销售组织ID{region["id"]}查询killer deal失败: {kill_deal}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_kill_deal_by_product_id(self, sales_header):
        """ # 促销管理-Killer deal-按商品ID查询killer deal商品 """
        # 先获取一个商品ID
        basic_result = CentralPromotion().event_code_products_query(
            headers=sales_header,
            event_code="202309_Crazy_all_forevery"
        )
        assert basic_result["result"] is True, "获取killer deal商品列表失败"

        if len(basic_result["object"]["event_product_list"]) > 0:
            product_id = basic_result["object"]["event_product_list"][0]["product_id"]

            # 使用获取到的商品ID进行查询
            product_result = CentralPromotion().event_code_products_query(
                headers=sales_header,
                event_code="202309_Crazy_all_forevery",
                product_id=str(product_id)
            )
            assert product_result["result"] is True, f'按商品ID查询killer deal失败: {product_result}'

            # 验证返回的商品ID是否正确
            if len(product_result["object"]["event_product_list"]) > 0:
                found = False
                for product in product_result["object"]["event_product_list"]:
                    if product["product_id"] == product_id:
                        found = True
                        break
                assert found, f'返回的商品中未找到指定的商品ID{product_id}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_kill_deal_by_type_id(self, sales_header):
        """ # 促销管理-Killer deal-按类型ID查询killer deal商品 """
        # 先获取可用的类型ID
        event_code_option = CentralPromotion().event_code_option(headers=sales_header)
        assert event_code_option["result"] is True, "获取killer deal filter选项失败"

        event_code_type_list = event_code_option["object"]["event_code_type_list"]
        if event_code_type_list and len(event_code_type_list) > 0:
            type_id = event_code_type_list[0]["id"]

            # 使用获取到的类型ID进行查询
            type_result = CentralPromotion().event_code_products_query(
                headers=sales_header,
                event_code="202309_Crazy_all_forevery",
                type_id=str(type_id)
            )
            assert type_result["result"] is True, f'按类型ID查询killer deal失败: {type_result}'



    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_kill_deal_combined_filters(self, sales_header):
        """ # 促销管理-Killer deal-组合条件查询killer deal商品 """
        # 先获取基础数据用于组合查询
        event_code_option = CentralPromotion().event_code_option(headers=sales_header)
        assert event_code_option["result"] is True, "获取killer deal filter选项失败"

        sku_types = event_code_option["object"]["sku_types"]
        sales_region_list = event_code_option["object"]["sales_region_list"]

        if len(sku_types) > 0 and len(sales_region_list) > 0:
            # 组合多个查询条件
            combined_result = CentralPromotion().event_code_products_query(
                headers=sales_header,
                event_code="202309_Crazy_all_forevery",
                sku_type=sku_types[0]["key"],
                sales_org_id=sales_region_list[0]["id"],
                page_no=1,
                page_size=10
            )
            assert combined_result["result"] is True, f'组合条件查询killer deal失败: {combined_result}'

            # 验证返回的数据符合查询条件
            if len(combined_result["object"]["event_product_list"]) > 0:
                for product in combined_result["object"]["event_product_list"]:
                    if "sku_type" in product:
                        assert product["sku_type"] == sku_types[0]["key"], \
                            f'返回的商品SKU类型不匹配，期望{sku_types[0]["key"]}，实际{product["sku_type"]}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_kill_deal_different_event_codes(self, sales_header):
        """ # 促销管理-Killer deal-不同活动代码查询killer deal商品 """
        # 先获取可用的活动代码
        event_code_option = CentralPromotion().event_code_option(headers=sales_header)
        assert event_code_option["result"] is True, "获取killer deal filter选项失败"

        event_codes = event_code_option["object"]["event_code"]
        assert len(event_codes) > 0, "没有可用的活动代码"

        # 测试前几个活动代码
        for event_code in event_codes[:3]:  # 只测试前3个
            code_result = CentralPromotion().event_code_products_query(
                headers=sales_header,
                event_code=event_code["event_code"]
            )
            assert code_result["result"] is True, f'活动代码{event_code["event_code"]}查询killer deal失败: {code_result}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_kill_deal_required_fields(self, sales_header):
        """ # 促销管理-Killer deal-验证返回数据必要字段 """
        kill_deal_result = CentralPromotion().event_code_products_query(
            headers=sales_header,
            event_code="202309_Crazy_all_forevery"
        )
        assert kill_deal_result["result"] is True, f'查询killer deal接口返回失败: {kill_deal_result}'

        # 验证返回结构
        assert "object" in kill_deal_result, "返回结果缺少object字段"
        assert "event_product_list" in kill_deal_result["object"], "返回结果缺少event_product_list字段"
        assert "page_no" in kill_deal_result["object"], "返回结果缺少page_no字段"
        assert "page_size" in kill_deal_result["object"], "返回结果缺少page_size字段"
        assert "total" in kill_deal_result["object"], "返回结果缺少total字段"

        # 验证商品的必要字段
        if len(kill_deal_result["object"]["event_product_list"]) > 0:
            required_fields = ["product_id", "product_name", "price", "discount_price"]
            for product in kill_deal_result["object"]["event_product_list"][:3]:  # 只检查前3条数据
                for field in required_fields:
                    assert field in product, f'killer deal商品缺少必要字段{field}: {product}'


    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_kill_deal_sku_type_validation(self, sales_header):
        """ # 促销管理-Killer deal-SKU类型验证查询killer deal商品 """
        # 测试所有支持的SKU类型
        sku_types = ["normal", "seller", "restaurant_pa", "restaurant_od", "fbw"]

        for sku_type in sku_types:
            sku_result = CentralPromotion().event_code_products_query(
                headers=sales_header,
                event_code="202309_Crazy_all_forevery",
                sku_type=sku_type
            )
            assert sku_result["result"] is True, f'SKU类型{sku_type}查询killer deal失败: {sku_result}'

            # 验证返回的商品SKU类型是否正确
            if len(sku_result["object"]["event_product_list"]) > 0:
                for product in sku_result["object"]["event_product_list"]:
                    if "sku_type" in product:
                        assert product["sku_type"] == sku_type, \
                            f'返回的商品SKU类型不匹配，期望{sku_type}，实际{product["sku_type"]}'
