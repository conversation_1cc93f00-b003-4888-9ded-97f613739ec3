# !/usr/bin/python3
# -*- coding: utf-8 -*-


import weeeTest
from test_dir.api.ec.central_portal.central_growth import CentralGrowth
from test_dir.api.ec.central_portal.central_promotion import CentralPromotion


class TestSalesPromotionList(weeeTest.TestCase):
    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_promotion_list_by_type_and_status(self, sales_header):
        """ # 促销管理-Promotion list-按类型和状态查询Promotion list页面数据 """
        # "type": type,  # promo_gift\promo_discount\promo_reduce
        # "status": status,  # 10\20\30\40
        type_list = ["promo_gift", "promo_discount", "promo_reduce"]
        status_list = ["10", "20", "30", "40"]

        for item in type_list:
            promotion_list = CentralPromotion().promotion_schedule_list(headers=sales_header, type=item)
            assert promotion_list["result"] is True, f'按类型{item}查询Promotion list失败: {promotion_list}'
            assert len(promotion_list["object"]["data"]) > 0, f'按类型{item}查询Promotion list数据为空{promotion_list}'

        for item in status_list:
            promotion_list = CentralPromotion().promotion_schedule_list(headers=sales_header, status=item)
            assert promotion_list["result"] is True, f'按状态{item}查询Promotion list失败: {promotion_list}'
            assert len(promotion_list["object"]["data"]) > 0, f'按状态{item}查询Promotion list数据为空{promotion_list}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_promotion_list_submit(self, sales_header):
        """ # 促销管理-Promotion list-提交Promotion list页面数据 """
        promotion_list = CentralPromotion().promotion_schedule_list(headers=sales_header, status="10")
        assert promotion_list["result"] is True, "获取Promotion list失败"

        if len(promotion_list["object"]["data"]) > 0:
            ps_id = promotion_list["object"]["data"][0]["id"]
            schedule_submit = CentralPromotion().promotion_schedule_submit(headers=sales_header, ps_id=ps_id)
            assert schedule_submit["result"] is True, f'提交Promotion list接口返回失败: {schedule_submit}'
            assert schedule_submit["object"] == "success", f'提交Promotion list页面数据异常{schedule_submit}'
