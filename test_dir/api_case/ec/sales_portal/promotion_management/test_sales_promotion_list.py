# !/usr/bin/python3
# -*- coding: utf-8 -*-


import weeeTest
from test_dir.api.ec.central_portal.central_growth import CentralGrowth
from test_dir.api.ec.central_portal.central_promotion import CentralPromotion


class TestSalesPromotionList(weeeTest.TestCase):
    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_promotion_list(self, sales_header):
        """ # 促销管理-Promotion list-查询Promotion list页面数据 """
        # "type": type,  # promo_gift\promo_discount\promo_reduce
        # "status": status,  # 10\20\30\40
        type = ["promo_gift", "promo_discount", "promo_reduce"]
        status = ["10", "20", "30", "40"]
        for item in type:
            coupon_plan_list = CentralPromotion().promotion_schedule_list(headers=sales_header, type=item)
            assert len(coupon_plan_list["object"]["data"]) > 0, f'查询Promotion list页面数据异常{coupon_plan_list}'
        for item in status:
            coupon_plan_list = CentralPromotion().promotion_schedule_list(headers=sales_header, status=item)
            assert len(coupon_plan_list["object"]["data"]) > 0, f'查询Promotion list页面数据异常{coupon_plan_list}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal_tb1')
    def test_sales_promotion_list_submit(self, sales_header):
        """ # 促销管理-Promotion list-提交Promotion list页面数据 """
        coupon_plan_list = CentralPromotion().promotion_schedule_list(headers=sales_header, status="10")
        schedule_submit = CentralPromotion().promotion_schedule_submit(headers=sales_header,
                                                                       ps_id=coupon_plan_list["object"]["data"][0]["id"])
        assert schedule_submit["object"] == "success", f'提交Promotion list页面数据异常{schedule_submit}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_promotion_list_by_keyword(self, sales_header):
        """ # 促销管理-Promotion list-按关键词查询Promotion list """
        # 先获取一个活动名称用于关键词搜索
        basic_result = CentralPromotion().promotion_schedule_list(headers=sales_header)
        assert basic_result["result"] is True, "获取Promotion list失败"

        if len(basic_result["object"]["data"]) > 0:
            # 使用活动名称的一部分作为关键词
            first_promotion = basic_result["object"]["data"][0]
            if "ps_title" in first_promotion and first_promotion["ps_title"]:
                keyword = first_promotion["ps_title"][:3]  # 取前3个字符作为关键词

                keyword_result = CentralPromotion().promotion_schedule_list(
                    headers=sales_header,
                    keyword=keyword
                )
                assert keyword_result["result"] is True, f'按关键词查询Promotion list失败: {keyword_result}'


    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_promotion_list_by_creator_user(self, sales_header):
        """ # 促销管理-Promotion list-按创建用户查询Promotion list """
        # 先获取一个创建用户
        basic_result = CentralPromotion().promotion_schedule_list(headers=sales_header)
        assert basic_result["result"] is True, "获取Promotion list失败"

        if len(basic_result["object"]["data"]) > 0:
            first_promotion = basic_result["object"]["data"][0]
            if "creator_user" in first_promotion and first_promotion["creator_user"]:
                creator_user = first_promotion["creator_user"]

                creator_result = CentralPromotion().promotion_schedule_list(
                    headers=sales_header,
                    creator_user=creator_user
                )
                assert creator_result["result"] is True, f'按创建用户查询Promotion list失败: {creator_result}'

                # 验证返回的数据创建用户是否正确
                if len(creator_result["object"]["data"]) > 0:
                    for promotion in creator_result["object"]["data"]:
                        if "creator_user" in promotion:
                            assert promotion["creator_user"] == creator_user, \
                                f'返回的创建用户不匹配，期望{creator_user}，实际{promotion["creator_user"]}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_promotion_list_by_sales_org_ids(self, sales_header):
        """ # 促销管理-Promotion list-按销售组织ID查询Promotion list """
        # 测试常见的销售组织ID
        sales_org_ids_list = [[1], [2], [4], [1, 2, 4]]

        for sales_org_ids in sales_org_ids_list:
            org_result = CentralPromotion().promotion_schedule_list(
                headers=sales_header,
                sales_org_ids=sales_org_ids
            )
            assert org_result["result"] is True, f'按销售组织ID{sales_org_ids}查询Promotion list失败: {org_result}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_promotion_list_by_gift_sku_id(self, sales_header):
        """ # 促销管理-Promotion list-按赠品SKU ID查询Promotion list """
        # 先获取一个赠品SKU ID
        gift_result = CentralPromotion().promotion_schedule_list(
            headers=sales_header,
            type="promo_gift"  # 赠品活动
        )
        assert gift_result["result"] is True, "获取赠品活动列表失败"

        if len(gift_result["object"]["data"]) > 0:
            first_gift_promotion = gift_result["object"]["data"][0]
            if "gift_sku_id" in first_gift_promotion and first_gift_promotion["gift_sku_id"]:
                gift_sku_id = first_gift_promotion["gift_sku_id"]

                sku_result = CentralPromotion().promotion_schedule_list(
                    headers=sales_header,
                    gift_sku_id=gift_sku_id
                )
                assert sku_result["result"] is True, f'按赠品SKU ID查询Promotion list失败: {sku_result}'



    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_promotion_list_combined_filters(self, sales_header):
        """ # 促销管理-Promotion list-组合条件查询Promotion list """
        # 组合多个查询条件
        combined_result = CentralPromotion().promotion_schedule_list(
            headers=sales_header,
            type="promo_discount",
            status="10",
            biz_type="normal",
            pageSize=10,
            startColumn=0
        )
        assert combined_result["result"] is True, f'组合条件查询Promotion list失败: {combined_result}'

        # 验证返回的数据符合查询条件
        if len(combined_result["object"]["data"]) > 0:
            for promotion in combined_result["object"]["data"]:
                if "type" in promotion:
                    assert promotion["type"] == "promo_discount", \
                        f'返回的活动类型不匹配，期望promo_discount，实际{promotion["type"]}'
                if "status" in promotion:
                    assert promotion["status"] == "10", \
                        f'返回的活动状态不匹配，期望10，实际{promotion["status"]}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_promotion_list_required_fields(self, sales_header):
        """ # 促销管理-Promotion list-验证返回数据必要字段 """
        promotion_result = CentralPromotion().promotion_schedule_list(headers=sales_header)
        assert promotion_result["result"] is True, f'查询Promotion list接口返回失败: {promotion_result}'

        # 验证返回结构
        assert "object" in promotion_result, "返回结果缺少object字段"
        assert "data" in promotion_result["object"], "返回结果缺少data字段"

        # 验证活动的必要字段
        if len(promotion_result["object"]["data"]) > 0:
            required_fields = ["id", "ps_title", "type", "status", "start_time", "end_time"]
            for promotion in promotion_result["object"]["data"][:3]:  # 只检查前3条数据
                for field in required_fields:
                    assert field in promotion, f'Promotion数据缺少必要字段{field}: {promotion}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_promotion_schedule_detail_basic(self, sales_header):
        """ # 促销管理-Promotion detail-基本查询活动详情 """
        # 先获取一个活动ID
        promotion_list = CentralPromotion().promotion_schedule_list(headers=sales_header)
        assert promotion_list["result"] is True, "获取Promotion list失败"

        if len(promotion_list["object"]["data"]) > 0:
            ps_id = promotion_list["object"]["data"][0]["id"]

            # 查询活动详情
            detail_result = CentralPromotion().query_promotion_schedule_detail(
                headers=sales_header,
                vender_id=ps_id
            )
            assert detail_result["result"] is True, f'查询活动详情失败: {detail_result}'
            assert "object" in detail_result, "返回结果缺少object字段"

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_promotion_schedule_detail_required_fields(self, sales_header):
        """ # 促销管理-Promotion detail-验证活动详情必要字段 """
        # 先获取一个活动ID
        promotion_list = CentralPromotion().promotion_schedule_list(headers=sales_header)
        assert promotion_list["result"] is True, "获取Promotion list失败"

        if len(promotion_list["object"]["data"]) > 0:
            ps_id = promotion_list["object"]["data"][0]["id"]

            # 查询活动详情
            detail_result = CentralPromotion().query_promotion_schedule_detail(
                headers=sales_header,
                vender_id=ps_id
            )
            assert detail_result["result"] is True, f'查询活动详情失败: {detail_result}'

            # 验证必要字段
            required_fields = [
                "ps_id", "ps_title", "start_time", "end_time", "type",
                "biz_type", "status", "ps_scope_content", "ps_rule_content",
                "ps_limit_content", "sales_org_ids"
            ]

            detail_obj = detail_result["object"]
            for field in required_fields:
                assert field in detail_obj, f'活动详情缺少必要字段{field}: {detail_obj}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_promotion_schedule_detail_gift_type(self, sales_header):
        """ # 促销管理-Promotion detail-查询赠品类型活动详情 """
        # 先获取一个赠品类型活动
        gift_list = CentralPromotion().promotion_schedule_list(
            headers=sales_header,
            type="promo_gift"
        )
        assert gift_list["result"] is True, "获取赠品活动列表失败"

        if len(gift_list["object"]["data"]) > 0:
            ps_id = gift_list["object"]["data"][0]["id"]

            # 查询赠品活动详情
            detail_result = CentralPromotion().query_promotion_schedule_detail(
                headers=sales_header,
                vender_id=ps_id
            )
            assert detail_result["result"] is True, f'查询赠品活动详情失败: {detail_result}'

            # 验证活动类型
            assert detail_result["object"]["type"] == "promo_gift", \
                f'活动类型不匹配，期望promo_gift，实际{detail_result["object"]["type"]}'

            # 验证赠品活动特有字段
            if "ps_products" in detail_result["object"]:
                ps_products = detail_result["object"]["ps_products"]
                if len(ps_products) > 0:
                    product_fields = ["product_id", "limit_qty", "promote_price", "limit_order_quantity"]
                    for field in product_fields:
                        assert field in ps_products[0], f'赠品商品缺少字段{field}: {ps_products[0]}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_promotion_schedule_detail_discount_type(self, sales_header):
        """ # 促销管理-Promotion detail-查询折扣类型活动详情 """
        # 先获取一个折扣类型活动
        discount_list = CentralPromotion().promotion_schedule_list(
            headers=sales_header,
            type="promo_discount"
        )
        assert discount_list["result"] is True, "获取折扣活动列表失败"

        if len(discount_list["object"]["data"]) > 0:
            ps_id = discount_list["object"]["data"][0]["id"]

            # 查询折扣活动详情
            detail_result = CentralPromotion().query_promotion_schedule_detail(
                headers=sales_header,
                vender_id=ps_id
            )
            assert detail_result["result"] is True, f'查询折扣活动详情失败: {detail_result}'

            # 验证活动类型
            assert detail_result["object"]["type"] == "promo_discount", \
                f'活动类型不匹配，期望promo_discount，实际{detail_result["object"]["type"]}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_promotion_schedule_detail_reduce_type(self, sales_header):
        """ # 促销管理-Promotion detail-查询满减类型活动详情 """
        # 先获取一个满减类型活动
        reduce_list = CentralPromotion().promotion_schedule_list(
            headers=sales_header,
            type="promo_reduce"
        )
        assert reduce_list["result"] is True, "获取满减活动列表失败"

        if len(reduce_list["object"]["data"]) > 0:
            ps_id = reduce_list["object"]["data"][0]["id"]

            # 查询满减活动详情
            detail_result = CentralPromotion().query_promotion_schedule_detail(
                headers=sales_header,
                vender_id=ps_id
            )
            assert detail_result["result"] is True, f'查询满减活动详情失败: {detail_result}'

            # 验证活动类型
            assert detail_result["object"]["type"] == "promo_reduce", \
                f'活动类型不匹配，期望promo_reduce，实际{detail_result["object"]["type"]}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_promotion_schedule_detail_status_validation(self, sales_header):
        """ # 促销管理-Promotion detail-验证活动状态详情 """
        # 测试不同状态的活动详情
        status_list = ["10", "20", "30", "40"]

        for status in status_list:
            status_result = CentralPromotion().promotion_schedule_list(
                headers=sales_header,
                status=status
            )
            assert status_result["result"] is True, f"获取状态{status}活动列表失败"

            if len(status_result["object"]["data"]) > 0:
                ps_id = status_result["object"]["data"][0]["id"]

                # 查询活动详情
                detail_result = CentralPromotion().query_promotion_schedule_detail(
                    headers=sales_header,
                    vender_id=ps_id
                )
                assert detail_result["result"] is True, f'查询状态{status}活动详情失败: {detail_result}'

                # 验证状态匹配
                assert str(detail_result["object"]["status"]) == status, \
                    f'活动状态不匹配，期望{status}，实际{detail_result["object"]["status"]}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_promotion_schedule_detail_content_validation(self, sales_header):
        """ # 促销管理-Promotion detail-验证活动内容字段 """
        # 先获取一个活动ID
        promotion_list = CentralPromotion().promotion_schedule_list(headers=sales_header)
        assert promotion_list["result"] is True, "获取Promotion list失败"

        if len(promotion_list["object"]["data"]) > 0:
            ps_id = promotion_list["object"]["data"][0]["id"]

            # 查询活动详情
            detail_result = CentralPromotion().query_promotion_schedule_detail(
                headers=sales_header,
                vender_id=ps_id
            )
            assert detail_result["result"] is True, f'查询活动详情失败: {detail_result}'

            detail_obj = detail_result["object"]

            # 验证内容字段格式
            content_fields = ["ps_scope_content", "ps_rule_content", "ps_limit_content"]
            for field in content_fields:
                if field in detail_obj and detail_obj[field]:
                    # 这些字段应该是JSON字符串格式
                    import json
                    try:
                        json.loads(detail_obj[field])
                    except json.JSONDecodeError:
                        assert False, f'{field}字段不是有效的JSON格式: {detail_obj[field]}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_promotion_schedule_detail_time_validation(self, sales_header):
        """ # 促销管理-Promotion detail-验证活动时间字段 """
        # 先获取一个活动ID
        promotion_list = CentralPromotion().promotion_schedule_list(headers=sales_header)
        assert promotion_list["result"] is True, "获取Promotion list失败"

        if len(promotion_list["object"]["data"]) > 0:
            ps_id = promotion_list["object"]["data"][0]["id"]

            # 查询活动详情
            detail_result = CentralPromotion().query_promotion_schedule_detail(
                headers=sales_header,
                vender_id=ps_id
            )
            assert detail_result["result"] is True, f'查询活动详情失败: {detail_result}'

            detail_obj = detail_result["object"]

            # 验证时间字段
            assert "start_time" in detail_obj, "缺少开始时间字段"
            assert "end_time" in detail_obj, "缺少结束时间字段"
            assert "in_dtm" in detail_obj, "缺少创建时间字段"
            assert "edit_dtm" in detail_obj, "缺少编辑时间字段"

            # 验证时间逻辑
            if detail_obj["start_time"] and detail_obj["end_time"]:
                assert detail_obj["start_time"] < detail_obj["end_time"], \
                    f'开始时间应该小于结束时间，开始:{detail_obj["start_time"]}，结束:{detail_obj["end_time"]}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_promotion_schedule_detail_sales_org_validation(self, sales_header):
        """ # 促销管理-Promotion detail-验证销售组织字段 """
        # 先获取一个活动ID
        promotion_list = CentralPromotion().promotion_schedule_list(headers=sales_header)
        assert promotion_list["result"] is True, "获取Promotion list失败"

        if len(promotion_list["object"]["data"]) > 0:
            ps_id = promotion_list["object"]["data"][0]["id"]

            # 查询活动详情
            detail_result = CentralPromotion().query_promotion_schedule_detail(
                headers=sales_header,
                vender_id=ps_id
            )
            assert detail_result["result"] is True, f'查询活动详情失败: {detail_result}'

            detail_obj = detail_result["object"]

            # 验证销售组织字段
            assert "sales_org_ids" in detail_obj, "缺少销售组织ID字段"

            # 如果有销售组织ID，验证格式
            if detail_obj["sales_org_ids"]:
                # 销售组织ID应该是逗号分隔的字符串格式，如 "4,7,1,5,2"
                org_ids = detail_obj["sales_org_ids"].split(",")
                for org_id in org_ids:
                    assert org_id.strip().isdigit(), f'销售组织ID格式错误: {org_id}'


    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_promotion_schedule_detail_preview_url_validation(self, sales_header):
        """ # 促销管理-Promotion detail-验证预览URL字段 """
        # 先获取一个活动ID
        promotion_list = CentralPromotion().promotion_schedule_list(headers=sales_header)
        assert promotion_list["result"] is True, "获取Promotion list失败"

        if len(promotion_list["object"]["data"]) > 0:
            ps_id = promotion_list["object"]["data"][0]["id"]

            # 查询活动详情
            detail_result = CentralPromotion().query_promotion_schedule_detail(
                headers=sales_header,
                vender_id=ps_id
            )
            assert detail_result["result"] is True, f'查询活动详情失败: {detail_result}'

            detail_obj = detail_result["object"]

            # 验证预览URL字段
            if "preview_url" in detail_obj and detail_obj["preview_url"]:
                preview_url = detail_obj["preview_url"]
                # 验证URL格式
                assert preview_url.startswith("http"), f'预览URL格式错误: {preview_url}'
                assert "ps_id=" in preview_url, f'预览URL应该包含ps_id参数: {preview_url}'

