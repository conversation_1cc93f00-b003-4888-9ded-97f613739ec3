# !/usr/bin/python3
# -*- coding: utf-8 -*-


import weeeTest
from test_dir.api.ec.central_portal.central_growth import CentralGrowth
from test_dir.api.ec.central_portal.central_promotion import CentralPromotion


class TestSalesPromotionList(weeeTest.TestCase):
    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_promotion_list(self, sales_header):
        """ # 促销管理-Promotion list-查询Promotion list页面数据 """
        # "type": type,  # promo_gift\promo_discount\promo_reduce
        # "status": status,  # 10\20\30\40
        type = ["promo_gift", "promo_discount", "promo_reduce"]
        status = ["10", "20", "30", "40"]
        for item in type:
            coupon_plan_list = CentralPromotion().promotion_schedule_list(headers=sales_header, type=item)
            assert len(coupon_plan_list["object"]["data"]) > 0, f'查询Promotion list页面数据异常{coupon_plan_list}'
        for item in status:
            coupon_plan_list = CentralPromotion().promotion_schedule_list(headers=sales_header, status=item)
            assert len(coupon_plan_list["object"]["data"]) > 0, f'查询Promotion list页面数据异常{coupon_plan_list}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal_tb1')
    def test_sales_promotion_list_submit(self, sales_header):
        """ # 促销管理-Promotion list-提交Promotion list页面数据 """
        coupon_plan_list = CentralPromotion().promotion_schedule_list(headers=sales_header, status="10")
        schedule_submit = CentralPromotion().promotion_schedule_submit(headers=sales_header,
                                                                       ps_id=coupon_plan_list["object"]["data"][0]["id"])
        assert schedule_submit["object"] == "success", f'提交Promotion list页面数据异常{schedule_submit}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_promotion_list_by_keyword(self, sales_header):
        """ # 促销管理-Promotion list-按关键词查询Promotion list """
        # 先获取一个活动名称用于关键词搜索
        basic_result = CentralPromotion().promotion_schedule_list(headers=sales_header)
        assert basic_result["result"] is True, "获取Promotion list失败"

        if len(basic_result["object"]["data"]) > 0:
            # 使用活动名称的一部分作为关键词
            first_promotion = basic_result["object"]["data"][0]
            if "ps_title" in first_promotion and first_promotion["ps_title"]:
                keyword = first_promotion["ps_title"][:3]  # 取前3个字符作为关键词

                keyword_result = CentralPromotion().promotion_schedule_list(
                    headers=sales_header,
                    keyword=keyword
                )
                assert keyword_result["result"] is True, f'按关键词查询Promotion list失败: {keyword_result}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_promotion_list_by_biz_type(self, sales_header):
        """ # 促销管理-Promotion list-按业务类型查询Promotion list """
        biz_types = ["normal", "special"]

        for biz_type in biz_types:
            biz_result = CentralPromotion().promotion_schedule_list(
                headers=sales_header,
                biz_type=biz_type
            )
            assert biz_result["result"] is True, f'按业务类型{biz_type}查询Promotion list失败: {biz_result}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_promotion_list_by_creator_user(self, sales_header):
        """ # 促销管理-Promotion list-按创建用户查询Promotion list """
        # 先获取一个创建用户
        basic_result = CentralPromotion().promotion_schedule_list(headers=sales_header)
        assert basic_result["result"] is True, "获取Promotion list失败"

        if len(basic_result["object"]["data"]) > 0:
            first_promotion = basic_result["object"]["data"][0]
            if "creator_user" in first_promotion and first_promotion["creator_user"]:
                creator_user = first_promotion["creator_user"]

                creator_result = CentralPromotion().promotion_schedule_list(
                    headers=sales_header,
                    creator_user=creator_user
                )
                assert creator_result["result"] is True, f'按创建用户查询Promotion list失败: {creator_result}'

                # 验证返回的数据创建用户是否正确
                if len(creator_result["object"]["data"]) > 0:
                    for promotion in creator_result["object"]["data"]:
                        if "creator_user" in promotion:
                            assert promotion["creator_user"] == creator_user, \
                                f'返回的创建用户不匹配，期望{creator_user}，实际{promotion["creator_user"]}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_promotion_list_by_sales_org_ids(self, sales_header):
        """ # 促销管理-Promotion list-按销售组织ID查询Promotion list """
        # 测试常见的销售组织ID
        sales_org_ids_list = [[1], [2], [4], [1, 2, 4]]

        for sales_org_ids in sales_org_ids_list:
            org_result = CentralPromotion().promotion_schedule_list(
                headers=sales_header,
                sales_org_ids=sales_org_ids
            )
            assert org_result["result"] is True, f'按销售组织ID{sales_org_ids}查询Promotion list失败: {org_result}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_promotion_list_by_gift_sku_id(self, sales_header):
        """ # 促销管理-Promotion list-按赠品SKU ID查询Promotion list """
        # 先获取一个赠品SKU ID
        gift_result = CentralPromotion().promotion_schedule_list(
            headers=sales_header,
            type="promo_gift"  # 赠品活动
        )
        assert gift_result["result"] is True, "获取赠品活动列表失败"

        if len(gift_result["object"]["data"]) > 0:
            first_gift_promotion = gift_result["object"]["data"][0]
            if "gift_sku_id" in first_gift_promotion and first_gift_promotion["gift_sku_id"]:
                gift_sku_id = first_gift_promotion["gift_sku_id"]

                sku_result = CentralPromotion().promotion_schedule_list(
                    headers=sales_header,
                    gift_sku_id=gift_sku_id
                )
                assert sku_result["result"] is True, f'按赠品SKU ID查询Promotion list失败: {sku_result}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_promotion_list_by_time_range(self, sales_header):
        """ # 促销管理-Promotion list-按时间范围查询Promotion list """
        import datetime

        # 设置时间范围：过去7天到未来7天
        today = datetime.datetime.now()
        start_time = (today - datetime.timedelta(days=7)).strftime('%Y-%m-%d')
        end_time = (today + datetime.timedelta(days=7)).strftime('%Y-%m-%d')

        time_result = CentralPromotion().promotion_schedule_list(
            headers=sales_header,
            start_time=start_time,
            end_time=end_time
        )
        assert time_result["result"] is True, f'按时间范围查询Promotion list失败: {time_result}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_promotion_list_pagination(self, sales_header):
        """ # 促销管理-Promotion list-分页查询Promotion list """
        # 测试第一页，每页5条
        page1_result = CentralPromotion().promotion_schedule_list(
            headers=sales_header,
            pageSize=5,
            startColumn=0
        )
        assert page1_result["result"] is True, f'分页查询第一页失败: {page1_result}'

        # 验证分页信息
        assert len(page1_result["object"]["data"]) <= 5, \
            f'返回的数据量超过页面大小，实际{len(page1_result["object"]["data"])}'

        # 如果有足够数据，测试第二页
        if len(page1_result["object"]["data"]) == 5:
            page2_result = CentralPromotion().promotion_schedule_list(
                headers=sales_header,
                pageSize=5,
                startColumn=5
            )
            assert page2_result["result"] is True, f'分页查询第二页失败: {page2_result}'
            assert len(page2_result["object"]["data"]) <= 5, \
                f'第二页返回的数据量超过页面大小，实际{len(page2_result["object"]["data"])}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_promotion_list_combined_filters(self, sales_header):
        """ # 促销管理-Promotion list-组合条件查询Promotion list """
        # 组合多个查询条件
        combined_result = CentralPromotion().promotion_schedule_list(
            headers=sales_header,
            type="promo_discount",
            status="10",
            biz_type="normal",
            pageSize=10,
            startColumn=0
        )
        assert combined_result["result"] is True, f'组合条件查询Promotion list失败: {combined_result}'

        # 验证返回的数据符合查询条件
        if len(combined_result["object"]["data"]) > 0:
            for promotion in combined_result["object"]["data"]:
                if "type" in promotion:
                    assert promotion["type"] == "promo_discount", \
                        f'返回的活动类型不匹配，期望promo_discount，实际{promotion["type"]}'
                if "status" in promotion:
                    assert promotion["status"] == "10", \
                        f'返回的活动状态不匹配，期望10，实际{promotion["status"]}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_promotion_list_required_fields(self, sales_header):
        """ # 促销管理-Promotion list-验证返回数据必要字段 """
        promotion_result = CentralPromotion().promotion_schedule_list(headers=sales_header)
        assert promotion_result["result"] is True, f'查询Promotion list接口返回失败: {promotion_result}'

        # 验证返回结构
        assert "object" in promotion_result, "返回结果缺少object字段"
        assert "data" in promotion_result["object"], "返回结果缺少data字段"

        # 验证活动的必要字段
        if len(promotion_result["object"]["data"]) > 0:
            required_fields = ["id", "ps_title", "type", "status", "start_time", "end_time"]
            for promotion in promotion_result["object"]["data"][:3]:  # 只检查前3条数据
                for field in required_fields:
                    assert field in promotion, f'Promotion数据缺少必要字段{field}: {promotion}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_promotion_list_large_page_size(self, sales_header):
        """ # 促销管理-Promotion list-大页面尺寸查询Promotion list """
        # 测试较大的页面尺寸
        large_page_result = CentralPromotion().promotion_schedule_list(
            headers=sales_header,
            pageSize=50,
            startColumn=0
        )
        assert large_page_result["result"] is True, f'大页面尺寸查询Promotion list失败: {large_page_result}'

        # 验证返回的数据量不超过页面尺寸
        assert len(large_page_result["object"]["data"]) <= 50, \
            f'返回的数据量超过页面尺寸，实际{len(large_page_result["object"]["data"])}'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_promotion_list_invalid_type(self, sales_header):
        """ # 促销管理-Promotion list-无效活动类型查询Promotion list """
        # 使用无效的活动类型
        invalid_result = CentralPromotion().promotion_schedule_list(
            headers=sales_header,
            type="invalid_type"
        )
        # 接口应该能处理无效类型，通常返回空列表或错误
        assert invalid_result["result"] is not None, "无效活动类型查询应该有响应"

        # 如果接口返回成功，数据列表应该为空
        if invalid_result["result"] is True:
            assert len(invalid_result["object"]["data"]) == 0, \
                "无效活动类型应该返回空的数据列表"

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_promotion_list_invalid_status(self, sales_header):
        """ # 促销管理-Promotion list-无效活动状态查询Promotion list """
        # 使用无效的活动状态
        invalid_result = CentralPromotion().promotion_schedule_list(
            headers=sales_header,
            status="99"
        )
        # 接口应该能处理无效状态，通常返回空列表或错误
        assert invalid_result["result"] is not None, "无效活动状态查询应该有响应"

        # 如果接口返回成功，数据列表应该为空
        if invalid_result["result"] is True:
            assert len(invalid_result["object"]["data"]) == 0, \
                "无效活动状态应该返回空的数据列表"

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_promotion_list_invalid_pagination(self, sales_header):
        """ # 促销管理-Promotion list-无效分页参数查询Promotion list """
        # 测试无效的页面大小（0或负数）
        invalid_size_result = CentralPromotion().promotion_schedule_list(
            headers=sales_header,
            pageSize=0,
            startColumn=0
        )
        # 接口应该能处理无效页面大小
        assert invalid_size_result["result"] is not None, "无效页面大小查询应该有响应"

        # 测试无效的起始位置（负数）
        invalid_start_result = CentralPromotion().promotion_schedule_list(
            headers=sales_header,
            pageSize=20,
            startColumn=-1
        )
        # 接口应该能处理无效起始位置
        assert invalid_start_result["result"] is not None, "无效起始位置查询应该有响应"

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_promotion_list_empty_params(self, sales_header):
        """ # 促销管理-Promotion list-空参数查询Promotion list """
        # 测试所有可选参数都为空的情况
        empty_params_result = CentralPromotion().promotion_schedule_list(
            headers=sales_header,
            keyword=None,
            biz_type="normal",
            sales_org_ids=None,
            creator_user=None,
            status=None,
            end_time=None,
            start_time=None,
            type=None,
            gift_sku_id=None
        )
        assert empty_params_result["result"] is True, f'空参数查询Promotion list失败: {empty_params_result}'

        # 验证默认分页参数
        assert len(empty_params_result["object"]["data"]) <= 20, \
            f'默认页面大小不正确，实际返回{len(empty_params_result["object"]["data"])}条'

    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_promotion_list_order_validation(self, sales_header):
        """ # 促销管理-Promotion list-排序规则验证 """
        promotion_result = CentralPromotion().promotion_schedule_list(headers=sales_header)
        assert promotion_result["result"] is True, "查询Promotion list失败"

        # 验证排序规则：按ps_id降序
        if len(promotion_result["object"]["data"]) > 1:
            data_list = promotion_result["object"]["data"]
            for i in range(len(data_list) - 1):
                current_id = data_list[i]["id"]
                next_id = data_list[i + 1]["id"]
                assert current_id >= next_id, \
                    f'排序规则错误，当前ID{current_id}应该大于等于下一个ID{next_id}'
