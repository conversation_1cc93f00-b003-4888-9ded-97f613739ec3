import weeeTest
from test_dir.api.ec.ec_item.product.get_restock_tips import GetRestockTips



class TestGetRestockTips(weeeTest.TestCase):

    @weeeTest.mark.list('restock_tips', 'Transaction', 'fail_product')
    def test_get_restock_tips(self, ec_login_header):
        """test_get_restock_tips"""
        # 不要用固定数据
        GetRestockTips().get_restock_tips(headers=ec_login_header, product_ids=[10766])
        # 断言
        assert self.response["result"] is True
