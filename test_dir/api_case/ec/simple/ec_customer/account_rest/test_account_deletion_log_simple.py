import weeeTest

from test_dir.api.ec.ec_customer.account_rest.account_deletion import AccountDeletion

from test_dir.api.ec.ec_customer.signup_rest.email_sigup import EmailSignup



class TestAccountDeletionLog(weeeTest.TestCase):

    @weeeTest.mark.list('Social')
    def non_test_account_deletion_log(self, ec_anony_header):
        """删除账号操作日志接口"""
        # 用例没有assert
        EmailSignup().email_signup(headers=ec_anony_header)
        user_ids = list(self.response["object"]["user_id"])
        for id in user_ids:
            AccountDeletion().account_deletion_log(headers=ec_anony_header, userId=id)
            data = self.response





if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net', env='tb1', debug=True)
