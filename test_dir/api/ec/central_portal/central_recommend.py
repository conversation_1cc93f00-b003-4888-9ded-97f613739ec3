# -*- coding: utf-8 -*-
"""
<AUTHOR>  suqin she
@Version        :  V1.0.0
------------------------------------
@Description    :  
@CreateTime     :  2024/3/27
@Software       :  PyCharm
------------------------------------
"""
from datetime import datetime, timedelta
# import datetime
import weeeTest

from datetime import datetime, timedelta


class CentralRecommend(weeeTest.TestCase):
    def recommend_trace_product_list(self, headers, experiment: str = None,
                                     input_experiment: str = None, platform: str = "h5",
                                     zipcode: str = "98011",
                                     sales_org_id: str = 4, lang: str = "en", store: str = "cn",
                                     flow: str = "re-ranking",
                                     user_id: str = None,
                                     date: str = (datetime.now() + timedelta(days=2)).strftime("%Y-%m-%d"),
                                     experiment_id: str = None,
                                     experiment_group: str = None,
                                     input_experiment_id: str = None,
                                     input_experiment_group: str = None,
                                     app_version: str = None,
                                     flow_id: str = None,
                                     pageSize: int = 20,
                                     startColumn: int = 0
                                     ):
        """
        商品销售信息管理/商品内容信息管理/商品推荐规则
        """
        if experiment is not None:
            experiment = [{
                "id": experiment_id,
                "group": experiment_group}]
        if input_experiment is not None:
            input_experiment = [{
                "id": input_experiment_id,
                "group": input_experiment_group}]

        data = {
            "pageSize": pageSize,
            "startColumn": startColumn,
            "flow_id": flow_id,
            "scenes_id": 3,
            "sales_org_id": sales_org_id,
            "zipcode": zipcode,
            "platform": platform,
            "app_version": app_version,
            "user_id": user_id,
            "date": date,
            "lang": lang,
            "store": store,
            "experiment": experiment,
            "input_experiment": input_experiment,
            "flow": flow  # "retrieval"\ranking\re-ranking\cache

        }

        self.post(url='/central/recommend/trace/product/list', headers=headers, json=data)
        return self.response

    def recommend_router_scenes_list(self, headers):
        """
         商品销售信息管理/商品内容信息管理/商品推荐场景
        """
        data = None
        self.post(url='/central/recommend/router/scenes/list', headers=headers, json=data)
        return self.response

    def recommend_router_scenes_detail(self, headers, id):
        """
         商品销售信息管理/商品内容信息管理/商品推荐场景
        """
        data = {
            "id": id
        }
        self.get(url='/central/recommend/router/scenes/detail', headers=headers, params=data)
        return self.response

    def recommend_router_component_list(self, headers, scenes_id):
        """
         商品销售信息管理/商品内容信息管理/商品推荐场景
        """
        data = {
            "scenes_id": scenes_id
        }
        self.get(url='/central/recommend/router/component/method/list', headers=headers, params=data)
        return self.response

    def recommend_router_list(self, headers, scenes_id):
        """
         商品销售信息管理/商品内容信息管理/商品推荐场景
        """
        data = {
            "scenes_id": scenes_id
        }
        self.get(url='/central/recommend/router/list', headers=headers, params=data)
        return self.response

    def recommend_router_detail(self, headers, router_id):
        """
         商品销售信息管理/商品内容信息管理/商品推荐场景
        """
        data = {
            "id": router_id

        }
        self.get(url='/central/recommend/router/detail', headers=headers, params=data)
        return self.response

    def recommend_router_flow_list(self, headers, router_id):
        """
         商品销售信息管理/商品内容信息管理/商品推荐场景
        """
        data = {
            "router_id": router_id,
        }
        self.get(url='/central/recommend/router/flow/list', headers=headers, params=data)
        return self.response

    def recommend_router_rollback_list(self, headers, router_id, experiment_id=None,
                                       experiment_group=None):
        """
         商品销售信息管理/商品内容信息管理/商品推荐场景
        """
        data = {
            "router_id": router_id,
            "experiment_id": experiment_id,
            "experiment_group": experiment_group
        }
        self.get(url='/central/recommend/router/flow/rollback/list', headers=headers, params=data)
        return self.response

    def monitor_position_key_all(self, headers):
        """
         报表/产品发现/Position filter
        """
        data = None
        self.get(url='/central/recommend/monitor/position/key/all', headers=headers, params=data)
        return self.response

    def monitor_scenes_total(self, headers, tab,
                             platform: str = None, sales_org: str = None, position: str = None,
                             lang: str = None, store: str = None,
                             start_date: str = format(
                                 (datetime.now() - timedelta(weeks=1)).strftime("%Y-%m-%d")),
                             end_date: str = datetime.now().strftime("%Y-%m-%d"),
                             week_start: int = 1, week_end: int = 4

                             ):
        """
         报表/产品发现/
        """
        data_date = {"start_date": start_date,
                     "end_date": end_date,
                     "platform": platform,  # ["DWeb"],
                     "sales_org": sales_org,  # "MOF",
                     "position": position,  # ""
                     "lang": lang,  # en
                     "store": store  # "cn"
                     }
        data_week = {"week_start": week_start,
                     "week_end": week_end,
                     "platform": platform,  # ["DWeb"],
                     "sales_org": sales_org,  # "MOF",
                     "position": position,  # ""
                     "lang": lang,  # en
                     "store": store  # "cn"
                     }
        if tab == "Daily":
            data = data_date
        else:
            data = data_week

        self.post(url='/central/recommend/monitor/scenes/total', headers=headers, json=data)
        return self.response

    def monitor_scenes_biz_type(self, headers, tab,
                                platform: str = None, sales_org: str = None, position: str = None,
                                lang: str = None, store: str = None,
                                start_date: str = format(
                                    (datetime.now() - timedelta(weeks=1)).strftime("%Y-%m-%d")),
                                end_date: str = datetime.now().strftime("%Y-%m-%d"),
                                week_start: int = 1, week_end: int = 4

                                ):
        """
         报表/产品发现/
        """
        data_date = {"start_date": start_date,
                     "end_date": end_date,
                     "platform": platform,  # ["DWeb"],
                     "sales_org": sales_org,  # "MOF",
                     "position": position,  # ""
                     "lang": lang,  # en
                     "store": store  # "cn"
                     }
        data_week = {"week_start": week_start,
                     "week_end": week_end,
                     "platform": platform,  # ["DWeb"],
                     "sales_org": sales_org,  # "MOF",
                     "position": position,  # ""
                     "lang": lang,  # en
                     "store": store  # "cn"
                     }
        if tab == "Daily":
            data = data_date
        else:
            data = data_week
        self.post(url='/central/recommend/monitor/scenes/biz_type', headers=headers, json=data)
        return self.response

    def monitor_scenes_list(self, headers, tab,
                            platform: str = None, sales_org: str = None, position: str = None,
                            lang: str = None, store: str = None,
                            start_date: str = format(
                                (datetime.now() - timedelta(weeks=1)).strftime("%Y-%m-%d")),
                            end_date: str = datetime.now().strftime("%Y-%m-%d"),
                            week_start: int = 1, week_end: int = 4,
                            page_no: int = 1, page_size: int = 10, ):
        """
         报表/产品发现/
        """
        data_date = {"start_date": start_date,
                     "end_date": end_date,
                     "platform": platform,  # ["DWeb"],
                     "sales_org": sales_org,  # "MOF",
                     "position": position,  # ""
                     "lang": lang,  # en
                     "store": store,  # "cn"
                     "startColumn": 0,
                     "pageSize": 20,
                     "page": 1
                     }
        data_week = {"week_start": week_start,
                     "week_end": week_end,
                     "platform": platform,  # ["DWeb"],
                     "sales_org": sales_org,  # "MOF",
                     "position": position,  # ""
                     "lang": lang,  # en
                     "store": store,  # "cn"
                     "startColumn": 0,
                     "page_no": page_no,
                     "page_size": page_size,
                     }
        if tab == "Daily":
            data = data_date
        else:
            data = data_week

        self.post(url='/central/recommend/monitor/scenes/list', headers=headers, json=data)
        return self.response
