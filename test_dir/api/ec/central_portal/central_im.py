# -*- coding: utf-8 -*-
"""
<AUTHOR>  suqin she
@Version        :  V1.0.0
------------------------------------
@Description    :  
@CreateTime     :  2024/3/27
@Software       :  PyCharm
------------------------------------
"""
from datetime import datetime, timedelta
import weeeTest


class CentralIm(weeeTest.TestCase):
    def search_synonym_list(self, headers, product_id: str = None, pageSize: int = 20,
                            startColumn: int = 0
                            ):
        """
         商品销售信息管理/商品内容信息管理/同义词管理
        """
        data = {
            "keyword": product_id,
            "product_id": product_id,
            "pageSize": pageSize,
            "startColumn": startColumn
        }
        self.post(url='/central/im/search/synonym/list', headers=headers, json=data)
        return self.response

    def group_list(self, headers, product_id: str = None, pageSize: int = 20,
                   startColumn: int = 0
                   ):
        """
         商品销售信息管理/商品内容信息管理/商品组
        """
        data = {
            "keyword": product_id,
            "product_id": product_id,
            "pageSize": pageSize,
            "startColumn": startColumn
        }
        self.post(url='/central/im/group/list?seller_id=0', headers=headers, json=data)
        return self.response

    def group_product_detail(self, headers, group_id: str = "3668"
                             ):
        """
         商品销售信息管理/商品内容信息管理/商品组详情
        """
        data = None
        self.get(url='/central/im/group/' + str(group_id) + '/detail?seller_id=0', headers=headers, params=data)
        return self.response

    def group_product_check(self, headers, product_ids: list,
                            property_ids: list,
                            group_id: int = 3668
                            ):
        """
         商品销售信息管理/商品内容信息管理/商品组详情商品check
        """
        data = {"product_ids": product_ids,
                "property_ids": property_ids,
                "group_id": group_id}
        self.post(url='/central/im/group/product/check?seller_id=0', headers=headers, jason=data)
        return self.response

    def group_product_property(self, headers, multiple_support: bool = False
                               ):
        """
         商品销售信息管理/商品内容信息管理/商品组属性
        """
        data = {"multiple_support": multiple_support}
        self.get(url='/central/im/property', headers=headers, params=data)
        return self.response

    def group_property_value(self, headers, property_ids: int = 13,
                             ):
        """
         商品销售信息管理/商品内容信息管理/商品组详情商品属性property_value
        """
        data = None
        self.get(url='/central/im/property_value?property_ids=' + str(property_ids), headers=headers, params=data)
        return self.response

    def selling_point_list(self, headers, product_id: str = None, pageSize: int = 20,
                           startColumn: int = 0
                           ):
        """
         商品销售信息管理/商品内容信息管理/Selling Point
        """
        data = {
            "product_id": product_id,
            "pageSize": pageSize,
            "startColumn": startColumn
        }
        self.post(url='/central/im/selling_point/list', headers=headers, json=data)
        return self.response

    def selling_point(self, headers, product_id: str = None
                      ):
        """
         Reset to AI
        """
        data = {
            "product_id": product_id
        }
        self.delete(url='/central/im/selling_point', headers=headers, json=data)
        return self.response

    def product_listing_check(self, headers, product_id: str = None, zipcode: str = "98011"
                              ):
        """
         商品销售信息管理/商品内容信息管理/商品上架检查
        """
        data = {
            "product_id": product_id,
            "zipcode": zipcode
        }
        self.post(url='/central/im/deal/product/listing/check', headers=headers, json=data)
        return self.response

    def search_keyword_top_list(self, headers, keyword: str = None, product_id: str = None,
                                page: int = 1, pageSize: int = 20,
                                status: str = None):
        """
         商品销售信息管理/商品内容信息管理/关键词管理
        """
        data = {
            "keyword": keyword,
            "page": page,
            "product_id": product_id,
            "pageSize": pageSize,
            "status": status  # A\X
        }
        self.post(url='/central/im/search/keyword/top/list', headers=headers, json=data)
        return self.response

    def search_keyword_top_status(self, headers, rec_id: int, edit_user: str = "10923004",

                                  status: str = "A"):
        """
         商品销售信息管理/商品内容信息管理/关键词管理
        """
        data = {
            "edit_user": edit_user,
            "rec_id": rec_id,
            "status": status  # A\X
        }
        self.post(url='/central/im/search/keyword/top/status', headers=headers, json=data)
        return self.response

    def scene_list(self, headers, langs: list, stores: list,
                   pages: list, sales_org_ids: list
                   ):
        """
         商品销售信息管理/商品内容信息管理/置顶配置
        """
        data = {
            "langs": langs,  # ["en"]
            "stores": stores,  # ["cn"],
            "pages": pages,  # ["Home"],
            "sales_org_ids": sales_org_ids  # [2]
        }
        self.post(url='/central/im/scene/list', headers=headers, json=data)
        return self.response

    def scene_product_list(self, headers, scene_id: int, user_id: str = "10923004"):
        """
         商品销售信息管理/商品内容信息管理/置顶配置
        """
        data = {
            "scene_id": scene_id,  # ["en"]
            "user_id": user_id  # ["cn"],

        }
        self.get(url='/central/im/scene/product/list', headers=headers, params=data)
        return self.response

    def search_log_list(self, headers, user_id: str = None, search_type: str = None,
                        start_time: int = None, end_time: int = None,
                        pageSize: int = 20,
                        startColumn: int = 0):
        """
         商品销售信息管理/商品内容信息管理/图片搜索日志
        """
        data = {
            "startColumn": startColumn,
            "pageSize": pageSize,
            "search_type": search_type,
            "start_time": start_time,
            "end_time": end_time,
            "user_id": user_id
        }
        self.post(url='/central/im/search/log/list', headers=headers, json=data)
        return self.response

    def product_performance_v1_search(self, headers, page_key: str = None, mod_key: str = None,
                                      start_time: str = format(
                                          (datetime.now() - timedelta(weeks=1)).strftime("%Y-%m-%d")),
                                      end_time: str = datetime.now().strftime("%Y-%m-%d"),
                                      pageSize: int = 20, startColumn: int = 0,
                                      first_catalogue: str = None, vender_id: str = None,
                                      prod_pos: str = None, product_id: str = None,
                                      keyword: str = None,
                                      ):
        """
         首页/报表/商品指标看板/查询
        """
        data = {
            "pageSize": pageSize,
            "startColumn": startColumn,
            "start_time": start_time,
            "end_time": end_time,
            "page_key": page_key,
            "mod_key": mod_key,
            "first_catalogue": first_catalogue,
            "vender_id": vender_id,
            "prod_pos": prod_pos,
            "product_id": product_id,
            "keyword": keyword
        }

        self.post(url='/central/im/product/performance/v1/search', headers=headers, json=data)
        return self.response

    def nav_card_list(self, headers, start_time: int = None, end_time: int = None,
                      keyword: str = None, progress: str = None,
                      language: str = None, store: str = None, sales_org: int = None,
                      ):
        """
         内容管理/分类Icon活动
        """
        data = {"limit": 20,
                "offset": 0,
                "start_time": start_time,# 非必填:
                "end_time": end_time,# 非必填:
                "time_zone": "Asia/Shanghai",
                "keyword": keyword, # 非必填: "test"
                "progress": progress,# 非必填:  "10","20","30"
                "language": language,# 非必填:  "en","zh"
                "store": store,# 非必填: 当language = en 时才传这个参数，否则是None
                "sales_org": sales_org# 非必填:单选， 1
                }
        self.post(url='/central/im/cm/nav_card/list', headers=headers, json=data)
        return self.response

    def create_pre_sale(self, headers, product_id_0: str = None, product_id_1: str = None,
                        product_id_2: str = None, sales_org_ids: int = None,
                        in_user: str = "8838831", start_time: str = None, end_time: str = None
                        ):
        """
         创建预售活动
        """
        data = {
            "title": "create pre-sale",
            "type": "presale",
            "status": "A",
            "start_time": 1742526578,
            "end_time": 1774062578,
            "sales_org_ids": sales_org_ids,
            "sale_event_products": [
                {
                    "product_id": product_id_0,
                    "inv_qty": 200,
                    "more_link": ""
                },
                {
                    "product_id": product_id_1,
                    "inv_qty": 200,
                    "more_link": ""
                },
                {
                    "product_id": product_id_2,
                    "inv_qty": 200,
                    "more_link": ""
                }
            ],
            "rule": {"shipping_date_range": [start_time, end_time]},
            "more_link": "https://tb1.sayweee.net/zh/cms/page/activity/NbJMfanv",
            "in_user": in_user
        }
        self.post(url='/central/im/sale_event/add', headers=headers, json=data)
        return self.response

    def start_pre_sale(self, headers):
        """
         预售活动创建成功后，通过这个接口开始预售活动
        """
        data = None
        self.get(url='/central/im/sale_event/job/start', headers=headers, params=data)
        return self.response
