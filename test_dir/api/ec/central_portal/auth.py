# -*- coding: utf-8 -*-
"""
<AUTHOR>  suqin she
@Version        :  V1.0.0
------------------------------------
@Description    :
@CreateTime     :  2024/3/27
@Software       :  PyCharm
------------------------------------
"""
import weeeTest
from weeeTest import log, weeeConfig, jmespath


class Auth(weeeTest.TestCase):

    def central_login(self,headers, user_id: int = None, password: str = None, login_platform: str = 'TMS'):
        """
        central登录
        @param login_platform: 平台
        @param user_id: 账户ID
        @param password: 密码
        @return:
        """
        if user_id is None or password is None:
            raise Exception('登录的user_id,password不能为空')
        data = {
            "account": user_id,
            "password": password,
            "realm_key": "ecommerce",
            "login_platform": login_platform
        }
        self.post(url='/hub/auth/user/login', headers=headers, json=data)
        return self.response
        # auth = jmespath(self.response, "object.token")
        # if auth is not None and len(auth) > 0:
        #     log.info(f'Central登录成功,CentralToken:{auth}')
        #     headers["authorization"] = 'Bearer ' + auth
        # else:
        #     raise Exception(f'Central登录失败,msg:{jmespath(self.response, "message")}')
        # return self.response