# -*- coding: utf-8 -*-
"""
<AUTHOR>  suqin she
@Version        :  V1.0.0
------------------------------------
@Description    :  
@CreateTime     :  2024/3/27
@Software       :  PyCharm
------------------------------------
"""
import weeeTest
from weeeTest import log, weeeConfig


class CentralPromotion(weeeTest.TestCase):

    # 内容管理===========================================================================================================

    def promotion_schedule_list(self, headers, keyword: str = None, biz_type: str = "normal",
                                sales_org_ids: list = None, creator_user: str = None,
                                status: str = None, end_time: str = None,
                                start_time: str = None, type: str = None,
                                gift_sku_id: str = None, pageSize: int = 20,
                                startColumn: int = 0,
                                ):
        """
         查询后台活动列表页面
        """
        data = {
            "pageSize": pageSize,# 必填 默认20
            "startColumn": startColumn,# 必填 默认0
            "type": type,  # promo_gift\promo_discount\promo_reduce
            "status": status,  # 10\20\30\40
            "keyword": keyword, # 非必填 例如搜索test
            "biz_type": biz_type,# 必填 normal
            "creator_user": creator_user,# 非必填 创建活动的用户，例如用户id为9715640
            "gift_sku_id": gift_sku_id, # 非必填 赠品id 例如1127
            "sales_org_ids": sales_org_ids,# 非必填 例如[1,2,4]
            "order": {
                "orderColumn": "ps_id", # 必填 ps_id为活动id
                "orderRule": "desc"# 必填 desc
            },
            "start_time": start_time,# 非必填 例如1742526578
            "end_time": end_time # 非必填 例如1774062578
        }
        self.get(url='/central/promotion/promotions/schedule/list', headers=headers, params=data)
        return self.response

    def promotion_schedule_submit(self, headers, ps_id: int = 10185):
        """
         提交活动列表数据
        """
        data = None
        self.put(url="/central/promotion/promotions/schedule/" + str(ps_id) + "/submit", headers=headers, json=data)
        return self.response

    def create_promotion(self, headers, keyword: str = None, biz_type: str = "normal",
                         sales_org_ids: str = None, creator_user: str = None,
                         ps_title: str = None, end_time: str = None,
                         start_time: str = None, type: str = None,
                         gift_sku_id: str = None, vendor_id: int = 0,
                         priority: int = 100,
                         ):
        """
         创建活动列表页面
        """
        data = {
            "biz_type": biz_type,
            "type": type,
            "ps_title": ps_title,
            "vendor_id": vendor_id,
            "start_time": start_time,
            "end_time": end_time,
            "priority": priority,
            "sales_org_ids": sales_org_ids,  # "1,2,4"
            "ps_rule_content": "{\"max_times_per_user\":2,\"promo_description\":{\"en\":\"<p>title-en</p>\",\"zh\":\"<p>title-zh</p>\",\"zh_Hant\":\"<p>title-zht</p>\",\"ko\":\"<p>title-ko</p>\",\"ja\":\"<p>title-ja</p>\",\"vi\":\"<p>title-vi</p>\"},\"promo_img\":{\"vi\":\"https://img06.test.weeecdn.com/mkt/image/618/218/73CDF8ABBDBD1E35.png\"},\"min_subtotal_amount\":30,\"reduce\":20}",
            "ps_scope_content": "{\"scope_type\":6,\"category\":\"05\"}",
            "ps_limit_content": "{\"user_types\":[\"old\",\"dormant\"]}"
        }
        self.post(url='/central/promotion/promotions/schedule', headers=headers, json=data)
        return self.response

    def upload_img(self, headers, subType: str = "image", bizType: str = "mkt",
                   ):
        """
         上传图片
        """
        data = {
            "bizType": bizType,
            "subType": subType,
        }
        self.post(url='/resource/v2/upload/directly', headers=headers, json=data)
        return self.response

    # 优惠券管理===========================================================================================================

    def coupon_plan_list(self, headers, id: str = None, group_id: str = None,
                         type: str = None, remark: str = None,
                         status: str = None, end_time: str = None,
                         start_time: str = None, order_name: str = "rec_create_time",
                         order_ordinal: str = "desc", page_no: int = 1,
                         page_size: int = 20,
                         ):
        """
         查询优惠券计划列表页面
        """
        data = {
            "id": id, # 非必填 优惠券计划id 假设id=5046
            "group_id": group_id,# 非必填 优惠券组id 假设group_id=101
            "type": type,# 非必填 优惠券类型 Z:满折 D:满减 F:免邮 C:满减券
            "remark": remark,# 非必填 优惠券计划名称  假设为test
            "status": status,# 非必填 优惠券计划状态 A:可用 G:已提交 C:待提交 R:已拒绝
            "page_no": page_no,# 必填 默认1
            "page_size": page_size,# 必填 默认20
            "start_time": start_time, # 非必填 当前时间减7天
            "end_time": end_time,# 非必填 当前时间加7天
            "order_name": order_name, # 必填 默认rec_create_time
            "order_ordinal": order_ordinal # 必填 默认desc
        }
        self.post(url='/central/promotion/coupon_plan/list', headers=headers, json=data)
        return self.response

    def coupon_plan_update(self, headers, id: str = None, status: str = None):
        """
         更新优惠券计划列表页面
        """
        data = {
            "id": id,# 必填
            "status": status # 必填 A:可用 G:已提交 C:待提交 R:已拒绝

        }
        self.post(url='/central/promotion/coupon_plan/update', headers=headers, json=data)
        return self.response

    def coupon_internal_list(self, headers, code: str = None, coupon_plan_id: str = None,
                             user_id: str = "10923004", remark: str = None,
                             status: str = None, group_ids=None,
                             order_name: str = "id",
                             order_ordinal: str = "desc", page_no: int = 1,
                             page_size: int = 20, entire: int = 2,
                             backend: str = "b43fdd98b1fd705ae4c3a10cf25aad8a",
                             ):
        """
         查询优惠券列表页面
        """
        if group_ids is None:
            group_ids = []
        data = {
            "code": code,# 非必填 优惠券编码 例如为10000000000000000000000000000000
            "coupon_plan_id": coupon_plan_id, #必填 例如为10
            "remark": remark,# 非必填 优惠券名称 SIGN_UP_COUPON_DORMANT
            "user_id": user_id,  # 必填 例如9715640
            "page_no": page_no,# 必填 默认20
            "page_size": page_size,# 必填 默认1
            "status": status,  # A U E
            "entire": entire,# 必填 1 2
            "order_name": order_name,# 必填 默认id
            "order_ordinal": order_ordinal,# 必填 默认desc
            "group_ids": group_ids,# 非必填
            "backend": backend # 必填 例如b43fdd98b1fd705ae4c3a10cf25aad8a
        }
        self.post(url='/ec/promotion/coupon/internal/list', headers=headers, json=data)
        return self.response

    def coupon_issuance_list(self, headers, coupon_plan_id: str = None, group_id: str = None,
                             biz_type: str = None, remark: str = None,
                             page_no: int = 1,
                             page_size: int = 20,
                             ):
        """
         查询优惠券发放列表页面
        """
        data = {
            "coupon_plan_id": coupon_plan_id,# 必填 优惠券id 例如115
            "group_id": group_id,# 非必填 例如26
            "biz_type": biz_type,  # 区分weee和 mkpl 例如筛选weee
            "remark": remark,# 非必填 例如test
            "page_no": page_no,#必填 默认1
            "page_size": page_size# 必填默认20
        }
        self.post(url='/central/promotion/backend/coupon/issuance/list', headers=headers, json=data)
        return self.response

    def coupon_issuance_save(self, headers, plan_id: str = None, coupon_prefix: str = None,
                             coupon_length: str = None, remark: str = None,
                             apply_start_time: str = None, apply_expiration_time: str = None,
                             user_ids_str: str = None,
                             page_no: int = 1,
                             page_size: int = 20,
                             ):
        """
         创建优惠券发放
        """
        data = {
            "plan_id": plan_id,# 优惠券id 例如为1
            "coupon_prefix": coupon_prefix,
            "coupon_length": coupon_length,
            "remark": remark, #优惠券标记
            "apply_start_time": apply_start_time,# 优惠券开始时间
            "apply_expiration_time": apply_expiration_time,# 优惠券失效时间
            "user_ids_str": user_ids_str,
            "page_no": page_no,
            "page_size": page_size
        }
        self.post(url='/central/promotion/backend/coupon/issuance/save', headers=headers, json=data)
        return self.response

    def event_code_products_query(self, headers, event_code: str = "202309_Crazy_all_forevery",
                                  sales_org_id: int = 4,
                                  type_id: str = None, product_id: str = None,
                                  sku_type: str = None, timeStamp: str = None,
                                  page_no: int = 1,
                                  page_size: int = 20,
                                  ):
        """
         killer deal
        """
        data = {

            "event_code": event_code,# 必填项 event id
            "page_no": page_no,# 必填项 默认为1
            "page_size": page_size,# 必填项 默认为20
            "sales_org_id": sales_org_id,# 非必填 例如1销售组织
            "type_id": type_id, #非必填 例如1
            "product_id": product_id,#非必填 商品id 例如1127
            "sku_type": sku_type,# 非必填 商品类型为normal,seller,restaurant_pa,restaurant_od,fbw
            "timeStamp": timeStamp #必填 例如1751007094477

        }
        self.get(url='/central/promotion/event/admin/eventCode/products/query', headers=headers, params=data)
        return self.response

    def event_code_option(self, headers):
        """
         killer deal
        """
        data = None
        self.get(url='/central/promotion/event/admin/eventCode', headers=headers, params=data)
        return self.response

    def recommend_product_list(self, headers, status: str = None,
                               catalogue_num: str = None, product_id: str = None,
                               offset: int = 1,
                               limit: int = 20,
                               ):
        """
         推荐搭配
        """
        data = {
            "status": status,  # 非必填 Y/N
            "offset": offset, # 必填 0
            "limit": limit, # 必填 20
            "product_id": product_id,# 非必填 例如1127
            "catalogue_num": catalogue_num # 非必填 例如1
        }
        self.get(url='/central/promotion/recommend/product/list', headers=headers, params=data)
        return self.response

    def query_promotion_info(self, headers):
        """# 商家活动页获取活动详情信息"""
        data = {
            "pageSize": 20,#必填 默认20
            "startColumn": 0,#必填 默认0
            "biz_type": "normal",#必填 默认nomal
            "order": {"orderColumn": "ps_id", "orderRule": "desc"}  #必填 ps_id为活动id
        }

        self.post(url='/central/promotion/promotions/schedule/list', headers=headers, json=data)
        return self.response

    def query_promotion_schedule_detail(self, headers, vender_id: int = 10182):
        """# 商家活动页获取活动详情信息"""

        self.get(url="/central/promotion/promotions/schedule/" + str(vender_id) + "/detail", headers=headers)
        return self.response




if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
