# -*- coding: utf-8 -*-
"""
Sales MKPL 接口维护
"""
import weeeTest
from dateutil.relativedelta import relativedelta
from weeeTest import log, jmespath, weeeConfig
from typing import Dict, Optional, Union, List
from copy import deepcopy
import pytz
from time import time
from datetime import datetime, timedelta


class SellerMgmt(weeeTest.TestCase):
    """
    商家管理模块
    """

    def vendor_list(self, headers, size: int = 20, page: int = 1, current: int = 1, pageSize: int = 20,
                    vendor_id: int = None, title: str = None,
                    seller_type: list = None, shipping_district: str = None, status: str = None,
                    open_status: str = None, rec_creator_id: str = None,
                    launch_date_start: str = None, launch_date_end: str = None):
        """
        商家列表
        :param headers: sales login headers
        :param size:列表每页数量 默认20/page
        :param page:结果分页
        :param current:当前页
        :param pageSize:列表每页数量 默认20/page
        :param vendor_id:filter Merchant ID
        :param title:filter Merchant Name
        :param seller_type:filter Business Type
        :param shipping_district:filter Shipping District
        :param status:filter Review Status
        :param open_status:filter Open Status
        :param rec_creator_id:Account Manager
        :param launch_date_start:filter Launch Date开始时间
        :param launch_date_end:filter Launch Date结束时间
        :return:商家列表
        """
        param = {
            "current": current,
            "pageSize": pageSize,
            "vendor_id": vendor_id,
            "title": title,
            "seller_type": seller_type,
            "shipping_district": shipping_district,
            "status": status,
            "open_status": open_status,
            "rec_creator_id": rec_creator_id,
            "launch_date_start": launch_date_start,
            "launch_date_end": launch_date_end
        }
        # None不传参
        param = {k: v for k, v in param.items() if v is not None}
        data = {
            "size": size,
            "page": page,
            "param": param
        }
        # json str传参
        self.post(url='/central/mkpl/admin/seller/vender/list', headers=headers, json=data)
        return self.response

    def seller_logo_upload(self, headers,
                           file_path='seller_logo.png',
                           biz_type: str = 'brand', subtype: str = 'image'):
        """
        商家logo上传
        :param headers: sales login headers
        :param file_path: 文件路径
        :param biz_type: 业务类型 seller logo默认为brand
        :param subtype: 文件类型 seller logo默认为image
        :return: filename key size type url
        """
        upload_header = deepcopy(headers)
        del upload_header['Content-Type']
        file_type = file_path.split('.')[-1]
        file_name = file_path.split('/')[-1]
        files = [
            ('file', (f'{file_name}', open(file_path, 'rb'), f'image/{file_type}'))
        ]
        data = {"bizType": biz_type,
                "subType": subtype}

        self.post(url='/resource/v2/upload/directly', headers=upload_header, data=data,
                  files=files)
        return self.response

    def seller_optional_tag_list(self, headers):
        """
        商家可选tags列表
        :param headers: sales login headers
        :return: tags列表
        """
        self.get(url='/central/mkpl/admin/tag/list', headers=headers)
        return self.response

    def seller_optional_product_first_catalogues(self, headers):
        """
        创建商家可选Catalogues
        :param headers: sales login headers
        :return: 创建商家时可勾选的Catalogues
        """
        self.get(url='/central/mkpl/product/first_catalogues', headers=headers)
        return self.response

    def seller_optional_shipping_district(self, headers):
        """
        创建商家发货地可选国家
        :param headers: sales login headers
        :return: 创建商家时可勾选的国家
        """
        self.get(url='/central/mkpl/shipping/district', headers=headers)
        return self.response

    def seller_optional_registration_country(self, headers):
        """
        创建商家注册地址可选国家
        :param headers: sales login headers
        :return: 创建商家时注册地址可选国家
        """
        self.get(url='/central/mkpl/shipping/district', headers=headers)
        return self.response

    def seller_optional_ethnicity(self, headers, type_id: int = 119):
        """
        族裔选项列表
        :param headers:
        :param type_id : 119Origin Ethnicity 100:Target Ethnicity
        :return:族裔列表
        """
        param = {'type_id': type_id}
        self.get(url='/central/im/meta_code/list', params=param, headers=headers)
        return self.response

    def seller_optional_registration(self, headers, type_id: int = 119):
        """
        Business Registration Info选项State列表

        :param headers: 请求头部字典，包含认证和其他必要信息。
        :return: seller Business Registration States
        """
        url = "/central/mkpl/vender/query/area/info/0"

        # GET 请求
        self.get(url, headers=headers)
        return self.response

    def seller_metric_performance(self, headers, vendor_id: int = None):
        """
        商家绩效指标
        :param headers:sales header
        :param vendor_id:seller id
        :return:
        """
        param = {}
        self.get(url=f'/central/mkpl/admin/sellers/{vendor_id}/metric/performance', params=param, headers=headers)
        return self.response

    def metric_diagnosis(self, headers, vendor_id: int, type: str, days: int = 90):
        """
        商家客户服务Customer service Diagnosis
        @param headers:
        @param vendor_id:
        @param type:
        @param days:
        @return:
        """

        param = {
            "type": type,
            "days": days}

        self.get(url=f'/central/mkpl/admin/sellers/{vendor_id}/metric/diagnosis', headers=headers, params=param)
        return self.response

    def seller_metric_diagnosis_download(self, headers, seller_id: int, type: str = "partial_sku_oos_rate",
                                         days: int = 90, page: int = 1, size: int = 20):
        """
        商家metric下载接口
        :param headers:请求头字典（必填）
        :param seller_id:卖家ID
        :param type:指标类型，默认为 "partial_sku_oos_rate"（可选）
        :param days:时间跨度，默认为 90 天（可选）
        :param page:分页页码，默认为 1（可选）
        :param size:分页大小，默认为 20（可选）
        :return:下载文件的二进制内容response.content
        """
        seller_id = str(seller_id)
        url = f"/central/mkpl/admin/sellers/{seller_id}/metric/diagnosis/download"
        params = {
            "type": type,
            "days": days,
            "page": page,
            "size": size,
            "days": days
        }
        res = self.get(url=url, headers=headers, params=params)
        return res

    def seller_account_health_list(self, headers, page=1, size=20, seller_id: str = None):
        data = {
            "size": size,
            "page": page,
        }
        if seller_id:
            data["seller_id"] = str(seller_id)

        # json str传参
        self.post(url='/central/mkpl/admin/seller/performance/violation/account/health/list', headers=headers,
                  json=data)
        return self.response

    def seller_account_health_detail(self, headers, vendor_id: int):
        """
        商家violation 详情
        @param vendor_id:
        @return:
        """

        self.get(url=f'/central/mkpl/admin/seller/performance/violation/account/health/detail/{vendor_id}',
                 headers=headers)
        return self.response

    def seller_account_health_record_list(self, headers, seller_id: int, page: int = 1, size: int = 20,
                                          start_date: str = None,
                                          end_time: str = None):
        """
        商家违规记录
        @param headers:
        @param seller_id:
        @param page:
        @param size:
        @param start_date:
        @param end_time:
        @return:
        """

        data = {
            "seller_id": seller_id,
            "page": page,
            "size": size,
            "start_date": start_date,
            "end_date": end_time
        }

        self.post(url='/central/mkpl/admin/seller/performance/violation/account/health/records/list', headers=headers,
                  json=data)
        return self.response

    def seller_health_recoreds_detail(self, headers, summary_id: int = None, page: int = 1, size: int = 20):
        """
        商家违规记录详情
        @param summary_id:
        @param page:
        @param size:
        @return:
        """

        data = {
            "summary_id": summary_id,
            "page": page,
            "size": size
        }

        self.post(url='/central/mkpl/admin/seller/performance/violation/account/health/records/detail', headers=headers,
                  json=data)
        return self.response

    def seller_violation_detail(self, headers, violation_id: int = None):
        """
        商家违规详情弹窗
        @param headers:
        @param violation_id:
        @return:
        """

        param = {}
        self.get(url=f'/central/mkpl/admin/seller/performance/violation/account/health/violation/detail/{violation_id}',
                 headers=headers, params=param)
        return self.response

    def seller_performance_penalty_list(self, headers, seller_id: int, page: int = 1, size: int = 20):
        """
        商家惩罚列表
        @param headers:
        @param seller_id:
        @param page:
        @param size:
        @return:
        """

        data = {
            "seller_id": seller_id,
            "page": page,
            "size": size
        }

        self.post(url='/central/mkpl/admin/seller/performance/penalty/list', headers=headers, json=data)
        return self.response

    def seller_performance_penalty_detail(self, headers, seller_id: int, penalty_id: int):
        """
        商家惩罚详情
        @param headers:
        @param seller_id:
        @param penalty_id:
        @return:
        """

        self.get(url=f'/central/mkpl/admin/seller/performance/penalty/{seller_id}/{penalty_id}', headers=headers)
        return self.response

    def violation_mgmt_list(self, headers, page: int = 1, size: int = 10, seller_id: str = None, start_time: int = None,
                            end_time: int = None, ops_name: str = None):
        """
        商家违规管理列表
        :param headers:
        :param page:分页页码
        :param size:分页大小
        :param seller_id:商家id
        :param start_time:开始过滤时间
        :param end_time:结束过滤时间
        :param ops_name:ops_name筛选
        :return:违规管理列表
        """
        data = {
            "page": page,
            "size": size,
            "seller_id": seller_id,
            "ops_name": ops_name,
            "start_time": start_time,
            "end_time": end_time
        }
        self.post(url='/central/mkpl/admin/seller/performance/violation/mgmt/list', headers=headers, json=data)
        return self.response

    def violation_mgmt_detail(self, headers, id: int):
        """
        商家违规管理详情
        :param headers:
        :param id: 违规id
        :return:违规管理详情
        """
        self.get(url=f'/central/mkpl/admin/seller/performance/violation/mgmt/detail/{id}', headers=headers)
        return self.response

    def review_product(self, headers, size: int = 20, page: int = 1,
                       biz_type: str = 'seller', order_id: int = None,
                       order_time_from: int = None, order_time_to: int = None, product_id: int = None,
                       rating: list = None, rating_time_from: int = None, rating_time_to: int = None,
                       seller_id: int = None):
        """
         商家商品评论列表
        :param headers:sales header
        :param size:每页数据
        :param page:分页数
        :param biz_type:业务类型 默认seller
        :param order_id:订单号
        :param order_time_from:筛选订单开始时间戳
        :param order_time_to:筛选订单结束时间戳
        :param product_id:商品id
        :param rating:用户评分 Positive rating: [5] Neutral：[3, 4] Negative：[1, 2]
        :param rating_time_from:用户评分筛选开始时间戳
        :param rating_time_to:用户评分筛选结束时间戳
        :param seller_id:商家id
        :return:符合条件的review
        """

        param = {
            "biz_type": biz_type,
            "order_id": order_id,
            "order_time_from": order_time_from,
            "order_time_to": order_time_to,
            "product_id": product_id,
            "rating": rating,
            "rating_time_from": rating_time_from,
            "rating_time_to": rating_time_to,
            "seller_id": seller_id
        }
        # None不传参
        param = {k: v for k, v in param.items() if v is not None}
        data = {
            "size": size,
            "page": page,
            "param": param
        }
        # json str传参
        self.post(url='/central/mkpl/admin/review/product', headers=headers, json=data)
        return self.response

    def order_review(self, headers, size: int = 20, page: int = 1,
                     biz_type: str = 'seller', order_id: int = None,
                     order_time_from: int = None, order_time_to: int = None, product_id: int = None,
                     rating: list = None, rating_time_from: int = None, rating_time_to: int = None,
                     seller_id: int = None):
        """
         商家订单评论列表
        :param headers:sales header
        :param size:每页数据
        :param page:分页数
        :param biz_type:业务类型 默认seller
        :param order_id:订单号
        :param order_time_from:筛选订单开始时间戳
        :param order_time_to:筛选订单结束时间戳
        :param product_id:商品id
        :param rating:用户评分 Positive rating: [5] Neutral：[3, 4] Negative：[1, 2]
        :param rating_time_from:用户评分筛选开始时间戳
        :param rating_time_to:用户评分筛选结束时间戳
        :param seller_id:商家id
        :return:符合条件的review
        """

        param = {
            "biz_type": biz_type,
            "order_id": order_id,
            "order_time_from": order_time_from,
            "order_time_to": order_time_to,
            "product_id": product_id,
            "rating": rating,
            "rating_time_from": rating_time_from,
            "rating_time_to": rating_time_to,
            "seller_id": seller_id
        }
        # None不传参
        param = {k: v for k, v in param.items() if v is not None}
        data = {
            "size": size,
            "page": page,
            "param": param
        }
        # json str传参
        self.post(url='/central/mkpl/admin/review/order', headers=headers, json=data)
        return self.response

    def review_detail(self, headers, rating_id: int):
        """
        订单评论详情
        :param headers:
        :param rating_id: 订单评分id
        :return:
        """
        params = {'rating_id': rating_id}
        self.get(headers=headers, url='/central/mkpl/admin/review/order/detail', params=params)
        return self.response

    def sales_report_summary(self, headers, seller_id: int, start_time: int, end_time: int, biz_type: str = ''):
        """
        seller 售卖报告汇总

        :param biz_type: GLOBAL+ :"" GLOBAL FBW:mkpl_fbw LOCAL:fbw
        :param seller_id:
        :param start_time:
        :param end_time:
        :return:
        """
        url = f"/central/mkpl/admin/statistics/{biz_type}/merchant/{seller_id}/order/summary"
        params = {
            "start_time": start_time,
            "end_time": end_time
        }
        self.get(headers=headers, url=url, params=params)
        return self.response

    def sales_report_detail(self, headers, seller_id: int, list_type: str, start_time: int, end_time: int,
                            biz_type: str = '',
                            limit: int = 20, page: int = 1):
        """
        seller 售卖报告商品、订单tab详情
        :param seller_id:
        :param list_type:By Product：product | By Date ：order
        :param start_time:
        :param end_time:
        :param biz_type: GLOBAL+ :"" GLOBAL FBW:mkpl_fbw LOCAL:fbw
        :param limit:
        :param page:
        :return:
        """
        url: str = f"/central/mkpl/admin/statistics/{biz_type}/merchant/{seller_id}/{list_type}/list"
        params = {
            "limit": limit,
            "page": page,
            "start_time": start_time,
            "end_time": end_time
        }
        self.get(headers=headers, url=url, params=params)
        return self.response

    def shipping_template_list(self, headers, seller_id: int):
        """
        运费模板列表
        :param seller_id:
        :return:
        """
        url: str = f"/central/mkpl/admin/sellers/{seller_id}/shipping_template/list"

        self.get(headers=headers, url=url)
        return self.response

    def shipping_template_detail(self, headers, template_id: int):
        """
        运费模板详情
        :param template_id: 运费模板id
        :return:
        """
        url: str = f"/central/mkpl/admin/shipping_template/{template_id}"
        self.get(headers=headers, url=url)
        return self.response

    def shipping_template_products(self, headers, template_id: int, page: int = 1, limit: int = 10):
        """
        运费模板关联商品
        :param template_id:模板id
        :param page:分页数
        :param limit:分页大小
        :return:
        """
        url: str = f"/central/mkpl/admin/shipping_template/{template_id}/products"
        # 设置请求体，默认参数已在函数定义中设定
        payload = {
            "page": page,
            "limit": limit
        }

        # 发送 POST 请求
        self.post(headers=headers, url=url, json=payload)
        return self.response


class ProductsMgmt(weeeTest.TestCase):
    """
     商品管理
    """

    def audit_records(self, headers, size: int = 20, page: int = 1, biz_type: str = "seller", filter_to_do: str = False,
                      keyword: str = None,
                      product_ids: str = None, audit_progress: str = None,
                      audit_status: str = None,
                      vendor_id: str = None,
                      account_manager_id: str = None,
                      update_time_start: str = None,
                      update_time_end: str = None
                      ):
        """
        审核商品列表-New Product Review
        @param headers: central login headers
        @param size:列表每页数量 默认20/page
        @param page:结果分页
        @param biz_type: "seller", "mkpl_fbw", "fbw"
        @param filter_to_do: filter To Do False/True
        @param keyword: filter Keywords
        @param product_ids: filter SKU ID
        @param audit_progress: filter Audit Progress "1"、"2"、"3"
        @param audit_status: filter Audit status  "E"、 "A"、"C"
        @param vendor_id: filter Seller
        @param account_manager_id: filter Seller
        @param update_time_start: filter Update Time开始时间
        @param update_time_end: filter Update Time结束时间
        @return:审核商品列表
        """
        data = {
            "biz_type": biz_type,
            "page": page,
            "size": size,
            "venderId": vendor_id,
            "audit_status": audit_status,
            "keyword": keyword,
            "product_ids": product_ids,
            "audit_progress": audit_progress,
            "filter_to_do": filter_to_do,
            "account_manager_id": account_manager_id,
            "update_time_start": update_time_start,
            "update_time_end": update_time_end
        }
        self.post(url='/central/mkpl/admin/product/audit/records', headers=headers, json=data)
        return self.response

    def product_audit_count(self, headers, biz_type: str = "seller"):
        """
        审核列表各个tab下的商品数量
        @param headers: central login headers
        @param biz_type: 商家类型 "seller", "mkpl_fbw", "fbw"
        @return:
        """

        data = {"biz_type": biz_type}
        self.get(url='/central/mkpl/admin/product/audit/count', headers=headers, params=data)
        return self.response

    def product_audit_progress(self, headers, product_id: str = "2119285"):
        """
        商品详情的商品审核进度及审核人员
        @param headers:central login headers
        @param product_id: 商品ID
        @return:
        """
        data = {"product_id": product_id}
        self.get(url='/central/mkpl/admin/product/audit/progress', headers=headers, params=data)
        return self.response

    def product_audit_log(self, headers, product_id: str = "2119285"):
        """
        商品详情的商品审核log
        @param headers:central login headers
        @param product_id: 商品ID
        @return:
        """

        data = {"product_id": product_id}
        self.post(url=f'/central/mkpl/admin/product/{product_id}/audit', headers=headers, json=data)
        return self.response

    def admin_products(self, headers, size: int = 20, page: int = 1, biz_type: str = "seller", web_status: str = None,
                       audit_status_list: str = None, vender_id: str = None,
                       name: str = None, manufacturer_part_num: str = None,
                       product_id: str = None, catalogue_num: str = None,
                       brand_key: str = None,
                       upc_code: str = None, product_area: str = None,
                       rec_create_id: str = None,
                       rec_create_time_start: int = None,
                       rec_create_time_end: int = None
                       ):
        """
        商品列表-Product List
        @param headers: central login headers
        @param size:列表每页数量 默认20/page
        @param page:结果分页
        @param biz_type: "seller", "mkpl_fbw", "fbw"
        @param web_status: A、X、R,
        @param audit_status_list:["D"]
        @param vender_id:filter Seller
        @param name:filter Name
        @param manufacturer_part_num:filter Seller SKU
        @param product_id:filter Weee SKU
        @param catalogue_num:filter Category
        @param brand_key:filter Brand
        @param upc_code:filter UPC
        @param product_area:filter Product Origin
        @param rec_create_id:
        @param rec_create_time_start:filter SKU Create Time开始时间戳
        @param rec_create_time_end:filter SKU Create Time 结束时间戳
        @return:商品列表-Product List
        """

        data = {
            "page": page,
            "size": size,
            "name": name,
            "manufacturer_part_num": manufacturer_part_num,
            "web_status": web_status, "audit_status_list": audit_status_list,
            "product_id": product_id,
            "catalogue_num": catalogue_num,
            "brand_key": brand_key,
            "vender_id": vender_id,
            "rec_create_id": rec_create_id,
            "biz_type": biz_type,
            "upc_code": upc_code,
            "product_area": product_area,
            "rec_create_time_start": rec_create_time_start,
            "rec_create_time_end": rec_create_time_end
        }
        self.post(url='/central/mkpl/admin/products', headers=headers, json=data)
        return self.response

    def global_fbw_products_list(self, headers, size: int = 20, page: int = 1, biz_type: str = "seller",
                                 web_status: str = None,
                                 audit_status_list: str = None, vender_id: str = None,
                                 name: str = None, manufacturer_part_num: str = None,
                                 product_id: str = None, catalogue_num: str = None,
                                 brand_key: str = None,
                                 upc_code: str = None, product_area: str = None,
                                 rec_create_id: str = None,
                                 rec_create_time_start: int = None,
                                 rec_create_time_end: int = None
                                 ):
        """
        商品列表-Product List
        @param headers: central login headers
        @param size:列表每页数量 默认20/page
        @param page:结果分页
        @param biz_type: "seller", "mkpl_fbw", "fbw"
        @param web_status: A、X、R,
        @param audit_status_list:["D"]
        @param vender_id:filter Seller
        @param name:filter Name
        @param manufacturer_part_num:filter Seller SKU
        @param product_id:filter Weee SKU
        @param catalogue_num:filter Category
        @param brand_key:filter Brand
        @param upc_code:filter UPC
        @param product_area:filter Product Origin
        @param rec_create_id:
        @param rec_create_time_start:filter SKU Create Time开始时间戳
        @param rec_create_time_end:filter SKU Create Time 结束时间戳
        @return:商品列表-Product List
        """

        data = {
            "page": page,
            "size": size,
            "name": name,
            "manufacturer_part_num": manufacturer_part_num,
            "web_status": web_status, "audit_status_list": audit_status_list,
            "product_id": product_id,
            "catalogue_num": catalogue_num,
            "brand_key": brand_key,
            "vender_id": vender_id,
            "rec_create_id": rec_create_id,
            "biz_type": biz_type,
            "upc_code": upc_code,
            "product_area": product_area,
            "rec_create_time_start": rec_create_time_start,
            "rec_create_time_end": rec_create_time_end
        }
        self.post(url='/central/mkpl/admin/global/fbw/products', headers=headers, json=data)
        return self.response

    def product_list(self, headers, rec_create_id: str, biz_type: str, page: int = 1, size: int = 20,
                     vender_id: str = "",
                     name=None, manufacturer_part_num=None, product_id=None, catalogue_num=None, brand_key=None,
                     upc_code=None, product_area=None, web_status=None, rec_create_time_start=None,
                     rec_create_time_end=None):
        """
        MKPL商品列表
        :param rec_create_id:
        :param biz_type:
        :param page:
        :param size:
        :param vender_id:
        :param name:
        :param manufacturer_part_num:
        :param product_id:
        :param catalogue_num:
        :param brand_key:
        :param upc_code:
        :param product_area:
        :param web_status:
        :param rec_create_time_start:
        :param rec_create_time_end:
        :return:
        """

        # 构建参数字典，包括必填和可选参数
        params = {
            "page": page,
            "size": size,
            "vender_id": vender_id,
            "rec_create_id": rec_create_id,
            "biz_type": biz_type,
            "name": name,
            "manufacturer_part_num": manufacturer_part_num,
            "product_id": product_id,
            "catalogue_num": catalogue_num,
            "brand_key": brand_key,
            "upc_code": upc_code,
            "product_area": product_area,
            "web_status": web_status,
            "rec_create_time_start": rec_create_time_start,
            "rec_create_time_end": rec_create_time_end
        }

        # 移除字典中的 None 值，这些通常是未提供的可选参数
        params = {k: v for k, v in params.items() if v is not None}
        url: str = f"/central/mkpl/admin/products"
        self.post(headers=headers, url=url, json=params)
        return self.response

    def product_detail(self, headers, product_id=None, biz_type=None):
        """
        商品详情页
        @param headers:
        @param product_id:
        @param biz_type:
        @return:
        """

        params = {
            "product_id": product_id,
            "biz_type": biz_type
        }

        self.get(url='/central/mkpl/admin/product', headers=headers, params=params)
        return self.response


class LocalFBW(weeeTest.TestCase):
    def seller_open_regions(self, headers, seller_id: int):
        """
        local fbw商家开通的region信息
        :param seller_id:
        :return:
        """
        # 构建请求的 URL
        url = f"/central/mkpl/admin/fbw/sellers/{seller_id}/regions/opened"

        # 发送 GET 请求
        self.get(headers=headers, url=url)
        return self.response

    def seller_fulfillment_status(self, headers, seller_id: int, region_id: int, start_date: str, end_date: str):
        """
        local fbw 商家14天内截单状态
        :param headers:
        :param seller_id:
        :param region_id:
        :param start_date:
        :param end_date:
        :return:
        """
        # 构建请求的 URL
        url: str = f"/central/mkpl/admin/fbw/sellers/{seller_id}/regions/{region_id}/cutoff/status"

        # 构建查询参数
        query_params = {
            "start": start_date,
            "end": end_date
        }

        # 发送 GET 请求
        self.get(headers=headers, url=url, params=query_params)
        return self.response

    def product_inventory_plan(self, headers, seller_id: int, region_id: int, product_id: int):
        """
        local fbw 商品库存计划
        :param headers:
        :param seller_id:
        :param region_id:
        :param product_id:
        :return:
        """
        # 构建请求的 URL
        url: str = f"/central/mkpl/admin/fbw/sellers/{seller_id}/regions/{region_id}/products/{product_id}/inventory_plan"

        # 发送 GET 请求
        response = self.get(headers=headers, url=url)
        return self.response

    def seller_region_inventory(self, headers, seller_id: int, region_id: int, page: int = 1, size: int = 50,
                                start_date: str = datetime.today().strftime('%Y-%m-%d'),
                                end_date: str = ((datetime.today() + timedelta(days=6)).strftime('%Y-%m-%d'))):
        """
        指定商家 region local fbw 库存。

        :param headers: 请求头部，包含认证和其他信息。
        :param seller_id: 卖家 ID。
        :param region_id: 地区 ID。
        :param page: 请求的页码。
        :param size: 每页的大小。
        :param start_date: 开始日期，格式为 'YYYY-MM-DD'。
        :param end_date: 结束日期，格式为 'YYYY-MM-DD'。
        :return: 接口的响应对象。
        """
        # 构造请求的 URL
        url = f"/central/mkpl/admin/fbw/sellers/{seller_id}/regions/{region_id}/inventory"

        # 请求体参数
        payload = {
            "page": page,
            "size": size,
            "start": start_date,
            "end": end_date
        }

        # 发送 POST 请求
        response = self.post(url, headers=headers, json=payload)

        return self.response

    def fbw_sales_summary(self, headers, seller_id: int, region_id: int, summary_date: str, step: int = 1):
        """
        local fbw 销量summary
        :param headers:
        :param seller_id:
        :param region_id:
        :param summary_date:
        :param step:
        :return:
        """
        # 构建请求的 URL
        url: str = f"/central/mkpl/admin/fbw/sellers/{seller_id}/regions/{region_id}/summary/products"
        # 构建查询参数
        query_params = {
            "date": summary_date,
            "step": step
        }

        # 发送 GET 请求
        self.get(headers=headers, url=url, params=query_params)
        return self.response

    def discount_list(self, headers, sales_org_id, seller_id=None, status=None, product_ids=None, start_time=None,
                      end_time=None):
        """
        local fbw discount 列表
        :param headers:
        :param sales_org_id:
        :param seller_id:
        :param status:
        :param product_ids:
        :param start_time:
        :param end_time:
        :return:
        """
        # 构建请求的 URL
        url = "/central/mkpl/admin/sellers/fbw/lightning/discounts/list"

        # 构建查询参数字典
        params = {
            'sales_org_id': sales_org_id
        }
        if seller_id:
            params['seller_id'] = seller_id
        if status:
            params['status'] = status
        if start_time:
            params['start_time'] = start_time
        if status:
            params['end_time'] = end_time

        # 如果有产品 ID 列表，添加到查询参数
        if product_ids:
            for index, product_id in enumerate(product_ids):
                params[f'product_ids[{index}]'] = product_id

        # 发送 GET 请求
        self.post(headers=headers, url=url, json=params)
        return self.response

    def get_discount_detail(self, headers, discount_id):
        # 构建请求的 URL
        url = f"/central/mkpl/admin/sellers/fbw/lightning/discounts/{discount_id}"

        # 发送 GET 请求
        self.get(headers=headers, url=url)
        return self.response

    def local_fbw_return_list(self, headers, seller_id, start_time=None, end_time=None, size=10, page=1):
        """
        local fbw 退货管理列表

        :param headers:
        :param seller_id:
        :param start_time:
        :param end_time:
        :param size:
        :param page:
        :return:
        """

        url = "/central/mkpl/admin/refund/seller/product"

        # 构建请求体
        data = {
            "size": size,
            "page": page,
            "param": {
                "seller_id": seller_id,
                "start_time": start_time,
                "end_time": end_time
            }
        }

        # 移除 None 值，保持请求体的整洁
        data['param'] = {k: v for k, v in data['param'].items() if v is not None}

        # 发送 POST 请求
        self.post(headers=headers, url=url, json=data)
        return self.response

    def seller_forecast_performance(self, headers, seller_id, page=1, size=20, product_ids=None, delivery_date=None,
                                    product_name=None):
        """
        local fbw 预测dashboard
        :param headers:
        :param seller_id:
        :param page:
        :param size:
        :param product_ids:
        :param delivery_date:
        :param product_name:
        :return:
        """
        # 构建请求的 URL
        url = f"/central/mkpl/admin/fbw/sellers/{seller_id}/forecast/page"

        # 构建请求体
        data = {
            "page": page,
            "size": size
        }

        # 只有在提供时才添加可选参数
        if product_ids:
            data["product_ids"] = product_ids
        if delivery_date:
            data["delivery_date"] = delivery_date
        if product_name:
            data["product_name"] = product_name

        # 发送 POST 请求
        self.post(headers=headers, url=url, json=data)
        return self.response


class GlobalFBW(weeeTest.TestCase):

    def global_fbw_sellers_pos_list(self, headers, page_number=1, page_size=20, po_ids=None, product_ids=None,
                                    vendor_id=None, status=None):
        """
        Global FBW IO单列表
        @param headers:
        @param page_number:
        @param page_size:
        @param po_ids:
        @param product_ids:
        @return:
        """

        data = {
            "page_number": page_number,
            "page_size": page_size,
            "po_ids": po_ids,
            "product_ids": product_ids,
            "vendor_id": vendor_id,
            "status": status
        }

        self.post(url='/central/mkpl/admin/global/fbw/sellers/pos/list', headers=headers, json=data)
        return self.response

    def global_fbw_warehouse(self, headers):
        """
        LA&NY仓库信息接口
        @param headers:
        @return:
        """

        self.get(url='/central/mkpl/admin/global/fbw/warehouses', headers=headers)
        return self.response

    def gloabl_fbw_sellers_pos_detail(self, headers, seller_id, group_id=None):
        """
        IO单详情
        @param headers:
        @param group_id:
        @return:
        """

        params = {"group_id": group_id}
        self.get(url=f'/central/mkpl/admin/global/fbw/sellers/{seller_id}/pos/detail', headers=headers, params=params)
        return self.response

    def global_fbw_sellers_label_carton(self, headers, seller_id, po_id, sku_id):
        """
        Global FBW Lpn info
        @param headers:
        @param seller_id: 商家id
        @param po_id: IO单id
        @param sku: 商品id
        @return:
        """

        self.get(url=f'/central/mkpl/admin/global/fbw/sellers/{seller_id}/label/carton/pos/{po_id}/sku/{sku_id}',
                 headers=headers)
        return self.response

    def global_fbw_sellers_ros_list(self, headers, page: int = 1, size: int = 20, ro_ids: list = []):
        """
        IO Return list
        @param page:
        @param size:
        @param ro_ids:
        @return:
        """

        data = {
            "page": page,
            "size": size,
            "ro_ids": ro_ids
        }
        self.post(url='/central/mkpl/admin/global/fbw/sellers/ros/list', headers=headers, json=data)
        return self.response

    def global_fbw_sellers_ros_detail(self, headers, return_id: int = None):
        """
        io return详情
        @return:
        """
        self.get(url=f'/central/mkpl/admin/global/fbw/sellers/ros/{return_id}/detail', headers=headers)
        return self.response

    def global_fbw_inv_warehouse_product(self, headers, page=1, param={}, size=20):
        """
        Global FBW商品库存列表
        @param headers:
        @param page:
        @param param:
        @param size:
        @return:
        """

        data = {
            "page": page,
            "size": size,
            "param": param
        }
        self.post(url='/central/mkpl/admin/global/fbw/inv/warehouse/product', headers=headers, json=data)
        return self.response

    def global_fbw_inv_warehouse_product_detail(self, headers, product_id, seller_id):
        """
        Global FBW商品库存详情
        @param headers:
        @param product_id:
        @param seller_id:
        @return:
        """

        params = {
            "product_id": product_id,
            "seller_id": seller_id
        }
        self.get(url='/central/mkpl/admin/global/fbw/inv/warehouse/product/detail', headers=headers, params=params)
        return self.response

    def global_fbw_inv_warehouse_product_exp(self, headers, page=1, size=20, param=None):
        """
        商品保质期列表
        @param headers:
        @param page:
        @param size:
        @param param:
        @return:
        """

        data = {
            "page": page,
            "size": size,
            "param": param
        }

        self.post(url='/central/mkpl/admin/global/fbw/inv/warehouse/product/exp', headers=headers, json=data)
        return self.response

    def global_fbw_vas_list(self, headers, page=1, size=20, seller_id: int = None, vas_status: str = None):
        """
        增值服务费列表
        @param headers:
        @param page:
        @param size:
        @return:
        """

        data = {
            "page": page,
            "size": size,
            "seller_id": seller_id,
            "vas_status": vas_status
        }

        data = {k: v for k, v in data.items() if v is not None}
        self.post(url='/central/mkpl/admin/global/fbw/vas/list', headers=headers, json=data)
        return self.response

    def global_fbw_vas_detail(self, headers, vas_id):
        """
        增值服务费详情页
        @param headers:
        @param vas_id:
        @return:
        """

        self.get(url=f'/central/mkpl/admin/global/fbw/vas/detail/{vas_id}', headers=headers)
        return self.response


class OrderMgmt(weeeTest.TestCase):
    def abnormal_tracking(self, headers: Dict[str, str],
                          page: int = 0,
                          size: int = 20,
                          tracking_type: str = "all",
                          tracking_number: Optional[Union[str, List[str]]] = None,
                          order_id: Optional[Union[str, List[str]]] = None,
                          seller_id: Optional[Union[str, List[str]]] = None,
                          order_create_from: Optional[int] = None,
                          order_create_to: Optional[int] = None):
        """

        :param headers:请求头
        :param page:页码，默认为开始0
        :param size:每页大小，默认为20
        :param tracking_type: tab
        :param tracking_number:物流号
        :param order_id:订单号
        :param seller_id:商家id
        :param order_create_from:订单创建起始时间（时间戳）
        :param order_create_to:订单创建结束时间（时间戳）
        :return:
        """
        url = "/central/mkpl/admin/seller/merchants/abnormal/trackings"

        params = {
            "page": page,
            "size": size,
            "type": tracking_type
        }

        # 如果传入 order_create_from 和 order_create_to，则忽略其他参数
        if order_create_from is not None and order_create_to is not None:
            params["order_create_from"] = order_create_from
            params["order_create_to"] = order_create_to
        else:
            if tracking_number:
                params["tracking_number"] = ','.join(tracking_number) if isinstance(tracking_number,
                                                                                    list) else tracking_number
            if order_id:
                params["order_id"] = ','.join(order_id) if isinstance(order_id, list) else order_id
            if seller_id:
                params["seller_id"] = ','.join(seller_id) if isinstance(seller_id, list) else seller_id

        self.get(url, headers=headers, params=params)
        return self.response


class Promotions(weeeTest.TestCase):

    def admin_promotion_page(self, headers, order_column: str = "ps_id", order_rule: str = "desc", page_size: int = 20,
                             start_column: int = 10, biz_type: str = None, type: str = None,
                             ):
        """
        获取mkpl促销列表1
        @param headers: central login headers
        @param biz_type: 商家类型 "seller", "mkpl_fbw", "fbw"
        @param vendor_id: 商家ID
        @param status: 活动状态
        @param type: 活动类型
        @param rec_creator_id:创建活动用户id
        @return:
        """

        data = {"pageSize": page_size,
                "startColumn": start_column,
                "type": "",
                "status": "",
                "biz_type": "",
                "order": {"orderColumn": order_column, "orderRule": order_rule},
                "rec_creator_id": "", "vendor_id": ""
                }

        data2 = {"pageSize": 20, "startColumn": 0, "type": "", "status": "", "biz_type": "seller",
                 "order": {"orderColumn": "ps_id", "orderRule": "desc"}}

        self.post(url='/central/mkpl/admin/promotion/page', headers=headers, json=data2)
        return self.response

    def mkpl_admin_promotion_detail(self, headers, ps_id: int = 10211):
        """

        获取mkpl促销活动详情页
        @param headers: central login headers
        @param biz_type: 商家类型 "seller", "mkpl_fbw", "fbw"
        @param vendor_id: 商家ID
        @param status: 活动状态
        @param type: 活动类型
        @param rec_creator_id:创建活动用户id
        @return:
        """
        data = None

        self.get(url="/central/mkpl/admin/promotion/" + str(ps_id), headers=headers, params=data)
        return self.response

    def admin_global_plus_discounts(self, headers: Dict[str, str],
                                    page: int = 1,
                                    size: int = 20,
                                    seller_id: Optional[int] = None,
                                    start_time: Optional[int] = None,
                                    end_time: Optional[int] = None,
                                    rec_creator_id: Optional[int] = None,
                                    type: Optional[str] = None,
                                    title: Optional[str] = None,
                                    id: Optional[int] = None,
                                    product_ids: Optional[Union[int, List[int]]] = None
                                    ):
        """
        获取mkpl global+折扣列表
        :param headers: 请求头部
        :param page: 请求的页码，默认为 1。
        :param size: 分页大小，默认为 20。
        :param seller_id: 商家 ID。
        :param start_time: 折扣开始时间戳。
        :param end_time: 折扣结束时间戳。
        :param rec_creator_id: 商家account manager
        :param type: 折扣类型
        :param title: 折扣标题
        :param id: 折扣 ID
        :param product_ids: 产品 ID 列表，可以是单个 ID 或多个 ID。
        :return: 符合条件的折扣列表
        """

        params = {
            "page": page,
            "size": size,
            "seller_id": seller_id,
            "start_time": start_time,
            "end_time": end_time,
            "rec_creator_id": rec_creator_id,
            "type": type,
            "title": title,
            "id": id,
            # 传入多个商品列表 将 product_ids 转换为逗号分隔的字符串
            "product_ids": ','.join(map(str, product_ids)) if isinstance(product_ids, list) else product_ids
        }
        # 移除参数中的 None 值不传
        params = {k: v for k, v in params.items() if v is not None}

        self.get(url='/central/mkpl/admin/vendors/discounts', headers=headers, params=params)
        return self.response

    def admin_global_fbw_discounts(self, headers: Dict[str, str],
                                   page: int = 1,
                                   size: int = 20,
                                   seller_id: Optional[int] = None,
                                   start_time: Optional[int] = None,
                                   end_time: Optional[int] = None,
                                   rec_creator_id: Optional[int] = None,
                                   type: Optional[str] = None,
                                   title: Optional[str] = None,
                                   id: Optional[int] = None,
                                   product_ids: Optional[Union[int, List[int]]] = None
                                   ):
        """
        获取mkpl global fbw折扣列表
        :param headers: 请求头部
        :param page: 请求的页码，默认为 1。
        :param size: 分页大小，默认为 20。
        :param seller_id: 商家 ID。
        :param start_time: 折扣开始时间戳。
        :param end_time: 折扣结束时间戳。
        :param rec_creator_id: 商家account manager
        :param type: 折扣类型
        :param title: 折扣标题
        :param id: 折扣 ID
        :param product_ids: 产品 ID 列表，可以是单个 ID 或多个 ID。
        :return: 符合条件的折扣列表
        """

        params = {
            "page": page,
            "size": size,
            "seller_id": seller_id,
            "start_time": start_time,
            "end_time": end_time,
            "rec_creator_id": rec_creator_id,
            "type": type,
            "title": title,
            "id": id,
            # 传入多个商品列表 将 product_ids 转换为逗号分隔的字符串
            "product_ids": ','.join(map(str, product_ids)) if isinstance(product_ids, list) else product_ids
        }
        # 移除参数中的 None 值不传
        params = {k: v for k, v in params.items() if v is not None}

        self.get(url='/central/mkpl/admin/sellers/mkpl_fbw/discounts', headers=headers, params=params)
        return self.response

    def admin_local_fbw_discounts(self, headers: Dict[str, str],
                                  page: int = 1,
                                  size: int = 20,
                                  seller_id: Optional[int] = None,
                                  start_time: Optional[int] = None,
                                  end_time: Optional[int] = None,
                                  rec_creator_id: Optional[int] = None,
                                  type: Optional[str] = None,
                                  title: Optional[str] = None,
                                  id: Optional[int] = None,
                                  product_ids: Optional[Union[int, List[int]]] = None
                                  ):
        """
        获取mkpl local fbw折扣列表
        :param headers: 请求头部
        :param page: 请求的页码，默认为 1。
        :param size: 分页大小，默认为 20。
        :param seller_id: 商家 ID。
        :param start_time: 折扣开始时间戳。
        :param end_time: 折扣结束时间戳。
        :param rec_creator_id: 商家account manager
        :param type: 折扣类型
        :param title: 折扣标题
        :param id: 折扣 ID
        :param product_ids: 产品 ID 列表，可以是单个 ID 或多个 ID。
        :return: 符合条件的折扣列表
        """

        params = {
            "page": page,
            "size": size,
            "seller_id": seller_id,
            "start_time": start_time,
            "end_time": end_time,
            "rec_creator_id": rec_creator_id,
            "type": type,
            "title": title,
            "id": id,
            # 传入多个商品列表 将 product_ids 转换为逗号分隔的字符串
            "product_ids": ','.join(map(str, product_ids)) if isinstance(product_ids, list) else product_ids
        }
        # 移除参数中的 None 值不传
        params = {k: v for k, v in params.items() if v is not None}

        self.get(url='/central/mkpl/admin/sellers/fbw/discounts', headers=headers, params=params)
        return self.response

    def promotion_coupon_page(self, headers, page_no: int = 1, page_size: int = 20, group_id: int = 200,
                              seller_id: str = None, status: str = None,
                              rec_creator_id: str = None,
                              start_time: str = None, end_time: str = None):
        """
        Sales MKPL 促销 优惠券管理列表
        :param headers: 消息头
        :param page_no: 页码
        :param page_size: 分页大小默认20
        :param group_id: Coupon group id
        :param seller_id: 商家id
        :param status: 优惠券状态
        :param rec_creator_id: seller BD account manager
        :param start_time:创建时间过滤
        :param end_time:结束时间过滤
        :return:符合条件的Coupon list
        """
        data = {"page_no": page_no, "page_size": page_size, "group_id": group_id, "seller_id": seller_id,
                "status": status, "start_time": start_time,
                "end_time": end_time, "rec_creator_id": rec_creator_id}

        self.post(url='/central/mkpl/admin/promotion/coupon/page', headers=headers, json=data)
        return self.response

    def mkpl_coupon_usage(self, headers: Dict[str, str],
                          coupon_id: int,
                          start_time: int = None,
                          end_time: int = int(time())):
        """
        Sales MKPL获取优惠券领取使用详情。
        :param headers: 请求头
        :param coupon_id: 优惠券的 ID
        :param start_time: 查询的开始时间戳，默认为 end_time 90天之前。
        :param end_time: 查询的结束时间戳，默认为当前时间戳。
        :return: Coupon领取使用详情
        """
        # 如果没有指定 start_time，则将其设置为 end_time 90天之前
        if start_time is None:
            start_time = int((datetime.fromtimestamp(end_time) - timedelta(days=90)).timestamp())

        # 构造请求 URL
        url = f"/central/mkpl/admin/promotion/coupon/{coupon_id}"
        params = {
            "start_time": start_time,
            "end_time": end_time
        }

        # 发送 GET 请求
        self.get(url, headers=headers, params=params)

        return self.response

    def mkpl_coupon_plan(self, headers, group_id: int, remark: str, type: str,
                         apply_relative_time: str, direct_skus: int, vendor_id: str, order_amount_limit: int,
                         issue_quantity: int = None,
                         amount: float = None, percentage: float = None, apply_quantity: int = None):
        data = {"group_id": group_id,
                # 206:普通无门槛满减卷(December Event Coupon)，204:November Lottery Coupon,210:大奖Free Giveaway Coupon，无门槛100%
                "remark": remark,
                "type": type,  # "Z"：满折,"D":满减
                "discount": [{"amount": amount, "percentage": percentage, "order_amount_limit": order_amount_limit}],
                "issue_quantity": issue_quantity,
                "apply_relative_time": apply_relative_time,  # 领取优惠券后的有效时间days
                "direct_skus": [direct_skus],  # 指定商品
                "vendor_id": vendor_id, "apply_quantity": apply_quantity}

        self.post(url='/central/promotion/mkpl/coupon/plan', headers=headers, json=data)
        return self.response

    def coupon_plan_update(self, headers, id: str, status: str):
        data = {"id": id, "status": status}

        self.post(url='/central/promotion/coupon_plan/update', headers=headers, json=data)
        return self.response

    def raffle_refresh_prize(self, headers, raffle_id: int, pool_id: int, weight: int, quantity: int,
                             refer_type: str, refer_id: int, price: str, vendor_id: str, id: str = None):
        data = {
            "id": id,
            "raffle_id": raffle_id,
            "pool_id": pool_id,
            "weight": weight,
            "quantity": quantity,
            "refer_type": refer_type,  # "coupon","coupon_gift"
            "refer_id": refer_id,
            "price": price,
            "vendor_id": vendor_id
        }

        self.post(url='/central/promotion/raffle/refresh/prize', headers=headers, json=data)
        return self.response

    def job_campaigns_process(self, headers):
        """
        Campaign 活动状态更新Job
        @param headers:
        @return:
        """
        self.get(url='/central/mkpl/job/campaigns/process', headers=headers)
        return self.response

    def admin_campaigns(self, headers, biz_type: str = "seller", seller_id: str = None, status_list: str = None,
                        campaign_type: str = None, name: str = None):
        """
        Campaign列表
        @param headers: central login headers
        @param biz_type: 商家类型 "seller", "mkpl_fbw", "fbw"
        @param seller_id: 商家ID
        @param status_list: 活动状态
        @param campaign_type: 活动类型
        @param name:
        @return:
        """

        data = {"biz_type": biz_type,
                "name": name,
                "campaign_type": campaign_type,
                "seller_id": seller_id}

        # 如果 status_list 是字符串或列表，则添加到请求参数中
        if status_list:
            if isinstance(status_list, list):
                # 为每个状态创建一个不带索引的status_list参数
                data['status_list'] = status_list
            else:
                data["status_list"] = [status_list]

        self.get(url='/central/mkpl/admin/campaigns', headers=headers, params=data)
        return self.response

    def admin_campaigns_calendar(self, headers, biz_type: str = "seller", campaign_start: str = "1722495600",
                                 campaign_end: str = "1754031599", seller_id: str = None,
                                 campaign_type: str = None, name: str = None):
        """
        Campaign 日历12个月
        @param headers:central login headers
        @param biz_type:商家类型 "seller", "mkpl_fbw", "fbw"
        @param campaign_start:本月
        @param campaign_end: 本月+11个月
        @param seller_id: 商家ID
        @param campaign_type: 活动类型
        @return:
        """

        # 设置洛杉矶时区
        la_tz = pytz.timezone('America/Los_Angeles')
        # 获取当前洛杉矶时间
        current_date = datetime.now(la_tz)
        # 设置开始日期为当前月的第一天
        campaign_start = datetime(current_date.year, current_date.month, 1, tzinfo=la_tz)
        # 设置结束日期为当前月份+11个月的最后一天
        campaign_end = campaign_start + relativedelta(months=12) - relativedelta(days=1)

        # 将日期转换为时间戳
        campaign_start_timestamp = int(campaign_start.timestamp())
        campaign_end_timestamp = int(campaign_end.timestamp())

        data = {"biz_type": biz_type, "campaign_start": str(campaign_start_timestamp),
                "campaign_end": str(campaign_end_timestamp),
                "name": name,
                "campaign_type": campaign_type,
                "seller_id": seller_id}
        self.get(url='/central/mkpl/admin/campaigns/calendar', headers=headers, params=data)
        return self.response

    def admin_campaign_detail(self, headers, campaign_id: int = None):
        """
        campaign详情页
        :param headers:sales header
        :param campaign_id:campaign_id
        :return:
        """
        param = {}
        self.get(url=f'/central/mkpl/admin/campaigns/{campaign_id}', params=param, headers=headers)
        return self.response

    def admin_campaign_submissions(self, headers, campaign_id: int, review_status: str):
        """
        campaign的提报submissions的详情
        :param headers:sales header
        :param campaign_id:campaign_id
        :param review_status:I,A,R(In Review,Approved,Rejected)
        :return:
        """
        param = {"review_status": review_status}
        self.get(url=f'/central/mkpl/admin/campaigns/{campaign_id}/submissions', params=param, headers=headers)
        return self.response

    def submissions_status_count(self, headers, campaign_id: int):
        """
        submissions的详情,三种状态的提报数量
        :param headers:sales header
        :param campaign_id:campaign_id
        :param review_status:I,A,R(In Review,Approved,Rejected)
        :return:
        """
        param = {}
        self.get(url=f'/central/mkpl/admin/campaigns/{campaign_id}/submissions/status/count', params=param,
                 headers=headers)
        return self.response

    def global_fbw_lightning_sales_orgs(self, headers):
        """
        获取global fbw秒杀销售组织
        @param headers:
        @return:
        """
        self.get(url='/central/mkpl/admin/sellers/mkpl_fbw/lightning/sales_orgs', headers=headers)
        return self.response

    def global_fbw_lightning_discounts(self, headers, page: int = 1, limit: int = 20):
        """
        获取global fbw 秒杀列表
        @param headers:
        @return:
        """

        data = {
            "page": page,
            "limit": limit
        }

        self.post(url='/central/mkpl/admin/sellers/mkpl_fbw/lightning/discounts/list', headers=headers, json=data)
        return self.response


class NotificationHelp(weeeTest.TestCase):
    def admin_announcements(self,
                            headers: Dict[str, str],
                            page: int = 1,
                            size: int = 20,
                            biz_type: int = 0,
                            publish_status: str = 'D'
                            ):
        """
        MKPL系统通知
        :param headers:
        :param page:分页大小
        :param size:分页页码
        :param biz_type:Global+:0,Local :1,Global fbw:2
        :param publish_status:页面tab :Draft:D,Sent:P
        :return:
        """
        url = "/central/mkpl/admin/announcements"

        # 查询参数
        params = {
            "page": page,
            "size": size,
            "biz_type": biz_type,
            "publish_status": publish_status,
        }

        self.get(url, headers=headers, params=params)
        return self.response

    def admin_announcement_detail(self, headers: Dict[str, str], announcement_id: int):
        """
           sales mkpl 系统通知详情。

           :param headers: 请求头。
           :param announcement_id: 通知的 ID。
           :return: 通知详情
           """
        url = f"/central/mkpl/admin/announcements/{announcement_id}"

        # 发送 GET 请求
        self.get(url, headers=headers)

        return self.response

    def help_doc_list(self,
                      headers: Optional[Dict[str, str]] = None,
                      page_num: int = 1,
                      page_size: int = 20,
                      title: str = None,
                      biz_type: str = "mkpl"
                      ):
        url = "/central/common/help-doc/query/document/list"

        # 请求体参数
        payload = {
            "pageNum": page_num,
            "pageSize": page_size,
            "bizType": biz_type
        }
        # title参数是可选
        if title is not None:
            payload["title"] = title

        # 发送 POST 请求
        self.post(url, json=payload, headers=headers)
        return self.response


class CMS(weeeTest.TestCase):
    def seller_cms_activity_list(self, headers: Optional[Dict[str, str]] = None,
                                 page_type: int = 7,
                                 seller_type: str = "third_party",
                                 seller_id: Optional[int] = None,
                                 limit: int = 20,
                                 offset: int = 0,
                                 subject: Optional[int] = None):
        """
        MKPL CMS 活动页
        :param headers:
        :param page_type:
        :param seller_type:
        :param seller_id:
        :param limit:
        :param offset:
        :param subject:
        :return:
        """
        url = "/central/content/page/list"

        # 构建查询参数字典
        params = {
            "page_type": page_type,
            "limit": limit,
            "offset": offset
        }
        # 如果传了 seller_id 或 status，则添加到查询参数中
        if seller_id is not None:
            params["seller_id"] = seller_id
        if subject is not None:
            params["subject"] = subject
        # 发送 GET 请求
        self.get(url, headers=headers, params=params)
        return self.response

    def store_front_page(self, headers: Optional[Dict[str, str]] = None,
                         page_type: int = 6,
                         seller_id: Optional[int] = None,
                         limit: int = 10,
                         offset: int = 0,
                         status: Optional[str] = None):
        """
        商家页explore tab 配置
        :param headers:
        :param page_type:
        :param seller_id:
        :param limit:
        :param offset:
        :param status:
        :return:
        """
        url = "/central/content/page/list"
        params = {
            "page_type": page_type,
            "limit": limit,
            "offset": offset
        }
        # 如果提供了 seller_id 或 status，则添加到查询参数中
        if seller_id is not None:
            params["seller_id"] = seller_id
        if status is not None:
            params["status"] = status
        self.get(url, headers=headers, params=params)
        return self.response


class Payment(weeeTest.TestCase):
    def seller_settlement(self, headers: Dict[str, str],
                          vendor_id: str,
                          biz_type: str):
        """
        查询商家结算记录。

        :param headers: 请求头。
        :param vendor_id: seller ID，必填。
        :param biz_type: 业务类型，必填['MKPL','FBW']
        :return: Response 对象。
        """
        url = "/central/mkpl/admin/merchant/settlement"

        # 请求体，所有参数都是必填的
        data = {
            "vendor_id": vendor_id,
            "bizType": biz_type
        }
        # POST 请求
        self.post(url, headers=headers, json=data)
        return self.response

    def seller_settlement_detail(self, headers: Optional[Dict[str, str]],
                                 summary_id: int,
                                 vendor_id: int,
                                 biz_type: str = 'MKPL'):
        """
        获取商家结算详细信息。

        :param headers: 请求头部字典，可包含认证和其他信息。
        :param summary_id: 结算ID，必填。
        :param vendor_id: 商家ID，必填。
        :param biz_type: 业务类型，MKPL,FBW(local)
        :return: 结算详情。
        """
        url = "/central/mkpl/admin/merchant/settlement/detail"
        vendor_id = str(vendor_id)
        # 构建请求体
        data = {
            "summary_id": summary_id,
            "vendor_id": vendor_id,
            "bizType": biz_type
        }

        # POST 请求
        self.post(url, headers=headers, json=data)

        return self.response

    def mkpl_payment_status_list(self, headers: Optional[Dict[str, str]] = None,
                                 seller_id: Optional[int] = None,
                                 payment_status: Optional[str] = None,
                                 ebs_status: Optional[str] = None,
                                 biz_type: str = "MKPL",
                                 page_number: int = 1,
                                 page_size: int = 20):
        """
        MKPL结算看板

        :param headers: 请求头
        :param seller_id: 商家ID，可选参数
        :param payment_status: 支付状态，可选参数
        :param ebs_status: EBS 状态，可选参数
        :param biz_type: 业务类型，默认为 "MKPL"
        :param page_number: 页码，默认为 1
        :param page_size: 分页大小，默认为 20
        :return: 结算看板列表
            """
        url = "/central/mkpl/admin/merchant/settlement/ebs/status"

        # 构建请求体
        data = {
            "biz_type": biz_type,
            "page_number": page_number,
            "page_size": page_size
        }

        # 其他optional参数，有则添加到请求体中
        if seller_id is not None:
            seller_id = str(seller_id)
            data["seller_id"] = seller_id
        if payment_status is not None:
            data["payment_status"] = payment_status
        if ebs_status is not None:
            data["ebs_status"] = ebs_status

        # 发送 POST 请求
        self.post(url, headers=headers, json=data)
        return self.response


class BrandList(weeeTest.TestCase):
    def brand_list(self, headers: Dict[str, str],
                   start_column: int = 0,
                   page_size: int = 20,
                   keyword: Optional[str] = None,
                   status: Optional[str] = None,
                   origin_area_id: Optional[str] = None):
        """
        获取品牌列表

        :param headers: 请求头
        :param start_column: 开始列索引，用于分页，必填，默认为 0
        :param page_size: 页面大小，必填，默认为 20
        :param keyword: 搜索关键字，可选
        :param status: 品牌状态，可选
        :param origin_area_id: 原产地区域ID，可选
        :return: 品牌列表
        """
        url = "/central/im/brand/list"

        # 构建请求体，包含必填参数以及任何提供的可选参数
        data = {
            "startColumn": start_column,
            "pageSize": page_size
        }
        if keyword is not None:
            data["keyword"] = keyword
        if status is not None:
            data["status"] = status
        if origin_area_id is not None:
            data["origin_area_id"] = origin_area_id

        # POST 请求
        self.post(url, headers=headers, json=data)

        return self.response


class Filters(weeeTest.TestCase):
    """
    PORTAL页面下拉选项Filters
    """

    def product_first_catalogues(self, headers):
        """
        商品一级分类
        :param headers: sale header
        :return:
        """
        SellerMgmt().seller_optional_product_first_catalogues(headers=headers)
        return self.response

    def shipping_district(self, headers):
        """
        Shipping District发货国家列表
        :param headers: sale header
        :return:
        """
        SellerMgmt().seller_optional_shipping_district(headers=headers)
        return self.response

    def account_manager_list(self, headers):
        """
        Account Manager列表
        :param headers: sale header
        :return:
        """
        self.get(url='/central/mkpl/admin/seller/accountManager/list', headers=headers, params={})
        return self.response

    def vendor_market_list(self, headers, biz_type: str = "seller"):
        """
        下拉选择Seller
        :param headers: sale header
        :param biz_type: seller>所有商家; fbw>Local fbw商家; mkpl_fbw>Global fbw 商家
        :return:
        """
        data = {"biz_type": biz_type}
        self.get(url='/central/mkpl/admin/seller/vender/market/list', headers=headers, params=data)
        return self.response

    def catalogue_tree(self, headers):
        """
        下拉选择分类
        :param headers: sale header
        :return:
        """
        data = None
        self.post(url='/central/im/catalogue/tree', headers=headers, params=data)
        return self.response

    def brand_list(self, headers):
        """
        下拉选择品牌
        :param headers: sale header
        :return:
        """
        data = {}
        self.post(url='/central/im/brand/list', headers=headers, params=data)
        return self.response

    def product_area(self, headers):
        """
        下拉选择Product Origin
        :param headers: sale header
        :return:
        """
        self.get(url='/central/mkpl/product/area', headers=headers, params={})
        return self.response

    def seller_fbw_regions(self, headers):
        """
        下拉选择Product Region
        :param headers: sale header
        :return:
        """
        self.get(url='/central/mkpl/admin/seller/fbw/regions', headers=headers, params={})
        return self.response

    def admin_campaign_types(self, headers, biz_type: str = "seller"):
        """
        Campaign 类型 Filter
        @param headers:central login headers
        @param biz_type: campaign类型
        @return:
        """
        data = {"biz_type": biz_type}
        self.get(url='/central/mkpl/admin/campaign_types', headers=headers, json=data)
        return self.response

    def vender_list_by_tag_and_catalogue(self, headers, biz_type: str = "seller", tagId: int = None,
                                         category: int = None,
                                         ethnicity: str = None):
        """
        Private campaign -商家筛选器
        @param headers:sales header
        @param biz_type:seller>所有商家; fbw>Local fbw商家; mkpl_fbw>Global fbw 商家
        @param tagId:seller tag
        @param category:一级分类
        @param ethnicity:商家族裔
        @return:
        """
        param = {"biz_type": biz_type, "tagId": tagId, "category": category, "ethnicity": ethnicity}
        self.post(url='/central/mkpl/vender/listByTagAndCatalogue', json=param, headers=headers)
        return self.response

    def local_fbw_sales_orgs(self, headers):
        """
        local_fbw 秒杀 sales org 下拉选项filter
        :param headers:
        :return:
        """
        # 构建请求的 URL
        url = "/central/mkpl/admin/sellers/fbw/lightning/sales_orgs"
        # 发送 GET 请求
        self.get(headers=headers, url=url)
        return self.response

    def product_reject_reason(self, headers):
        """
        reject product 审核拒绝的原因下拉选项filter
        :param headers:
        :return:审核拒绝原因
        """
        # 构建请求的 URL
        url = "/central/mkpl/admin/product/reject/reason"
        # 发送 GET 请求
        self.get(headers=headers, url=url)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
    headers = {
        'accept': '*/*',
        'accept-language': 'zh-CN,zh;q=0.9',
        'authorization': 'Bearer eyJraWQiOiJiZDIwNTQ2ZC00ODlhLTQ1OTMtODgwOC1mZDI2YzhlZjZmN2YiLCJhbGciOiJSUzI1NiJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.i-Pfpw8taedTrQaa0JkNuM1XAEMDJpyTmjERkC2ENYre1eKhBIoMeaDpgZQ06s2cTeuu1WVzf705EdxnSkS-K41nQuKXecfQelxItsC4Dl_QjkNLLL9hv_aMZGmBW9nRUI5uzniHy_hOAP3wtxoW5VYq8C__Rl3lazH5pyDL1eo',
        'origin': 'https://sales.tb1.sayweee.net',
        'priority': 'u=1, i',
        'referer': 'https://sales.tb1.sayweee.net/',
        'sec-ch-ua': '"Not/A)Brand";v="8", "Chromium";v="126", "Google Chrome";v="126"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"macOS"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-site',
        'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/126.0.0.0 Safari/537.36',
        'x-requested-with': 'XMLHttpRequest',
        'Cookie': 'auth_token=****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************; b_cookie=28833; site_lang=zh; DELIVERY_DATE=2024-05-17; IS_LOGIN=1; NEW_SALES_ORG_ID=1; NEW_ZIP_CITY=Fremont; NEW_ZIP_CODE=94538; assets_cdn=%7B%220%22%3A0%2C%221%22%3A0%2C%222%22%3A0%2C%223%22%3A0%7D; deal_id=511838; ftu_cookie=1253865; order_token=YmNjOTQ4ZWQtMDVlMy00YjBlLWJmNTktMmUwMjViZWQyNjdh; pantry_fee=0; pantry_free_fee=35; shipping_fee=0; shipping_free_fee=0; site_lang=zh'
    }
    r = SellerMgmt().violation_mgmt_detail(headers=headers, id=65)
    print(r)
