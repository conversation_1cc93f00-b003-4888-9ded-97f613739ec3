# -*- coding: utf-8 -*-
"""
<AUTHOR>  suqin she
@Version        :  V1.0.0
------------------------------------
@Description    :  
@CreateTime     :  2024/3/27
@Software       :  PyCharm
------------------------------------
"""
import weeeTest
from weeeTest import log, weeeConfig


class CentralActivity(weeeTest.TestCase):

    def popup_list(self, headers, page_key: str = "page_home", popup_id: str = None,
                   sales_org: list = None, title: str = None,
                   data: str = None, in_user: str = None,
                   status: str = None, start_time: int = None,
                   end_time: int = None, pageSize: int = 20,
                   startColumn: int = 0,
                   time_zone: str = "America/New_York",
                   ):
        """
         查询pup列表页面
        """
        data = {
            "page_key": page_key,
            "sales_org": sales_org,
            "popup_id": popup_id,
            "title": title,
            "data": data,
            "in_user": in_user,
            "status": status,
            "start_time": start_time,
            "end_time": end_time,
            "pageSize": pageSize,
            "startColumn": startColumn,
            "time_zone": time_zone  # "America/Los_Angeles"
        }
        self.post(url='/central/activity/popup/list', headers=headers, json=data)
        return self.response

    def popup_status(self, headers, popup_id: int, status: str = None
                     ):
        """
         更新popup状态
        """
        data = {"popup_id": popup_id,
                "status": status}
        self.put(url='/central/activity/popup/status', headers=headers, json=data)
        return self.response

    def popup_page_list(self, headers):
        """
         popup 详情页面-page 列表
        """
        data = None
        self.get(url='/central/activity/popup/page/list', headers=headers, json=data)
        return self.response

    def popup_option(self, headers):
        """
         popup 详情页面-page option
        """
        data = None
        self.get(url='/central/activity/popup/option', headers=headers, json=data)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
