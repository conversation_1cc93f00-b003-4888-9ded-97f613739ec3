# -*- coding: utf-8 -*-
"""
<AUTHOR>  su<PERSON><PERSON> she
@Version        :  V1.0.0
------------------------------------
@Description    :  
@CreateTime     :  2024/3/27
@Software       :  PyCharm
------------------------------------
"""
import weeeTest
from weeeTest import log, weeeConfig


class CentralContent(weeeTest.TestCase):

    def content_page_list(self, headers, keyword: str = None, status: str = None,
                          in_user: int = None, edit_user: int = None,
                          in_start_dtm: str = None, in_end_dtm: str = None,
                          edit_start_dtm: str = None, edit_end_dtm: str = None,
                          is_show_homepage: int = 0, lang: str = None,
                          page_type: int = 7,
                          limit: int = 20, offset: int = 0,
                          ):
        """
         内容管理-活动页-查询单页面活动数据
        """
        data = {
            "page_type": page_type,  # page_type: 9 品牌页面， page_type: 7 cms单个页面
            "limit": limit,# 非必填：默认20
            "offset": offset,# 非必填：默认0
            "status": status,  # 非必填： "A","G","R"
            "in_user": in_user,# 非必填：
            "in_start_dtm": in_start_dtm,# 非必填：
            "in_end_dtm": in_end_dtm,# 非必填：
            "edit_user": edit_user,# 非必填：
            "edit_start_dtm": edit_start_dtm,# 非必填：
            "edit_end_dtm": edit_end_dtm,# 非必填：
            "keyword": keyword, # 非必填："test "
            "is_show_homepage": is_show_homepage, #非必填： 0,1
            "lang": lang,# 非必填："en"、"zh"、"ja"、"vi"、"zh-Hant"、"ko"
        }

        self.get(url='/central/content/page/list', headers=headers, params=data)
        return self.response

    def content_page_type_list(self, headers, page_type: int = 7):
        """
         内容管理-活动页-查询单页面活动filter数据
        """
        data = {
            "page_type": page_type,  # page_type: 9 品牌页面， page_type: 7 cms单个页面

        }

        self.get(url='/central/content/page/type/list', headers=headers, params=data)
        return self.response

    def content_routing_list(self, headers, keyword: str = None, status: str = None,
                             in_user: int = None, edit_user: int = None,
                             in_start_dtm: str = None, in_end_dtm: str = None,
                             edit_start_dtm: str = None, edit_end_dtm: str = None,
                             page_type: int = 7,
                             limit: int = 20, offset: int = 0,
                             ):
        """
         内容管理-活动页-查询多活动页面数据
        """
        data = {
            "page_type": page_type,
            "limit": limit, #非必填
            "offset": offset,#非必填
            "status": status,  #非必填： "A","R"
            "in_user": in_user,#非必填
            "in_start_dtm": in_start_dtm,#非必填
            "in_end_dtm": in_end_dtm,#非必填
            "edit_user": edit_user,#非必填
            "edit_start_dtm": edit_start_dtm,#非必填
            "edit_end_dtm": edit_end_dtm,#非必填
            "keyword": keyword,#非必填："test"
        }

        self.post(url='/central/content/page/routing/list', headers=headers, json=data)
        return self.response

    def content_routing_operate(self, headers, status: str = None, routing_id: int = 7):
        """
         内容管理-活动页-更新多活动页面数据
        """
        data = {
            "routing_id": routing_id,
            "status": status,

        }

        self.put(url='/central/content/page/routing/operate', headers=headers, json=data)
        return self.response

    def content_page_trash(self, headers, page_key: str = None, page_type: int = 9):
        """
         内容管理-活动页-offline牌页面数据
        """
        data = {
            "page_key": page_key,
            "page_type": page_type,

        }

        self.put(url='/central/content/page/trash', headers=headers, json=data)
        return self.response

    def content_data_object_list(self, headers, keyword: str = None, seller_id: int = 0,
                                 in_user: int = None, pageSize: int = 20,
                                 startColumn: int = 0, data_type: int = 1,

                                 ):
        """
         内容管理-数据源管理-数据源列表
        """
        data = {
            "seller_id": seller_id,
            "pageSize": pageSize,
            "startColumn": startColumn,
            "data_type": data_type,  # 1
            "in_user": in_user,
            "keyword": keyword, # 非必填 "test"
        }

        self.get(url='/central/content/dataobject/list', headers=headers, params=data)
        return self.response
