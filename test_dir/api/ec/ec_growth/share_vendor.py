import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath


class ShareVendor(weeeTest.TestCase):

    # 获取商家分享信息
    def share_vendor(self, headers, vendor_id, tab: str = None, biz_type: str = None):
        params = {
            "vendor_id": vendor_id,
            "tab": tab,
            "biz_type": biz_type
        }
        params = {k: v for k, v in params.items() if v is not None}
        self.get(url='/ec/growth/share/vendor', headers=headers, params=params)
        return self.response

if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
