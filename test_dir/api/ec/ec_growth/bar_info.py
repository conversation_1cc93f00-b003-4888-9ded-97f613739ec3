# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON> <PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  user_bak.py
@Description    :
@CreateTime     :  2023/5/30 11:06
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/30 11:06
"""

import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath


class BarInfo(weeeTest.TestCase):

    def bar_info(self, headers, channel_type: str = None, referral_type: str = None, source: str = None,
                 referrer_id: int = None):
        """bar_info"""
        data = {
            "channel_type": channel_type,
            "referral_type": referral_type,
            "source": source,
            "referrer_id": referrer_id
        }
        self.get(url='/ec/growth/bar/info', headers=headers, params=data)
        return self.response

    def get_safe_secure_tips(self, headers):
        """ 获取首页安全支付信息：安全可靠的支付"""
        data = None
        self.get(url='/ec/growth/message/get_safe_secure_tips', headers=headers, params=data)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
