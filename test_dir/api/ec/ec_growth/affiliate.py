# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  user_bak.py
@Description    :
@CreateTime     :  2023/5/30 11:06
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/30 11:06
"""

import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath


class Affiliate(weeeTest.TestCase):

    def share_list_create(self, headers, status: str = "A"):
        """# 创建/修改返利联盟分享清单"""
        data = {"title": "TEST 返利联盟分享清单title",
                "description": "返利联盟分享清单description",
                "status": status
                }
        self.post(url='/ec/growth/share_list/list/create_or_update', headers=headers, json=data)
        return self.response

    def share_list_detail(self, headers, share_list_id):
        """# 查看返利联盟分享清单详情"""
        data = None
        self.get(url='/ec/growth/share_list/detail/' + str(share_list_id), headers=headers, params=data)
        return self.response

    def share_list_product(self, headers, product_id: int = 94169):
        """# PDP利联盟分享清单all"""
        data = {
            "product_id": product_id
        }
        self.get(url='/ec/growth/share_list/get/all', headers=headers, json=data)
        return self.response

    def share_list_add_pdp_product(self, headers, share_list_ids: list, product_id: str = "94169"):
        """# PDP添加商品进返利联盟分享清单"""
        data = {"share_list_ids": share_list_ids, "product_id": product_id}
        self.post(url='/ec/growth/share_list/list/add/pdp/product', headers=headers, json=data)
        return self.response

    def share_list_add_product(self, headers, share_list_id, product_ids):
        """# 添加商品进返利联盟分享清单"""
        data = {"share_list_id": share_list_id, "product_ids": [product_ids]}

        self.post(url='/ec/growth/share_list/list/add/product', headers=headers, json=data)
        return self.response

    def share_list_delete_product(self, headers, share_list_id, product_ids):
        """# 删除返利联盟分享清单商品"""
        data = {"share_list_id": share_list_id, "product_ids": [product_ids]}
        self.post(url='/ec/growth/share_list/list/delete/product', headers=headers, json=data)
        return self.response

    def share_list_delete(self, headers, share_list_id):
        """# 删除返利联盟分享清单"""
        data = None
        self.delete(url='/ec/growth/share_list/list/delete/' + str(share_list_id), headers=headers, params=data)
        return self.response

    def affiliate_share(self, headers):
        """返利联盟清单分享"""
        data = None
        self.get(url='/ec/growth/share/affiliate/landing', headers=headers, params=data)
        return self.response

    def affiliate_sign_up(self, headers, email):
        """# 申请加入返利联盟"""
        data = {"email": email,
                "firstname": "autotest",
                "lastname": "autotest"
                }
        self.post(url='/ec/growth/affiliate/sign_up', headers=headers, json=data)
        return self.response

    def affiliate_get_referral_user(self, headers):
        """获取用戶邀請了哪些返利联盟用户"""
        data = None
        self.post(url='/ec/mkt/affiliate/get_referral_user', headers=headers, params=data)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
