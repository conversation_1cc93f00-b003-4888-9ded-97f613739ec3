import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath

"""
MKPL拼单分享单接口-0312

Charlie Chen
"""

class GroupOrderShare(weeeTest.TestCase):

    def share_group_order(self, headers, vendor_id, key):

        data = {
            "vendor_id": vendor_id,
            "key": key
        }

        self.get('/ec/growth/share/group-by', headers=headers, params=data)
        return self.response

if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'