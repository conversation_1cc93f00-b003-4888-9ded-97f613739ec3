import weeeTest
from weeeTest import weeeConfig
from test_dir.api.ec.ec_customer.login_rest.email_login import EmailLogin


class GetGlobalPlusBanner(weeeTest.TestCase):

    # 获取waterfall的竖着的Banner
    def get_global_plus_banner(self, headers):
        data = {"banner_group_key": "global_plus_banner_group_A"}
        self.get(url='/ec/growth/banner/get_global_plus_banner', headers=headers, params=data)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
