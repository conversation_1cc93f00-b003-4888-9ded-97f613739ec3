# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  user_bak.py
@Description    :
@CreateTime     :  2023/5/30 11:06
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/30 11:06
"""

import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath


class Share(weeeTest.TestCase):

    def share_product(self, headers, product_id: int = 9819):
        """# PDP 分享有哪些方式"""
        data = {
            "product_id": product_id
        }
        self.get(url='/ec/growth/share/product', headers=headers, params=data)
        return self.response

    def product_share_image(self, headers, product_id: int = 9819):
        """# 商品的图片分享"""
        data = {
            "product_id": product_id
        }
        self.get(url='/ec/growth/share/share_image', headers=headers, params=data)
        return self.response

    def collection_grocery_share(self, headers, collection_key):
        """ collection_grocery_share """
        data = {
            "collection_key": collection_key

        }
        self.get(url='/ec/content/collection/grocery/share', headers=headers, params=data)

        return self.response

    def collection_theme_share(self, headers, tag_id: int = 1):
        """# 合集主题分享"""
        data = {
            "tag_id": tag_id
        }
        self.get(url='/ec/growth/share/collection/theme', headers=headers, params=data)
        return self.response

    def collection_brand_share(self, headers, brand_key):
        """# 合集品牌分享"""
        data = {
            "brand_key": brand_key
        }
        self.get(url='/ec/growth/share/collection/brand', headers=headers, params=data)
        return self.response

    def brand_share(self, headers, brand_key):
        """# 品牌分享"""
        data = {
            "brand_key": brand_key
        }
        self.get(url='/ec/growth/share/brand', headers=headers, params=data)
        return self.response

    def share_chnnel_get(self, headers, source):
        """# 获取分享渠道方式"""
        data = {
            "from": source
        }
        self.get(url='/ec/growth/share/chnnel/get', headers=headers, params=data)
        return self.response

    # 获取Global商家的分享信息
    def cms_page_share(self, headers, page_key: str = "", page_type: str = '8'):
        data = {"page_key": page_key,  # page_type: 8, page_key: '' 是/mkpl/waterfall ， 其他页面都需要传 page_key
                "page_type": page_type}
        self.get(url='/ec/content/cms/page/share', headers=headers, params=data)
        return self.response

    def global_plus_share(self, headers):
        """# 全球购首单优惠券分享 """
        data = None
        self.get(url='/ec/growth/share/global_plus', headers=headers, params=data)
        return self.response

    def level_coupon_share(self, headers, plan_id: int = 2754):
        """# MKPL商家优惠券分享"""
        data = {
            "plan_id": plan_id  # 转盘id
        }
        self.get(url='/ec/growth/share/level_coupon', headers=headers, params=data)
        return self.response

    def raffle_share(self, headers, raffle_id):
        """# MKPL转盘抽奖分享"""
        data = {
            "from": raffle_id  # 转盘id
        }
        self.get(url='/ec/growth/share/raffle', headers=headers, params=data)
        return self.response

    def fresh_deli_share(self, headers):
        """# FBW熟食分享"""
        data = None
        self.get(url='/ec/growth/share/freshdeli', headers=headers, params=data)
        return self.response

    def bakery_share(self, headers):
        """# FBW每日现做分享"""
        data = None
        self.get(url='/ec/growth/share/bakery', headers=headers, params=data)
        return self.response

    def vendor_share(self, headers, vendor_id: int = 6887):
        """# MKPL商家分享"""
        data = {
            "vendor_id": vendor_id
        }
        self.get(url='/ec/growth/share/vendor', headers=headers, params=data)
        return self.response

    def topx_ranking_detail(self, headers, key):
        """# TOP X排行榜详情 """
        data = {
            "key": key  # 排行榜的key
        }
        self.get(url='/ec/growth/share/ranking-detail', headers=headers, params=data)
        return self.response

    def topx_ranking_list(self, headers, key: str = None):
        """# TOP X排行榜 """
        data = None
        # {
        #     "key": key  # 排行榜的key
        # }
        self.get(url='/ec/growth/share/ranking-list?key', headers=headers, params=data)
        return self.response

    def group_by_share(self, headers, key: str = None, vendor_id: int = 6887):
        """# MKPL 拼团分享 """
        data = {
            "key": key,  # 商家id 以及订单的key
            "vendor_id": vendor_id
        }
        self.get(url='/ec/growth/share/group-by', headers=headers, params=data)
        return self.response

    def group_by_bill_share(self, headers, alias: str = "test", order_id: int = 56013868, guest_id: int = 0,
                            vendor_id: int = 6887):
        """# MKPL 拼团账单分享 """
        data = {
            "alias": alias,  # 账单别名，订单id，分享者id，商家id
            "order_id": order_id,
            "guest_id": guest_id,
            "vendor_id": vendor_id,

        }
        self.get(url='/ec/growth/share/group-by-bill', headers=headers, params=data)
        return self.response

    def lighting_share(self, headers, deal_id):
        """# 生鲜秒杀分享 """
        data = {
            "deal_id": deal_id  # 帖子id
        }
        self.get(url='/ec/growth/share/lighting', headers=headers, params=data)
        return self.response

    def mkpl_lighting_share(self, headers, deal_id):
        """# mkpl秒杀分享 """
        data = {
            "deal_id": deal_id  # 帖子id
        }
        self.get(url='/ec/growth/share/mkpl/lighting', headers=headers, params=data)
        return self.response

    def topic_share(self, headers, topic_id: int = 1):
        """# 话题分享 """
        data = {
            "topic_id": topic_id  # 话题id
        }
        self.get(url='/ec/growth/share/topic', headers=headers, params=data)
        return self.response

    def hero_landing_share(self, headers, product_id: int = 9819):
        """# 英雄商品分享 """
        data = {
            "product_id": product_id  # 产品ID
        }
        self.get(url='/ec/growth/share/hero/landing', headers=headers, params=data)
        return self.response

    def affiliate_landing_share(self, headers):
        """# 返利联盟LANDING """
        data = None
        self.get(url='/ec/growth/share/affiliate/landing', headers=headers, params=data)
        return self.response
