# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  user_bak.py
@Description    :
@CreateTime     :  2023/5/30 11:06
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/30 11:06
"""

import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath


class Session(weeeTest.TestCase):

    def anon_session(self, header):
        """# 匿名获取session"""
        self.get(url='/ec/tracking/session_id', headers=header)
        # auth = jmespath(self.response, "object.token")
        # log.info(f'匿名auth:{auth}')
        # headers["authorization"] = 'Bearer ' + auth
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
    # user = Session()
    # user.anon_session(header=headers)
