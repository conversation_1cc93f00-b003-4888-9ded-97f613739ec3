import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath
import datetime


class ApiCs(weeeTest.TestCase):

    def self_service_list(self, headers):
        """# 帮助中心cs接口 """
        data = None
        self.get(url='/ec/cs/helpCenter/selfServiceList', headers=headers, params=data)
        return self.response

    def category_first_list(self, headers):
        """# 帮助中心cs接口 """
        data = None
        self.get(url='/ec/cs/helpCenter/categoryFirstList', headers=headers, params=data)
        return self.response

    def article_list(self, headers, categoryId):
        """# 帮助中心cs接口 """
        data = {"categoryId": categoryId}
        self.post(url='/ec/cs/helpCenter/articleList', headers=headers, json=data)
        return self.response

    def search_article(self, headers, keywords):
        """# 帮助中心cs接口 """
        data = {"keywords": keywords}
        self.post(url='/ec/cs/helpCenter/searchArticle', headers=headers, json=data)
        return self.response

    def article_detail(self, headers, desk_article_id: str, language: str = "zh"):
        """# 获取文章详情"""
        data = {"deskArticleId": desk_article_id, "language": language}
        self.post(url='/ec/cs/helpCenter/reviewArticle', headers=headers, json=data)
        return self.response
