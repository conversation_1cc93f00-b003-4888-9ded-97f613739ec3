import weeeTest
from weeeTest import weeeConfig


class RecordMarketplaceContent(weeeTest.TestCase):

    def record_marketplace_content(self, headers, recommend_session: str, parent_nums: str, type: str,
                                   special_nums: str | None):
        """# 获取waterfall商品，商家，视频排序"""
        """parent_nums和special_nums二选一;不穿category就是精选"""
        data = {
            "page": "global",
            "recommend_session": recommend_session,
            "category": {
                "parent_nums": [parent_nums],
                "nums": [],
                "type": type,
                "special_nums": None
            }
        } if special_nums is None else {
            "page": "global",
            "recommend_session": recommend_session,
            "category": {
                "parent_nums": None,
                "nums": [],
                "type": type,
                "special_nums": [special_nums]
            }
        }

        self.post(url='/ec/recommend/record/marketplace/content', headers=headers, json=data)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
