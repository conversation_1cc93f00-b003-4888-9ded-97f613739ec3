import weeeTest
from weeeTest import weeeConfig


class AdminEndpointInvoke(weeeTest.TestCase):

    def admin_endpoint_invoke(self, headers, category: str,  category_lib_id: int, user_id: str,
                              device_id: str, sales_org_id: int,hashtags: str=None,
                              endpoint: str = "global-plus-waterfall-recommendation"):
        """# 获取waterfall大数据返回的商品 admin_endpoint_invoke"""
        """不传category，默认精选.hashtags和category二传一；category_lib_id默认0，1，4，5，6;hashtags:onsale"""
        data = {
            "endpoint": endpoint,
            "body": {
                "category": [category],
                "category_lib_id": category_lib_id,
                "device_id": device_id,
                "in_cart_items": [],
                "n_item": 30,
                "user_id": user_id,
                "viewed_items": [],
                "hashtags": [hashtags],
                "sales_org_id": sales_org_id,
                "region_ids": [
                    "0",
                    "29",
                ]
            }
        }

        self.post(url='/ec/recommend/admin/endpoint/invoke', headers=headers, json=data)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
