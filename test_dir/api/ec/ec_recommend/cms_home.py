import weeeTest
from weeeTest import weeeConfig


class AdminEndpointInvoke(weeeTest.TestCase):

    def admin_endpoint_invoke(self, headers, zipcode: str, date: str, sales_org_id: int = 4):
        """# 首页组件（cmshome）"""
        data = {
            "scenes": "cmshome",
            "page": "home",
            "sales_org_id": sales_org_id,
            "zipcode": zipcode,
            "date": date,
            "offset": 0,
            "limit": 100
        }

        self.get(url='/ec/recommend/record', headers=headers, json=data)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
