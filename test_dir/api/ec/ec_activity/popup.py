# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  user_bak.py
@Description    :  
@CreateTime     :  2023/5/30 11:06
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/30 11:06
"""

import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath


class Popup(weeeTest.TestCase):
    def popup_precheck(self, headers):
        """# popup_precheck"""
        # 可以获取哪些page有popup
        self.get(url='/ec/activity/popup/precheck', headers=headers)
        return self.response

    def activity_popup(self, headers, page: str = "page_home"):
        """# activity_popup"""
        data = {"page": page}  # ["page_home","page_mkpl_waterfall","rtg_home"]
        self.get(url='/ec/activity/popup/page', headers=headers, params=data)
        return self.response

    def popup_success(self, headers, popup_id: int = 1712):
        """# popup"""
        data = {"popup_id": popup_id}
        self.put(url='/ec/activity/popup/success', headers=headers, json=data)
        return self.response

    def mkt_popup(self, headers, page: str = "page_mkpl_waterfall"):
        """# mkt_popup"""
        data = {"page": page}
        self.get(url='/ec/activity/popup/page', headers=headers, params=data)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
    user = Popup()
    user.activity_popup({"term": "tofu"})
    # user.search_v3()
    # user.email_login(email='<EMAIL>', password='aa123456')

    # user.email_register(email='test.' + user.random_word(num=5) + '@sayweee.com', password='aa123456')
