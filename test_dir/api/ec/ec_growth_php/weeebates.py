# -*- coding: utf-8 -*-
"""
<AUTHOR>  yue.wang
@Version        :  V1.0.0
------------------------------------
@Description    :
@CreateTime     :  2023/7/25
@Software       :  PyCharm
------------------------------------
"""

import weeeTest
from weeeTest import log, weeeConfig

from test_dir.api.ec.ec_customer.user.user_bak import User

class Weeebates(weeeTest.TestCase):
    def weeebates_order_confirm_pop(self,orderIds, headers):
        data={"orderIds":orderIds}
        #订单成功页popup数据
        self.post(url='/ec/growth_v2/weeebates/popup',json=data, headers=headers)
        return self.response

    def weeebates_new_order(self,headers,orderId):
        #获取砍单订单信息
        self.get(url=f'/ec/growth_v2/weeebates/new?orderId={orderId}', headers=headers)
        return self.response

    def weeebates_list(self,headers):
        #砍单list
        self.get(url='/ec/growth_v2/weeebates/list', headers=headers)
        return self.response

    def weeebates_create(self,headers,orderId,orderIds,productIds):
        #创建砍单
        data = {"orderId": orderId,"orderIds":orderIds,"productIds":productIds}
        self.post(url='/ec/growth_v2/weeebates',json=data,headers=headers)
        return self.response
    def weeebates_rtg_create(self,headers, data):
        #创建RTG砍单
        self.post(url='/ec/growth_v2/weeebates',json=data,headers=headers)
        return self.response


    def weeebates_order_edit(self, orderId, headers):
        #砍单创建后回调
        self.get(url=f'/ec/growth_v2/weeebates/{orderId}/edit', headers=headers)
        return self.response

    def weeebates_share_progress(self,orderId:str,headers):
        #砍单进度页
        self.get(url=f'/ec/growth_v2/weeebates/{orderId}', headers=headers)
        return self.response

    def weeebates_record_more_popup(self,headers):
        self.get(url='/ec/growth_v2/weeebates/record_more_popup', headers=headers)
        return self.response

    def weeebates_receiver(self,orderId,headers):
        #砍单
        data = {"orderId":orderId}
        self.post(url='/ec/growth_v2/weeebates/receiver',json=data,headers=headers)
        return self.response

    def weeebates_settle_order_share_points(self,orderId:str,headers):
        # 获取订单跟买返利
        self.get(url=f'/ec/growth_v2/weeebates/settle_order_share_points/{orderId}',headers=headers)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
    user = User()
    user.anon_auth()
    # user.email_register(email='test.' + user.random_word(num=5) + '@sayweee.com', password='aa123456')
    user.email_login(email='<EMAIL>', password='12')