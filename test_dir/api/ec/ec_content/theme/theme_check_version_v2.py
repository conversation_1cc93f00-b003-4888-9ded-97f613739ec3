import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath


class ThemeCheckVersionV2(weeeTest.TestCase):

    def theme_check_version_v2(self, headers):
        """# theme_check_version_v2"""
        data = {
        }
        self.get(url='/ec/content/theme/v2/check', headers=headers, params=data)

        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
