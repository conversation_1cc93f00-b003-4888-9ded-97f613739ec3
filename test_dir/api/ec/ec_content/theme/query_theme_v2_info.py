import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath


class QueryThemeV2Info(weeeTest.TestCase):

    def query_theme_v2_info(self, headers):
        """# THEME"""
        data = {
        }
        self.get(url='/ec/content/theme/v2', headers=headers, params=data)

        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
