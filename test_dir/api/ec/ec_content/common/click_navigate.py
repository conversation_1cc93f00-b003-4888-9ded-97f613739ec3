# !/usr/bin/python3
# -*- coding: utf-8 -*-

"""
<AUTHOR>  huimin.li
@Version        :  V1.0.0
------------------------------------
@File           :  seller_desc.py
@Description    :
@CreateTime     :  2023/8/15 19:06
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/8/15 19:06
"""

import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath


class ClickNavigate(weeeTest.TestCase):

    def click_navigate(self, headers, config_id, zipcode):
        data = {
            "navigate_config_id": config_id,
            "zipcode": zipcode
        }

        self.post(url='/ec/content/navigate/label/click', headers=headers, json=data)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
