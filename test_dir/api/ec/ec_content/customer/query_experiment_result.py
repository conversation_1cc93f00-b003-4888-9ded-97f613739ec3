import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath


class QueryExperimentResult(weeeTest.TestCase):

    def query_experiment_result(self, headers, experiment_id):
        """# banner信息"""
        data = {
            "experiment_id": experiment_id,
        }
        self.get(url='/ec/content/customer/experiment', headers=headers, params=data)

        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
