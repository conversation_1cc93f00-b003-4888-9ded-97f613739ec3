import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath


class QueryProductListByDsKey(weeeTest.TestCase):

    def query_product_list_by_ds_key(self,  headers, dataobject_key):
        """# query_product_list_by_ds_key"""
        data = {
            "dataobject_key": dataobject_key,
        }
        self.get(url='/ec/content/product/list', headers=headers, params=data)

        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
