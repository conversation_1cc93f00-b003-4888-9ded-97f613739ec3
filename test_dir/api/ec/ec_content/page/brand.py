import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath


class Brand(weeeTest.TestCase):

    def brand(self, headers,brand_key):
        """# brand"""
        data = {
            "brand_key": brand_key,
        }
        self.get(url='/ec/content/cms/page/brand', headers=headers, params=data)

        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
