from typing import Optional

import weeeTest
from weeeTest import weeeConfig


class QueryPageData(weeeTest.TestCase):
    """ # 获取页面组件key"""

    def query_page_data(self, headers, page_key: str, page_type: str, lang: str = "en",
                        sales_org_id: int = 4,
                        zipcode: str = "98011",
                        store_key: Optional[str] = None,
                        mode: Optional[str] = None):
        # 商家首页（6）：seller - home
        # 活动页（7）：activity
        # global+页面（8）：home, recommend, ja等
        # 品牌页面（9）：brand
        # 主题页面（10）：theme
        data = {"page_key": page_key,
                "page_type": page_type,
                "lang": lang,
                "sales_org_id": sales_org_id,
                "zipcode": zipcode
                }
        if mode:
            data['mode'] = mode
        if store_key:
            data['store_key'] = store_key
        self.get(url='/ec/content/cms/page/getPage', headers=headers, params=data)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
