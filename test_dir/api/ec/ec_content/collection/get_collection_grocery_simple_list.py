import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath


class GetCollectionGrocerySimpleList(weeeTest.TestCase):

    def get_collection_grocery_simple_list(self, headers):
        """# 查询首页手动合集列表 simple_list"""
        data = {
            #"sales_org_id": sales_org_id
        }
        self.get(url='/ec/content/collection/grocery/simple/list', headers=headers, params=data)

        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
