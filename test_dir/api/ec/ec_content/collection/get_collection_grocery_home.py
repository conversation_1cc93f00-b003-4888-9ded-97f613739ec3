import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath
import datetime


class GetCollectionGroceryHome(weeeTest.TestCase):

    def get_collection_grocery_home(self, headers):
        """ 首页生鲜合集（生鲜合集数据源地址）:get_collection_grocery_home """
        data = {
            "limit": 50,
            "product_limit": 100
        }
        self.get(url='/ec/content/collection/grocery/home', headers=headers, params=data)

        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
