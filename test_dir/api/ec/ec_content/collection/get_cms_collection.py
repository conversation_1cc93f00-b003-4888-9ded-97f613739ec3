import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath
import datetime


class CmsCollection(weeeTest.TestCase):

    def get_cms_collection(self, headers, collection_key):
        """# winback活动： cms合集"""
        data = {
            "collection_key": collection_key
        }
        self.get(url="/ec/content/collection/" + str(collection_key), headers=headers, params=data)

        return self.response




if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
