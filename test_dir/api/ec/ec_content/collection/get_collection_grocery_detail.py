import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath
import datetime


class GetCollectionGroceryDetail(weeeTest.TestCase):

    def get_collection_grocery_detail(self, headers, collection_key, lang: str = "en", zipcode: int = 98011,
                                      date: str = (datetime.datetime.now() + datetime.timedelta(days=2)).strftime(
                                          '%Y-%m-%d')):
        """ 合集详情页：get_collection_grocery_detail """
        data = {
            "collection_key": collection_key,
            "lang": lang,
            "zipcode": zipcode,
            "date": date

        }
        self.get(url='/ec/content/collection/grocery/detail', headers=headers, params=data)

        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
