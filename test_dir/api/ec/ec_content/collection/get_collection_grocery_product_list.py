import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath


class GetCollectionGroceryProductList(weeeTest.TestCase):

    def get_collection_grocery_product_list(self, headers, collection_key: str = "tzal4"):
        """# Winback 合集product_list"""
        data = {
            "collection_key": collection_key,
            "product_limit": 10,
            "filter_sold_out": True
        }
        self.get(url='/ec/content/collection/product/list', headers=headers, params=data)

        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
