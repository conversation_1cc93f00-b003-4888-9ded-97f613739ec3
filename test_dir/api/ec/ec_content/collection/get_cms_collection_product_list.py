import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath


class GetCmsCollectionProductList(weeeTest.TestCase):

    def get_cms_collection_product_list(self, headers, collection_key):
        """# winback活动： product_list"""
        data = {
            "collection_key": collection_key
        }
        self.get(url='/ec/content/collection/product/list', headers=headers, params=data)

        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
