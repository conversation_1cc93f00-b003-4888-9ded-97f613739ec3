import weeeTest
from weeeTest import weeeConfig
import datetime


class DealOfWeekCollection(weeeTest.TestCase):
    """ # 获取首页一周一品 """

    def deal_of_week_collection(self, headers, zipcode: int = 98011,
                                date: str = (datetime.datetime.now() + datetime.timedelta(days=2)).strftime(
                                    '%Y-%m-%d'), lang: str = "en"):
        data = {
            "lang": lang,
            "date": date,
            "zipcode": zipcode
        }
        self.get(url='/ec/content/collection/deal_of_week', headers=headers, params=data)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
    user = DealOfWeekCollection()
