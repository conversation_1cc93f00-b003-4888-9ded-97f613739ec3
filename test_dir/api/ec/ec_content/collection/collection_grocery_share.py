import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath
import datetime


class CollectionGroceryShare(weeeTest.TestCase):

    def collection_grocery_share(self, headers, collection_key):
        """ collection_grocery_share """
        data = {
            "collection_key": collection_key

        }
        self.get(url='/ec/content/collection/grocery/share', headers=headers, params=data)

        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
