import datetime

import weeeTest
from weeeTest import weeeConfig


class CmsBanner(weeeTest.TestCase):
    """ # 获取首页组件"""

    def cms_banner(self, headers, zipcode: int = 98011,
                   dataobject_key: str = None, lang: str = "en",
                   date: str = (datetime.datetime.now() + datetime.timedelta(days=2)).strftime(
                       '%Y-%m-%d'),

                   ):
        data = {
            "lang": lang,
            "zipcode": zipcode,
            "date": date,
            "dataobject_key": dataobject_key
        }
        self.get(url='/ec/content/banner/list', headers=headers, params=data)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
