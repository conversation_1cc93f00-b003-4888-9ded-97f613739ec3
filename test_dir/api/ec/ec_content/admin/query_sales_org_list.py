import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath


class QuerySalesOrgList(weeeTest.TestCase):

    def query_sales_org_list(self, headers):
        """# query sales org list"""
        data = {
        }
        self.get(url='/ec/content/sales_org/list', headers=headers, params=data)

        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
