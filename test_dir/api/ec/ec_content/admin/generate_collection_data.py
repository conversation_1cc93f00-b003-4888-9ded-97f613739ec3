import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath


class GenerateCollectionData(weeeTest.TestCase):

    def generate_collection_data(self, headers):
        """# banner_list信息"""
        data = {
        }
        self.get(url='/ec/content/admin/generateCollectionData', headers=headers, params=data)

        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
