import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath


class QueryCupportHotdishZipcode(weeeTest.TestCase):

    def query_cupport_hotdish_zipcode(self, headers):
        """# query cupport hotdish zipcode"""
        data = {
        }
        self.get(url='/ec/content/admin/get_hotdish_zipcode', headers=headers, params=data)

        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
