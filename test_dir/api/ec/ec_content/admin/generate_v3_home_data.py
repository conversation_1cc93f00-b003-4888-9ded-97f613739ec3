import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath


class GenerateV3HomeData(weeeTest.TestCase):

    def generate_v3_home_data(self, headers, platform, sales_org_id: int = 4):
        """# generateV3HomeData"""
        data = {
            "sales_org_id": sales_org_id,
            "platform": platform,
        }
        self.get(url='/ec/content/admin/generateV3HomeData', headers=headers, params=data)

        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
