import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath


class QueryByConfigKeyAndLang(weeeTest.TestCase):

    def query_by_configKey_and_lang(self, headers):
        """# query by config<PERSON><PERSON> and lang"""
        data = {
        }
        self.get(url='/ec/content/config/fresh_guarantee_config', headers=headers, params=data)

        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
