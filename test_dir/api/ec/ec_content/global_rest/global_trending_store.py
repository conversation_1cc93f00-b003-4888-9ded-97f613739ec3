import weeeTest
from weeeTest import weeeConfig


class GetGlobalTrendingList(weeeTest.TestCase):
    # 获取global+的热销商家列表
    def get_global_trending_list(self, headers, zipcode: str | None, source_key: str | None,
                                 dataobject_key: str | None, lang: str | None, date: str | None):
        data = {
            "zipcode": zipcode,
            "dataobject_key": dataobject_key,
            "lang": lang,
            "date": date,
            "source_key": source_key
        }

        self.get(url='/ec/content/marketplace/global/trending/list', headers=headers, params=data)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
