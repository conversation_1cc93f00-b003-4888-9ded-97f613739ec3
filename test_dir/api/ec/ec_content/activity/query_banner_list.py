import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath


class QueryBannerList(weeeTest.TestCase):

    def query_banner_list(self, headers, dataobject_key, lang: str = "en", sales_org_id: int = 4):
        """# banner_list信息"""
        data = {
            "sales_org_id": sales_org_id,
            "dataobject_key": dataobject_key,
            "lang": lang,
        }
        self.get(url='/ec/content/activity/queryBannerList', headers=headers, params=data)

        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
