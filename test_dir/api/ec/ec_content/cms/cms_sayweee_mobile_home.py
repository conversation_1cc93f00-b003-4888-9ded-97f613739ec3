import weeeTest
from weeeTest import weeeConfig


class CmsSayweeeHome(weeeTest.TestCase):
    """ # 获取首页组件"""

    def cms_sayweee_mobile_home(self, headers, zipcode: int | str = 98011,
                                sales_org_id: int = 4, lang: str = "en"):
        data = {
            "lang": lang,
            "zipcode": zipcode,
            "sales_org_id": sales_org_id
        }
        self.get(url='/ec/content/cms/page/sayweee_mobile_home', headers=headers, params=data)
        return self.response

    def cms_sayweee_pc_home(self, headers, zipcode: int | str = 98011,
                            sales_org_id: int = 4, lang: str = "en"):
        data = {
            "lang": lang,
            "zipcode": zipcode,
            "sales_org_id": sales_org_id
        }
        self.get(url='/ec/content/cms/page/sayweee_pc_home', headers=headers, params=data)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
