import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath
import datetime


class EcSearch(weeeTest.TestCase):
    def ec_search_keyword(self, headers, from_page: str = None, filter_key_word: str = "fruit", lang: str = "en",
                          trigger_type: str = "search_active",
                          date: str = (datetime.date.today() + datetime.timedelta(days=1)).strftime('%Y-%m-%d')
                          , zipcode: str = "98011", sort: str = "recommend", filters: dict = None):
        """# v3_search global搜索"""
        data = {
            "from_page": from_page,  # "mkpl_global_search",
            "zipcode": zipcode,
            "date": date,
            "lang": lang,
            "offset": 0,
            "limit": 10,
            "force": False,
            "filter_key_word": filter_key_word,
            "trigger_type": trigger_type,
            "filters": filters,
            "sort": sort
        }
        self.get(url='/ec/search/keyword', headers=headers, params=data)
        return self.response
