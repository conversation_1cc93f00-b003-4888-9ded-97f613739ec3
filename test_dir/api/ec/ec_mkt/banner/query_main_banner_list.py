import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath


class QueryMainBannerList(weeeTest.TestCase):

    def query_main_banner_list(self, headers,dataobject_key):
        """# query_main_banner_list信息"""
        data = {
            "dataobject_key": dataobject_key,
        }
        self.get(url='/ec/content/banner/main_banner_list', headers=headers, params=data)

        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
