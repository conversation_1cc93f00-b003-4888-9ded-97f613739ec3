import datetime

import weeeTest
from weeeTest import weeeConfig


# dataobject_key=Global_page_key().cms_page_getPage(headers=headers)
class QueryTrendingListByDsKey(weeeTest.TestCase):

    # 获取Global热销商家的信息
    def global_trending_list(self, headers, source_key: str, dataobject_key: str,
                             date: str = (datetime.date.today() + datetime.timedelta(days=1)).strftime('%Y-%m-%d'),
                             lang: str = "en"):
        data = {"source_key": source_key,
                "lang": lang,
                "date": date,
                "dataobject_key": dataobject_key}
        self.get(url='/ec/content/marketplace/global/trending/list', headers=headers, params=data)
        return self.response

    def global_trending_list_v2(self, headers, params):
        data = params
        self.get(url='/ec/content/marketplace/global/trending/list', headers=headers, params=data)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
    user = QueryTrendingListByDsKey()
