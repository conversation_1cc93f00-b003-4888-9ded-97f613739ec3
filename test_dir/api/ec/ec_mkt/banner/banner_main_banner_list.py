import weeeTest
from weeeTest import weeeConfig
import datetime


class BannerMainBannerList(weeeTest.TestCase):

    # 获取Global的Banner信息
    def main_banner_list(self, headers, dataobject_key: str, lang: str = "en",
                         date: str = (datetime.date.today() + datetime.timedelta(days=1)).strftime('%Y-%m-%d')
                         ):
        data = {
            "lang": lang,
            "date": date,
            "dataobject_key": dataobject_key
        }
        self.get(url='/ec/content/cms/page/share', headers=headers, params=data)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
    user = BannerMainBannerList()
