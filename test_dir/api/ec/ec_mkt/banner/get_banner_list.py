# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  user_bak.py
@Description    :
@CreateTime     :  2023/5/30 11:06
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/30 11:06
"""
import datetime

import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath


class GetBannerList(weeeTest.TestCase):

    def get_banner_list(self, headers, type, dataobject_key: str = None,
                        sales_org_id: int = 4, lang: str = "en",
                        date: str = (datetime.date.today() + datetime.timedelta(days=1)).strftime('%Y-%m-%d'),
                        is_rtg: int = None, zipcode=None
                        ):
        """获取banner列表"""
        data = {
            "type": type,
            "sales_org_id": sales_org_id,
            "lang": lang,
            "date": date,
            "dataobject_key": dataobject_key,
            "is_rtg": is_rtg,
            "zipcode": zipcode
        }
        self.get(url='/ec/mkt/banner', headers=headers, params=data)
        return self.response

    def get_banner_list_rtg(self, headers, type,
                            sales_org_id: int = 4, lang: str = "en",
                            date: str = (datetime.date.today() + datetime.timedelta(days=1)).strftime('%Y-%m-%d'),
                            is_rtg: int = 1, zipcode=None
                            ):
        """获取rtg banner列表"""
        data = {
            "type": type,
            "sales_org_id": sales_org_id,
            "lang": lang,
            "date": date,
            "is_rtg": is_rtg,
            "zipcode": zipcode
        }
        self.get(url='/ec/mkt/banner', headers=headers, params=data)
        return self.response

    def banner_component(self, headers, placement: str = "home_component", zipcode: int = 98011, lang: str = "en",
                         date: str = (datetime.date.today() + datetime.timedelta(days=1)).strftime('%Y-%m-%d'),
                         ):
        """获取2x2 banner组件"""
        data = {
            "placement": placement,
            "zipcode": zipcode,
            "lang": lang,
            "date": date
        }
        self.get(url='/ec/growth/banner/component', headers=headers, params=data)
        return self.response

    def get_category_banner(self, headers, category: str = "sale", zipcode: int = 98011, lang: str = "en",
                            date: str = (datetime.date.today() + datetime.timedelta(days=1)).strftime('%Y-%m-%d'),
                            ):
        """获取分类banner"""
        data = {
            "category": category,
            "zipcode": zipcode,
            # "lang": lang,
            # "date": date
        }
        self.get(url='/ec/growth/banner/get_category_banner', headers=headers, params=data)
        return self.response

    def get_category_banner_v2(self, headers, category: str = "sale", zipcode: int = 98011, lang: str = "en",
                               date: str = (datetime.date.today() + datetime.timedelta(days=1)).strftime('%Y-%m-%d'),
                               ):
        """获取分类banner"""
        data = {
            "category": category,
            "zipcode": zipcode,
            # "lang": lang,
            # "date": date
        }
        self.get(url='/ec/growth/banner/get_category_banner/v2', headers=headers, params=data)
        return self.response

    def get_theme_banner(self, headers, category: str = "all", zipcode: int = 98011, lang: str = "en",
                         date: str = (datetime.date.today() + datetime.timedelta(days=1)).strftime('%Y-%m-%d'),
                         ):
        """获取主题banner"""
        data = {
            "category": category,
            "zipcode": zipcode,
            "lang": lang,
            "date": date
        }
        self.get(url='/ec/growth/banner/get_theme_banner', headers=headers, params=data)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
