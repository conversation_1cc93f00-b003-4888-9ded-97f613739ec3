import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath


class QueryBanner(weeeTest.TestCase):

    def query_banner(self, headers, type, sales_org_id: int = 4):
        """# banner信息"""
        data = {
            "sales_org_id": sales_org_id,
            "type": type
        }
        self.get(url='/ec/content/banner', headers=headers, params=data)

        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
