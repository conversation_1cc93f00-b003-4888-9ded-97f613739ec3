"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :  activity_trade_in.py
@Description    :  
@CreateTime     :  2023/8/15 13:44
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/8/15 13:44
"""
import datetime
from weeeTest import weeeConfig
import weeeTest


class ActivityTradeIn(weeeTest.TestCase):
    def activity_trade_in(self, headers, type, deal_id: int = 484344, delivery_sale_org_id: int = 4):
        """获取换购列表数据"""
        # type, deal_id: int = 484344, delivery_sale_org_id: int = 4
        data = {
            "type": type,
            "delivery_sale_org_id": delivery_sale_org_id,
            "deal_id": deal_id,
        }
        # data=None
        self.get(url='/ec/item/activity/trade_in', headers=headers, params=data)
        return self.response

    def item_activity_trade_in(self, headers, type, deal_id: int = 484344, delivery_sale_org_id: int = 4,
                               filter_alcohol: int = 0, shop_more_type: str = "Deal", diff_amount: int = 0):
        """获取换购列表数据"""
        # type, deal_id: int = 484344, delivery_sale_org_id: int = 4
        data = {
            "type": type,
            "deal_id": deal_id,
            "delivery_sale_org_id": delivery_sale_org_id,
            "filter_alcohol": filter_alcohol,
            "shop_more_type": shop_more_type,
            "diff_amount": diff_amount,
            "limit_amount": 68.00,
            "limit_count": 5
        }
        self.get(url='/ec/item/activity/trade_in', headers=headers, params=data)
        return self.response

    def item_activity_cart_trade_in(self, headers, type,
                                    filter_alcohol: int = 0):
        """获取换购列表数据"""
        # type, deal_id: int = 484344, delivery_sale_org_id: int = 4
        data = {
            "type": type,
            "filter_alcohol": filter_alcohol,
            "limit": 10
        }
        self.get(url='/ec/item/activity/cart/trade_in', headers=headers, params=data)
        return self.response

    def activity_shop_more_tab(self, headers, diff_amount: float = 10.00,
                               shop_more_type: str = "Deal"):
        """获取换购列表点击去凑单页面接口"""
        data = {
            # "tab": tab,
            # "price_group_key": price_group_key,
            "shop_more_type": shop_more_type,
            # "ref_value": ref_value,
            "diff_amount": diff_amount
            # "type": type,
            # "offset": 0,
            # "limit": 20
        }
        self.get(url='/ec/item/activity/shop_more/tab', headers=headers, params=data)
        return self.response

    def item_activity_shop_more_products(self, headers, tab: str = "featured", price_group_key: str = None,
                                         ref_value: str = None, diff_amount: float = 10.00,
                                         shop_more_type: str = "Deal", type: str = "normal"):
        """获取换购列表点击去凑单页面接口"""
        data = {
            "tab": tab,
            "price_group_key": price_group_key,
            "shop_more_type": shop_more_type,
            "ref_value": ref_value,
            "diff_amount": diff_amount,
            "type": type,
            "offset": 0,
            "limit": 20
        }
        self.get(url='/ec/item/activity/shop_more/products', headers=headers, params=data)
        return self.response

    def item_activity_shop_more_global_products(self, headers, tab: str = None, price_group_key: str = None,
                                         ref_value: str = None, diff_amount: float = None, refer_value: str = None,
                                         shop_more_type: str = None, type: str = None):
        """点击去凑单页面接口"""
        data = {
            "tab": tab,
            "price_group_key": price_group_key,
            "shop_more_type": shop_more_type,
            "ref_value": ref_value,
            "refer_value": refer_value,
            "diff_amount": diff_amount,
            "type": type,
            "offset": 0,
            "limit": 20
        }
        self.get(url='/ec/item/activity/shop_more/products', headers=headers, params=data)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
