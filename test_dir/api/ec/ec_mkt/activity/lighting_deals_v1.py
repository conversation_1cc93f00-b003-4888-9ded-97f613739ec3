# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  user_bak.py
@Description    :
@CreateTime     :  2023/5/30 11:06
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/30 11:06
"""
import datetime

import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath


class LightingDeals(weeeTest.TestCase):

    def lighting_deals(self, headers, lang: str = "en",
                       date: str = (datetime.date.today() + datetime.timedelta(days=1)).strftime('%Y-%m-%d'),
                       limit: int = 60,
                       ):
        """获取生鲜秒杀列表"""
        data = {
            "lang": lang,
            "date": date,
            "limit": limit
        }
        self.get(url='/ec/mkt/activity/lightning_deals', headers=headers, params=data)

        return self.response

    def lightings(self, headers, deal_id, zipcode: int = 98011, limit: int = 60
                  ):
        """获取生鲜秒杀详情页面列表"""
        data = {
            "deal_id": deal_id,
            "zipcode": zipcode
        }
        self.get(url='/ec/mkt/activity/lightning', headers=headers, params=data)

        return self.response

    def lightning_deals_remind(self, headers, product_id, status: str = "A"
                               ):
        """未开始秒杀，添加提醒"""
        data = {
            "product_id": product_id,
            "status": status  # A 添加提醒，X 取消提醒
        }
        self.post(url='/ec/mkt/activity/lightning_deals/remind', headers=headers, json=data)

        return self.response

    def special_share(self, headers, deal_id):
        """秒杀分享"""
        data = {
            "deal_id": deal_id
        }
        self.get(url='/ec/mkt/activity/special_share', headers=headers, params=data)

        return self.response

    def carousels_lighting(self, headers, lang: str = "zh",
                           date: str = (datetime.date.today() + datetime.timedelta(days=1)).strftime('%Y-%m-%d')
                           ):
        """获取餐馆秒杀"""
        data = {
            "lang": lang,
            "date": date
        }
        self.get(url='/ec/mkpl/v2/carousels/lightning', headers=headers, params=data)

        return self.response

    def mkpl_lighting(self, headers, lang: str = "zh",
                      date: str = (datetime.date.today() + datetime.timedelta(days=1)).strftime('%Y-%m-%d')
                      ):
        """获取mkpl秒杀"""
        data = {
            "lang": lang,
            "date": date
        }
        self.get(url='/ec/marketplace/lightning/carousel', headers=headers, params=data)

        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
