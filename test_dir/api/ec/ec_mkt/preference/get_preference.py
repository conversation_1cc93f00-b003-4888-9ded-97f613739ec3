# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  user_bak.py
@Description    :
@CreateTime     :  2023/5/30 11:06
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/30 11:06
"""
import datetime

import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath


class GetPreference(weeeTest.TestCase):

    def get_preference(self, headers, zipcode: int = 98011, lang: str = "en",
                       date: str = (datetime.date.today() + datetime.timedelta(days=1)).strftime('%Y-%m-%d'),
                       limit: int = 20, dataobject_key: str = "ds_item_perference"):
        """获取猜你喜欢列表"""
        data = {
            "zipcode": zipcode,
            "lang": lang,
            "date": date,
            "limit": limit,
            "offset": 0,
            "dataobject_key": dataobject_key

        }
        self.get(url='/ec/mkt/recommend/home/<USER>', headers=headers, params=data)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
