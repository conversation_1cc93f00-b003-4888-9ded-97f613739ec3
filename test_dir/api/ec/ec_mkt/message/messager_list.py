import weeeTest
from weeeTest import weeeConfig
import datetime


class HomeMessage(weeeTest.TestCase):

    # 获取message信息
    def top_message(self, headers):
        """new_user_message"""
        data = None
        self.get(url='/ec/growth/message/get/top_message', headers=headers, params=data)
        return self.response

    def new_user_message(self, headers, channel_type: str = None, referral_type: str = None,
                         source: str = None):
        """new_user_message"""
        data = {
            "channel_type": channel_type,
            "referral_type": referral_type,
            "source": source
        }
        self.get(url='/ec/mkt/message/new_user_message', headers=headers, params=data)
        return self.response

    def home_header_message(self, headers, lang: str = "en",
                            date: str = (datetime.date.today() + datetime.timedelta(days=1)).strftime('%Y-%m-%d')
                            ):
        """home_header_message"""
        data = {
            "version": "v2",
            "lang": lang,
            "date": date
        }
        self.get(url='/ec/customer/messages/home_header', headers=headers, params=data)
        return self.response

    def show_message(self, headers, value: str):
        """show_message"""
        data = {"value": value}
        self.post(url='/ec/customer/event/show_message', headers=headers, json=data)
        return self.response

    def get_message(self, headers, page_key: str = "home_page", position: str = "top", zipcode: int = 98011,
                    lang: str = "en",
                    date: str = (datetime.date.today() + datetime.timedelta(days=1)).strftime('%Y-%m-%d'),
                    ):
        """get_message"""
        data = {
            "page_key": page_key,
            "position": position,
            "zipcode": zipcode,
            "lang": lang,
            "date": date

        }
        self.get(url='/ec/growth/message/get', headers=headers, params=data)
        return self.response

    def get_safe_secure_tips(self, headers):
        """get_message"""
        data = None
        self.get(url='/ec/growth/message/get_safe_secure_tips', headers=headers, params=data)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
