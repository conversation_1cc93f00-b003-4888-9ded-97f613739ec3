import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath
import datetime


class ApiTopRanking(weeeTest.TestCase):

    # 排行榜
    def top_ranking_catalogue_list(self, headers):
        """# 排行榜分类列表 """
        data = None
        self.get(url='/ec/item/top_ranking/catalogue/list', headers=headers, json=data)
        return self.response

    def top_ranking_list(self, headers, catalogue_num: str = "trending",
                         collection_type: str = None):
        """# 查询榜单列表 """
        data = {
            "catalogue_num": catalogue_num,  # trending
            "collection_type": collection_type,  # [repurchase,most_liked]
            "offset": 0,  # 起始位置 默认0
            "limit": 20  # 查询个数 默认10

        }
        self.get(url='/ec/item/top_ranking/list', headers=headers, params=data)
        return self.response

    def top_ranking_detail(self, headers, key: str = "178bbbws"):
        """# 榜单详情 """
        data = {
            "key": key,
            "offset": 0,  # 起始位置 默认0
            "limit": 2  # 查询个数 默认10

        }
        self.get(url='/ec/item/top_ranking/detail', headers=headers, params=data)
        return self.response

    def top_ranking_related_list(self, headers, key: str = "178bbbws"):
        """# 查询榜单关联榜单列表 """
        data = {
            "key": key
        }
        self.get(url='/ec/item/top_ranking/related/list', headers=headers, params=data)
        return self.response
