import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath
import datetime


class DsCollectionHome(weeeTest.TestCase):

    def ds_collection_home(self, headers,  ds_url: str, dataobject_key: str = None,scenes: str = None,
                           date: str = (datetime.date.today() + datetime.timedelta(days=1)).strftime('%Y-%m-%d'),
                           lang: str = "en"):
        """ # 大数据推荐合集商品查询--首页 """
        data = {
            "lang": lang,
            "dataobject_key": dataobject_key,  # top trending 直接传 cm_item_exciting，ds_item_buy_it_again_443408，
            "scenes": scenes,  # "cmscomponent" top trending 会传，默认是空，buyagaincollection
            # "date": date,
            "limit": 10,
            "offset": 0

        }
        self.get(url="/ec/item/v1/ds_collection/" + str(ds_url) + "/home", headers=headers, params=data)

        return self.response

    def collection_specific(self, headers, lang: str = "en",
                            date: str = (datetime.date.today() + datetime.timedelta(days=1)).strftime('%Y-%m-%d')
                            ):
        """# waterfall首页置顶合集的接口"""
        data = {
            "position": 1,
            "product_limit": 20,
            "lang": lang,
            "date": date
        }
        self.get(url="/ec/content/collection/specific", headers=headers, params=data)

        return self.response

if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
