import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath
import datetime


class DsCollectionDetailPage(weeeTest.TestCase):

    def ds_collection_detail_page(self, headers, dataobject_key: str, ds_url: str, lang: str = "en"):
        """ ds_collection_detail_page """
        data = {
            "lang": lang,
            "dataobject_key": dataobject_key,
            "limit": 10,
            "offset": 0

        }
        self.get(url="/ec/item/v1/ds_collection/" + str(ds_url) + "/page", headers=headers, params=data)

        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
