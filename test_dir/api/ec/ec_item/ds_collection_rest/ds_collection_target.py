import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath
import datetime


class DsCollectionTarget(weeeTest.TestCase):

    def ds_collection_target(self, headers, store_key: str = None):
        """ targeted collection页查询 """
        data = {
            "store_key": store_key,  # 不传,默认为all
            "limit": 10,
            "offset": 0

        }
        self.get(url="/ec/item/v1/ds_collection/target", headers=headers, params=data)

        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
