import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath
import datetime


class DsCollectionMiniMarketDetail(weeeTest.TestCase):

    def ds_collection_mini_market_detail(self, headers, key, category_num):
        """ # mini Market 查询 """
        data = {
            "limit": 10,
            "offset": 0,
            "key": key,
            "category_num": category_num

        }
        self.get(url="/ec/item/v1/ds_collection/mini_market/detail", headers=headers, params=data)

        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
