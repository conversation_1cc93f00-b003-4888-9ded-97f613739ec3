import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath
import datetime


class DsCollectionDetail(weeeTest.TestCase):

    def ds_collection_detail(self, headers, dataobject_key:str,ds_url:str, scenes: str = None,
                             date: str = (datetime.date.today() + datetime.timedelta(days=1)).strftime('%Y-%m-%d'),
                             zipcode: int = 98011, lang: str = "en"):
        """ # 合集商品查询--详情页 """
        data = {
            "lang": lang,
            "dataobject_key": dataobject_key,  # top trending 直接传 cm_item_exciting
            "scenes": scenes,  # "cmscomponent" top trending 会传，默认是空
            "zipcode": zipcode,
            "zip_code": zipcode,
            "date": date,
            "limit": 10,
            "offset": 0

        }
        self.get(url="/ec/item/v1/ds_collection/" + str(ds_url), headers=headers, params=data)

        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
