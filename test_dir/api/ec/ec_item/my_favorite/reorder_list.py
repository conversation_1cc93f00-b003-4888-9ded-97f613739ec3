import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath
import datetime


class ReorderList(weeeTest.TestCase):

    def reorder_list(self, headers, zipcode: str = 98011, filter_sub_category: str = "fruit"):
        """ Reorder list """
        data = {
            "zipcode": zipcode,
            "offset": 0,
            "limit": 100,
            "filter_sub_category": filter_sub_category

        }
        self.get(url='/ec/item/me/favorites/reorder/list', headers=headers, params=data)

        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
