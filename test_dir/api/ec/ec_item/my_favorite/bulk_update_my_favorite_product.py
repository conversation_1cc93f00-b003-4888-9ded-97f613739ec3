import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath
import datetime


class BulkUpdateMyFavoriteProduct(weeeTest.TestCase):

    def bulk_update_my_favorite_product(self, headers, product_ids: list):
        """ Bulk update my favorite product """
        data = {
            "is_watch": False,
            "product_ids": product_ids
        }
        self.post(url='/ec/item/me/favorites/batch/watch', headers=headers, json=data)

        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
