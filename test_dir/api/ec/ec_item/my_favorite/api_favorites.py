import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath
import datetime


class ApiFavorites(weeeTest.TestCase):

    def my_favorite_product_list_v2(self, headers, zipcode: int = 98011, limit: int = 100,
                                    filter_sub_category: str = ""):
        """ 我的收藏列表v2-H5 """
        data = {
            "zipcode": zipcode,
            "offset": 0,
            "limit": limit,
            "hasMore": True,
            "filter_sub_category": filter_sub_category

        }
        self.get(url='/ec/item/me/favorites/v2/list', headers=headers, params=data)

        return self.response

    def my_favorite_product_list(self, headers, zipcode: int = 98011, limit: int = 60,
                                    categoryNum: str = None):
        """ 我的收藏列表v2-PC """
        data = {
            "zipcode": zipcode,
            "offset": 0,
            "limit": limit,
            "hasMore": True,
            "categoryNum": categoryNum

        }
        self.get(url='/ec/item/me/favorites/list', headers=headers, params=data)

        return self.response

    def favorites_set(self, headers, target_id: str = "24536", type: str = "product_favorite_a"):
        """ 添加/取消 我的收藏 """
        data = {
            "type": type,  # product_favorite_a
            "target_id": target_id
        }

        self.post(url='/ec/customer/favorites/set', headers=headers, json=data)
        return self.response

    def favorites_simple(self, headers):
        """ 我的收藏商品id集合 """
        data = None

        self.get(url='/ec/customer/favorites/simple', headers=headers, json=data)
        return self.response

    def favorites_batch_batch(self, headers, product_ids: list):
        """ 批量取消我的收藏 """
        data = {
            "is_watch": False,
            "product_ids": product_ids
        }
        self.post(url='/ec/item/me/favorites/batch/watch', headers=headers, json=data)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
