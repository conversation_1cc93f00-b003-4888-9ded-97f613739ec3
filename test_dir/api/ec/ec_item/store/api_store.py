import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath
import datetime


class ApiStore(weeeTest.TestCase):

    def store_list_all(self, headers, lang: str = "en", zipcode: int = 98011,
                       date: str = (datetime.date.today() + datetime.timedelta(days=1)).strftime('%Y-%m-%d')
                       ):
        """# 查询所有store列表 """
        data = {
            "zipcode": zipcode,
            "date": date,
            "lang": lang,
            "limit": 50,
            "offset": 0

        }
        self.get(url='/ec/item/store/list/all', headers=headers, params=data)

        return self.response

    def store_list(self, headers, lang: str = "en", zipcode: int | str = 98011):
        """# 查询sales_org 下的store列表 """
        data = {
            "zipcode": zipcode,
            "lang": lang
        }
        self.get(url='/ec/item/store/list', headers=headers, params=data)

        return self.response

    def store_is_select(self, headers, lang: str = "en", zipcode: int = 98011):
        """# 查询用户选择store信息 """
        data = {
            "zipcode": zipcode,
            "lang": lang
        }
        self.get(url='/ec/item/store/is_select', headers=headers, params=data)

        return self.response

    def store_select(self, headers, store_id: int = 2, zipcode: int|str = 98011,
                     is_selected: bool = True, is_remind: bool = False):
        """# 用户选择store """
        data = {
            "store_id": store_id,
            "is_selected": is_selected,
            "is_remind": is_remind,
            "zipcode": zipcode
        }

        self.post(url='/ec/item/store/select', headers=headers, json=data)

        return self.response

    def store_remind(self, headers, store_id: int = 2, zipcode: int|str = 98011,
                     is_selected: bool = True, is_remind: bool = False):
        """# 用户订阅store，添加提醒 """
        data = {
            "store_id": store_id,
            "is_selected": is_selected,
            "is_remind": is_remind,
            "zipcode": zipcode
        }
        self.post(url='/ec/item/store/remind', headers=headers, json=data)

        return self.response

    # central 接口

    def store_mapping_batch_add(self, headers, saleOrgIdList: list = [1, 2], storeIdList: list = [1, 2],
                                status: int = 1):
        """ # 添加store mapping """
        data = {
            "saleOrgIdList": saleOrgIdList,
            "storeIdList": storeIdList,
            "status": status
        }
        self.post(url='/central/im/store/mapping/batchAdd', headers=headers, json=data)

        return self.response

    def store_mapping_batch_update(self, headers, saleOrgIdList: list = [1, 2], storeIdList: list = [1, 2],
                                   status: int = 1):
        """# 更新store mapping """
        data = {
            "saleOrgIdList": saleOrgIdList,
            "storeIdList": storeIdList,
            "status": status
        }
        self.post(url='/central/im/store/mapping/batchUpdate', headers=headers, json=data)

        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
