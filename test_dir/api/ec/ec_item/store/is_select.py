import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath
import datetime


class IsSelect(weeeTest.TestCase):

    def is_select(self, headers, lang: str = "en", zipcode: int = 98011,
                  date: str = (datetime.date.today() + datetime.timedelta(days=1)).strftime('%Y-%m-%d')
                  ):
        """# is_select"""
        data = {
            "zipcode": zipcode,
            "date": date,
            "lang": lang,
            "limit": 10,
            "offset": 0

        }
        self.get(url='/ec/item/v3/search/brand_filter', headers=headers, params=data)

        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
