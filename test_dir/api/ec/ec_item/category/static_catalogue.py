import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath
import datetime


class StaticCatalogue(weeeTest.TestCase):

    def static_catalogue(self, headers, sales_org_id: str = "4", lang: str = "en"):
        """# 获取静态分类"""
        data = {
            "lang": lang,
            "sales_org_id": sales_org_id
        }
        self.get(url='/ec/item/catalogue/static_catalogues', headers=headers, params=data)

        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
