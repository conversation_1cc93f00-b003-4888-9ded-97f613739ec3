import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath
import datetime


class ApiCatalogues(weeeTest.TestCase):

    def catalogue_home(self, headers, type: str = "home_dynamic", lang: str = "en",zipcode:int=98011,
                       date: str = (datetime.date.today() + datetime.timedelta(days=1)).strftime('%Y-%m-%d'),
                       ):
        """# 获取分类列表 """
        # 0: "normal" 表示普通分类
        # *1: "special" 表示特殊的分类
        # *3: 餐馆菜
        # *4: 自定义链接
        data = {
            "type": type,  # shopping 响应里：1 "special"表示特殊的分类 0 "normal"表示普通分类
            "zipcode":zipcode,
            "lang": lang,
            "date": date
        }
        self.get(url='/ec/item/categories', headers=headers, params=data)

        return self.response

    def catalogue_special(self, headers, type: str = "special"):
        """# 获取特殊分类列表 """
        # 废弃了
        data = {
            "type": type  # 响应里：1 "special"表示特殊的分类 0 "normal"表示普通分类
        }
        self.get(url='/ec/item/categories', headers=headers, params=data)

        return self.response

    def catalogue_filter_normal(self, headers, type: str = "filter_normal"):
        """# 获取普通分类列表 """
        # 废弃了
        data = {
            "type": type  # 响应里：1 "special"表示特殊的分类 0 "normal"表示普通分类
        }
        self.get(url='/ec/item/categories', headers=headers, params=data)

        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
