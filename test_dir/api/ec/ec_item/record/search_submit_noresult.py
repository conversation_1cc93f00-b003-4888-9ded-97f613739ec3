import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath
import datetime


class SearchSubmitNoresult(weeeTest.TestCase):

    def search_submit_noresult(self, headers, noresult_keyword: str = "找找找找找找", zipcode: int = 98011):
        """# 提交空搜索想要的产品"""
        data = {
            "zipcode": zipcode,
            "name": noresult_keyword,
            "description": "test_test_test_test_test",
            "image_urls":  ["https://img06.test.weeecdn.com/item/image/491/489/E0AACB96BD9F762_0x0.png"]
            # "user_id": 0,
            # "brand": "",
            # "user_agent": "",
            # "images": "",
            # "device_id": "",
            #  "ip": "",

        }

        self.post(url='/ec/item/noresult', headers=headers, json=data)

        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
    user = SearchSubmitNoresult()
    user.search_submit_noresult({"term": "tofu"})
    # user.search_v3()
    # user.email_login(email='<EMAIL>', password='aa123456')

    # user.email_register(email='test.' + user.random_word(num=5) + '@sayweee.com', password='aa123456')
