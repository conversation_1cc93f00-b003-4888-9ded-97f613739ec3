import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath
import datetime


class SearchByCuisine(weeeTest.TestCase):

    def search_by_cuisine(self, headers, filterCuisine: str = None, lang: str = "en", zipcode: int = 98011,
                          date: str = (datetime.date.today() + datetime.timedelta(days=1)).strftime('%Y-%m-%d')
                          ):
        """# search_by_cuisine"""
        data = {
            "lang": lang,  # 非必传
            "zipcode": zipcode,
            "date": date,
            "filterCuisine": filterCuisine  # 非必传

        }
        self.get(url='/ec/item/v2/search/cuisine', headers=headers, json=data)

        return self.response

    def search_by_cuisine_home_page(self, headers, lang: str = "en", dataobject_key: str = None):
        """# search_by_cuisine_home_page"""
        data = {
            "page": 0,  # 非必传
            "pageSize": 5,  # 非必传
            "lang": lang,
            "dataobject_key": dataobject_key  # ds_item_editors_pick \ds_big_product_editors_pick_1822024

        }
        self.get(url='/ec/item/v2/search/cuisine/home', headers=headers, params=data)

        return self.response

    def search_by_cuisine_filter(self, headers, filterCuisine, lang: str = "en", zipcode: int = 98011,
                                 sort: str = "recommend", filters: str = None,
                                 date: str = (datetime.datetime.now() + datetime.timedelta(days=2)).strftime(
                                     '%Y-%m-%d'),
                                 ):
        """# search_by_cuisine_filter"""
        data = {
            "lang": lang,  # 非必传
            "zipcode": zipcode,
            "date": date,
            "filterCuisine": filterCuisine,  # 非必传
            "sort": sort,
            "filters": filters,
            "offset": 0,
            "limit": 2

        }
        self.get(url='/ec/item/v2/search/cuisine_filter', headers=headers, params=data)

        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
