import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath
import datetime


class SearchByIdsV2(weeeTest.TestCase):

    def search_by_ids_v2(self, headers):
        """# search_by_ids_v2"""
        data = {

        }
        self.post(url='/ec/item/v2/search/ids', headers=headers, params=data)

        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
