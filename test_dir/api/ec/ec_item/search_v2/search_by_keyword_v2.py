import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath
import datetime


class SearchByKeyWordV2(weeeTest.TestCase):

    def search_by_keyword_v2(self, headers, filter_key_word: str = "tofu", lang: str = "en", zipcode: int = 98011,
                             date: str = (datetime.date.today() + datetime.timedelta(days=1)).strftime('%Y-%m-%d'),
                             trigger_type: str = "search_popular"):

        """# 关键字搜索-接口已废弃不用"""
        data = {
            "filter_key_word": filter_key_word,
            "lang": lang,
            "zipCode": zipcode,
            "date": date,
            "trigger_type": trigger_type,
            "limit": 10,
            "offset": 0,
            "force": False
            # "filters": "",
            # "sort": "",
            # "tag_type": "",
            # "tag_id": "",
            # "sign": "",
            # "search_keyword": "",
            # "filter_biz_type": "",
            # "from_page": ""

        }
        self.get(url='/ec/item/v2/search', headers=headers, params=data)

        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
