import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath
import datetime


class SearchBy<PERSON>eywordAggs(weeeTest.TestCase):

    def search_by_keyword_aggs(self, headers, keyword: str = "tofu", lang: str = "en"):
        """# search_by_keyword_aggs"""
        data = {
            "keyword": keyword,
            "lang": lang


        }
        self.get(url='/ec/item/v2/search/aggs', headers=headers, params=data)

        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
