import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath
import datetime


class SearchByKeywordBizType(weeeTest.TestCase):

    def search_by_keyword_biz_type(self, headers, biz_type: str = "new", lang: str = "en", zipcode: int = 98011,
                                   date: str = (datetime.datetime.now() + datetime.timedelta(days=2)).strftime(
                                       '%Y-%m-%d'),
                                   trigger_type: str = "search_popular"):
        """# search_by_keyword_biz_type"""
        # biz_type：
        # public static final String NEW = "new";
        # public static final String SALE = "sale";
        # public static final String ALL = "all";
        # public static final String GLOBAL = "global";
        # public static final String GLOBAL_PC = "global_pc";

        data = {
            "biz_type": biz_type,
            "lang": lang,
            "zipcode": zipcode,
            "date": date,
            "trigger_type": trigger_type,
            "limit": 10

        }
        self.get(url='/ec/item/v2/search/biz_type', headers=headers, params=data)

        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
