import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath
import datetime


class QueryNavigateLabelList(weeeTest.TestCase):

    def query_navigate_label_list(self, headers, zipcode: int = 98011):
        """# query_navigate_label_list """
        data = {
            "zipcode": zipcode
        }
        self.get(url='/ec/content/navigate/label/update', headers=headers, params=data)

        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
