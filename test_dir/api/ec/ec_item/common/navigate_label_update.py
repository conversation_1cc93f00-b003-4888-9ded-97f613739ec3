import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath
import datetime


class NavigateLabelUpdate(weeeTest.TestCase):

    def navigate_label_update(self, headers, type: str = "home_dynamic", lang: str = "en",
               date: str = (datetime.date.today() + datetime.timedelta(days=1)).strftime('%Y-%m-%d'),
               ):
        """# update """
        data = {
            "start_time": 0,
            "time_range": "",
            "end_time": 0,
            "limit_content": "",
            "label_type": "",
            "navigate_config_id": 0,
            "frequency_type": "",
            "label_content": "",
            "priority": 0,
            "status": 0
        }
        self.get(url='/ec/content/navigate/label/update', headers=headers, params=data)

        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
