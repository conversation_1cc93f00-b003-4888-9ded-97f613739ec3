import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath
import datetime


class ClickNavigate(weeeTest.TestCase):

    def click_navigate(self, headers, zipcode: int = 98011, navigate_config_id: int = 0):
        """# query_navigate_label_list """
        data = {
            "zipcode": zipcode,
            "navigate_config_id": navigate_config_id
        }
        self.get(url='/ec/content/navigate/label/click', headers=headers, params=data)

        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
