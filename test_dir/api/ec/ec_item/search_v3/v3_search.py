import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath
import datetime


class V3Search(weeeTest.TestCase):
    def ec_search_keyword(self, headers, from_page: str = None, filter_key_word: str = "fruit", lang: str = "en",
                          trigger_type: str = "search_active",
                          date: str = (datetime.date.today() + datetime.timedelta(days=1)).strftime('%Y-%m-%d')
                          , zipcode: str = "98011", sort: str = "recommend", filters: dict = None):
        """# v3_search global搜索"""
        data = {
            "from_page": from_page,  # "mkpl_global_search",
            "zipcode": zipcode,
            "date": date,
            "lang": lang,
            "offset": 0,
            "limit": 10,
            "force": False,
            "filter_key_word": filter_key_word,
            "trigger_type": trigger_type,
            "filters": filters,
            "sort": sort
        }
        self.get(url='/ec/search/keyword', headers=headers, params=data)
        return self.response

    def ec_search_keyword_promo(self, headers, from_page: str = "use_page",  tag_id: int = None,
                          tag_type: str = "promo"):
        """ec search promo搜索 for global+大购物车"""
        data = {
            "from_page": from_page,
            "tag_id": tag_id,
            "tag_type": tag_type,
            "offset": 0,
            "limit": 10
        }
        self.get(url='/ec/search/keyword', headers=headers, params=data)
        return self.response

    def v3_search(self, headers, filter_key_word: str = "fruit", lang: str = "en",
                  date: str = (datetime.date.today() + datetime.timedelta(days=1)).strftime('%Y-%m-%d')
                  , zipcode: str = "98011"):
        """# v3_search global搜索"""
        data = {
            "from_page": "mkpl_global_search",
            "zipcode": zipcode,
            "date": date,
            "lang": lang,
            "offset": 0,
            "limit": 10,
            "force": "false",
            "filter_key_word": filter_key_word,

        }
        self.get(url='/ec/item/v3/search', headers=headers, params=data)
        return self.response

    def v3_search_category(self, headers, filter_key_word: str = "fruit", key: str = "dried", lang: str = "en",
                           date: str = (datetime.date.today() + datetime.timedelta(days=1)).strftime('%Y-%m-%d')
                           , zipcode: str = "98011"):
        """# v3_search global搜索,分类过滤"""
        data = {
            "from_page": "mkpl_global_search",
            "zipcode": zipcode,
            "date": date,
            "lang": lang,
            "offset": 0,
            "limit": 10,
            "force": "false",
            "filter_key_word": filter_key_word,
            "trigger_type": "taxonomy_" + key,
            "filters": {"catalogue_num": key}

        }
        self.get(url='/ec/item/v3/search', headers=headers, params=data)
        return self.response

    def v3_search_history(self, headers, filter_key_word: str = "fruit", lang: str = "en",
                          date: str = (datetime.date.today() + datetime.timedelta(days=1)).strftime('%Y-%m-%d')
                          , zipcode: str = "98011"):
        """# v3_search global搜索 历史搜索"""
        data = {
            "from_page": "mkpl_global_search",
            "zipcode": zipcode,
            "date": date,
            "lang": lang,
            "offset": 0,
            "limit": 10,
            "force": "false",
            "filter_key_word": filter_key_word,
            "trigger_type": "search_history"

        }
        self.get(url='/ec/item/v3/search', headers=headers, params=data)
        return self.response

    def v3_search_banner_category(self, headers, filter_sub_category: str, filters: dict,
                                  lang: str,
                                  date: str = (datetime.date.today() + datetime.timedelta(days=1)).strftime('%Y-%m-%d')
                                  , zipcode: str = "98011"):
        """# v3_search global搜索 历史搜索"""
        data = {
            "filter_sub_category": filter_sub_category,
            "zipcode": zipcode,
            "date": date,
            "lang": lang,
            "offset": 0,
            "limit": 12,
            "filters": filters

        }
        self.get(url='/ec/item/v3/search/catalogue', headers=headers, params=data)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
