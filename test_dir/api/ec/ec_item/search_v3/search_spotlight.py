import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath
import datetime


class SearchSpotlight(weeeTest.TestCase):

    def search_spotlight(self, headers, zipcode: int = 98011, lang: str = "en"):
        """# search_spotlight"""
        data = {
            "zipcode": zipcode,
            "lang": lang,
            "offset": 0,
            "offset": 10

        }
        self.get(url='/ec/item/v3/search/spotlight', headers=headers, json=data)

        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
