import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath
import datetime


class SearchByBrand(weeeTest.TestCase):

    def search_by_brand(self, headers, filterBrand: str = "2xOA0oYz", lang: str = "en", zipcode: int = 98011,
                        filters=None, sort: str = "recommend",
                        date: str = (datetime.date.today() + datetime.timedelta(days=1)).strftime('%Y-%m-%d')
                        ):
        """# search_by_brand"""
        if filters is None:
            filters = {}
        data = {
            "filterBrand": filterBrand,
            "zipcode": zipcode,
            "date": date,
            "lang": lang,
            "filters": filters,
            "sort": sort,
            "sign": "",
            "limit": 20,
            "offset": 0

        }
        self.get(url='/ec/item/v3/search/brand_filter', headers=headers, params=data)

        return self.response

    def recommend_card_brand_list(self, headers, dataobject_key: str = "ds_card_line_1008260", lang: str = "en",
                                  exclude_brand_key: str = None):
        """# 首页品牌接口/品牌详情其他品牌"""
        data = {
            "lang": lang,
            "dataobject_key: ": dataobject_key,
            "exclude_brand_key": exclude_brand_key  # 默认不传，传的话，就表示排除这个品牌之后其他品牌

        }
        self.get(url='/ec/item/v1/recommend/card/brand/list', headers=headers, params=data)

        return self.response

    def recommend_brand_product_list(self, headers, exclude_brand_key: str = "ds_card_line_1008260",
                                     catalogue: str = None):
        """# 指定品牌详情页面接口"""
        # recommend-brand 相关商品列表 （you may also like）
        data = {
            "exclude_brand_key: ": exclude_brand_key  # 要排除的brand_key
        }
        self.get(url='/ec/item/v1/recommend/brand/product/list', headers=headers, params=data)

        return self.response

    def recommend_brand_list(self, headers):
        """# 查看全部品牌落地页"""
        # recommend-brand 列表（聚合页）
        data = {
            "limit": 50,
            "offset": 0

        }
        self.get(url='/ec/item/v1/recommend/brand/list', headers=headers, params=data)

        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
