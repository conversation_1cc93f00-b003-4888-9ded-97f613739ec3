import datetime

import weeeTest
from weeeTest import weeeConfig


class SearchByKeywordV3(weeeTest.TestCase):

    def search_by_keyword_v3(self, headers, from_page: str = None, filter_key_word: str = "tofu",
                             lang: str = "en", zipcode: int = 98011, seller_id: int = None,
                             sort: str = "recommend", trigger_type: str = "search_popular", filters: str = None,
                             date: str = (datetime.date.today() + datetime.timedelta(days=1)).strftime('%Y-%m-%d')
                             ):
        """# 关键字搜索"""
        data = {
            "from_page": from_page,
            "filter_key_word": filter_key_word,
            "lang": lang,
            "zipcode": zipcode,
            "date": date,
            "trigger_type": trigger_type,
            "filters": filters,
            "sort": sort,
            "limit": 10,
            "offset": 0,
            "force": False,
            "seller_id": seller_id

            # "tag_type": "",
            # "tag_id": "",
            # "sign": "",
            # "search_keyword": "",
            # "filter_biz_type": "",
            #

        }
        self.get(url='/ec/item/v3/search', headers=headers, params=data)

        return self.response

    def mkpl_global_search_by_keyword_v3(self, headers, date, filter_key_word: str = "japan", force=0,
                                         from_page: str = 'mkpl_global_search', lang: str = "en", limit: int = 20,
                                         offset: int = 0,
                                         sign="7180B802201D6AC3CE0F67EAF5897A03", trigger_type="search_active",
                                         zipcode="94538"):
        """
        marketplace 首页搜索
        :param headers:
        :param date:
        :param filter_key_word:
        :param force:
        :param from_page:
        :param lang:
        :param limit:
        :param offset:
        :param sign:
        :param trigger_type:
        :param zipcode:
        :return:
        """
        data = {
            "filter_key_word": filter_key_word,
            "lang": lang,
            "zipcode": zipcode,
            "date": date,
            "trigger_type": trigger_type,
            "limit": limit,
            "offset": offset,
            "force": force,
            "from_page": from_page,
            "sign": sign
        }
        self.get(url='/ec/item/v3/search/', headers=headers, params=data)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
