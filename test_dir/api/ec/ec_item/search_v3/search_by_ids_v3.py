import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath
import datetime


class SearchByIdsV3(weeeTest.TestCase):

    def search_by_ids_v3(self, headers, source, ids, source_key: bool = False, from_page: str = "cms_page",
                             sales_org_id: int = 4,
                             lang: str = "en", zipcode: int = 98011,
                             date: str = (datetime.date.today() + datetime.timedelta(days=1)).strftime('%Y-%m-%d'),
                             dataobject_key: str = None
                         ):
        """# search_by_ids_v3"""
        data = {
            "date": date,
            # "domain": "",
            # "enableFilterEthnic": True,
            # "filter_change_date": True,
            # "filter_no_sale": True,
            # "filter_sold_out": True,
            "from_page": from_page,
            "ids": ids,
            "lang": lang,
            "offset": 0,
            "limit": 10,
            # "sales_org_id": sales_org_id,
            # "sort": "",
            "source": source,
            "source_key": source_key,
            # "sub_catalogue_nums": [],
            "zipcode": zipcode
        }
        self.post(url='/ec/item/v3/search/ids', headers=headers, json=data)

        return self.response

    def search_by_ids_cms_v3(self, headers, source, ids, source_key: bool = False, from_page: str = "cms_page",
                             sales_org_id: int = 4,
                             lang: str = "en", zipcode: int = 98011,
                             date: str = (datetime.date.today() + datetime.timedelta(days=1)).strftime('%Y-%m-%d'),
                             dataobject_key: str = None):
        """# search_by_ids_cms_v3"""
        data = {
            "from_page": from_page,
            "source": source,
            "filter_sold_out": True,
            "ids": ids,
            "source_key": source_key,
            "zipcode": zipcode,
            "lang": lang,
            "date": date,
            "dataobject_key": dataobject_key
            #
            # "domain": "",
            # "enableFilterEthnic": True,
            # "filter_change_date": True,
            # "filter_no_sale": True,
            # "offset": 0,
            # "limit": 10,
            # "sales_org_id": sales_org_id,
            # "sort": "",
            # "sub_catalogue_nums": []

        }
        self.get(url='/ec/item/v3/search/ids', headers=headers, params=data)

        return self.response

    def search_by_ids_mini_v3(self, headers, sales_org_id: int = 4, lang: str = "en", zipcode: int = 98011,
                              date: str = (datetime.date.today() + datetime.timedelta(days=1)).strftime('%Y-%m-%d')
                              ):
        """# search_by_ids_mini_v3"""
        data = {
            "date": date,
            "domain": "",
            "enableFilterEthnic": True,
            "filter_change_date": True,
            "filter_no_sale": True,
            "filter_sold_out": True,
            "from_page": "",
            "ids": [],
            "lang": lang,
            "offset": 0,
            "limit": 10,
            "sales_org_id": sales_org_id,
            "sort": "",
            "source": "",
            "source_key": "",
            "sub_catalogue_nums": [],
            "zipcode": zipcode
        }
        self.post(url='/ec/item/v3/search/ids/mini', headers=headers, json=data)

        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
