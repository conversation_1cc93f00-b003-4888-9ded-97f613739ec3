from __future__ import annotations

import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath
import datetime


class SearchByCatalogueContent(weeeTest.TestCase):

    def search_by_catalogue_content(self, headers, filter_sub_category, filters: dict | str = None,
                                    sort: str = "recommend", default_sort_key: str = None, cuisine: str = None,
                                    filter_ethnic: str = None,
                                    lang: str = "en", zipcode: int | str = 98011,
                                    limit: int = 30, out_filters: dict | str = None,
                                    date: str = (datetime.datetime.now() + datetime.timedelta(days=2)).strftime(
                                        '%Y-%m-%d')
                                    ):
        """# 普通分类搜索：search_by_catalogue_content"""
        data = {
            "filter_sub_category": filter_sub_category,  # ["fruits"]
            "lang": lang,
            "zipcode": zipcode,
            # "sales_org_id": sales_org_id,
            "filters": filters,  # {"delivery":true}
            "sort": sort,  # ["recommend","sold_count","price_asc","price_desc"]
            "date": date,
            "sign": "",
            "from_page": "",
            "limit": limit,
            "page": 1,
            "default_sort_key": default_sort_key,
            "cuisine": cuisine,
            "filter_ethnic": filter_ethnic,
            "out_filters": out_filters

        }

        self.get(url='/ec/item/v3/search/content/catalogue', headers=headers, params=data)

        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
