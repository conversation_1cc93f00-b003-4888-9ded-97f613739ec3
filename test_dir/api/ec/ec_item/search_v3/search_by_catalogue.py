import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath
import datetime


class SearchByCatalogue(weeeTest.TestCase):

    def search_by_catalogue(self, headers, filter_sub_category, dataobject_key, filters: str = None,
                            sort: str = None, default_sort_key: str = None, cuisine: str = None,
                            filter_ethnic: str = None, lang: str = "en", zipcode: int = 98011,
                            sales_org_id: int = 4, from_page: str = "home",
                            date: str = (datetime.date.today() + datetime.timedelta(days=1)).strftime('%Y-%m-%d')
                            ):
        """# 首页特殊分类搜索：search_by_catalogue"""
        data = {
            "filter_sub_category": filter_sub_category,  # [trending,new,sale]
            "lang": lang,
            "zipcode": zipcode,
            "sales_org_id": sales_org_id,
            "filters": filters,
            "sort": sort,
            "date": date,
            "sign": "",
            "from_page": from_page,
            "limit": 20,
            "offset": 0,
            "default_sort_key": default_sort_key,
            "cuisine": cuisine,
            "filter_ethnic": filter_ethnic,
            "dataobject_key": dataobject_key  # [ds_item_trending,ds_item_new,ds_item_sale,ds_product_bakery_new]

        }
        self.get(url='/ec/item/v3/search/catalogue', headers=headers, params=data)

        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
