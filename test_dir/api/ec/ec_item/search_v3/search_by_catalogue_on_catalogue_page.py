import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath
import datetime


class SearchByCatalogueOnCataloguePage(weeeTest.TestCase):

    def search_by_catalogue_on_catalogue_page(self, headers, special_num: str, catalogue_num: str):
        """# 特殊分类下普通一级分类商品获取"""
        data = {
            "special_num": special_num,  # 必填
            "catalogue_num": catalogue_num,  # 必填
            "offset": 10

        }
        self.get(url='/ec/item/v3/search/catalogue/special/' + str(special_num), headers=headers, params=data)

        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
