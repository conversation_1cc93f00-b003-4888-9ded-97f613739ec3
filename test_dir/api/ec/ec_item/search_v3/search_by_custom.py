import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath
import datetime


class SearchByCustom(weeeTest.TestCase):

    def search_by_custom(self, headers, sales_org_id: int = 4, lang: str = "en", zipcode: int = 98011,
                         date: str = (datetime.date.today() + datetime.timedelta(days=1)).strftime('%Y-%m-%d')
                         ):
        """# search_by_custom"""
        data = {
            "date": date,
            "domain": "",
            "enableFilterEthnic": True,
            "filter_change_date": True,
            "filter_no_sale": True,
            "filter_sold_out": True,
            "from_page": "",
            "ids": [],
            "lang": lang,
            "offset": 10,
            "offset": 0,
            "sales_org_id": sales_org_id,
            "sort": "",
            "source": "",
            "source_key": "",
            "sub_catalogue_nums": [],
            "zipcode": zipcode
        }
        self.post(url='/ec/item/v3/search/custom', headers=headers, json=data)

        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
