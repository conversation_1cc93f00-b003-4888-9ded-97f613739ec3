import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath
import datetime


class SearchModelPage(weeeTest.TestCase):

    def search_model_page(self, headers, zipcode: int = 98011,
                          date: str = (datetime.date.today() + datetime.timedelta(days=1)).strftime('%Y-%m-%d')
                          ):
        """# search_model_page"""
        data = {
            "zipcode": zipcode,
            "date": date,
            "page_num": 1,
            "sort": "",
            "offset": 0,
            "offset": 10

        }
        self.get(url='/ec/item/v3/search/model_page', headers=headers, json=data)

        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
