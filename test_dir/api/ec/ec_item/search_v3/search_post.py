import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath
import datetime


class SearchPost(weeeTest.TestCase):

    def search_post_v1(self, headers, search_keyword: str = "tofu", zipcode: int = 98011, lang: str = "en",
                       date: str = (datetime.date.today() + datetime.timedelta(days=1)).strftime('%Y-%m-%d')
                       ):
        """# search_post"""
        data = {
            "search_keyword": search_keyword,
            "zipcode": zipcode,
            "date": date,
            "lang": lang,
            "sort": "",
            "offset": 0,
            "offset": 10

        }
        self.get(url='/ec/item/v3/search/post', headers=headers, json=data)

    def search_post_v2(self, headers, filter_key_word: str = "tofu", zipcode: int = 98011, lang: str = "en",
                       date: str = (datetime.date.today() + datetime.timedelta(days=1)).strftime('%Y-%m-%d')
                       ):
        """# search_post"""
        data = {
            "filter_key_word": filter_key_word,
            "zipcode": zipcode,
            "date": date,
            "lang": lang,
            "filters": "",
            "sort": "",
            "offset": 0,
            "force": False,
            "search_module": "",
            "offset": 10

        }
        self.get(url='/ec/item/v3/search/v2/post', headers=headers, json=data)

        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
