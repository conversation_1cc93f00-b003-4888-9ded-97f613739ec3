import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath
import datetime


class SearchByPicture(weeeTest.TestCase):

    def search_by_picture_v1(self, headers, zipcode: int = 98011,
                             image_url: str = "https://img06.weeecdn.com/item/image/730/718/40666F9F7BB4B65E.jpg!c750x0.auto",
                             date: str = (datetime.date.today() + datetime.timedelta(days=1)).strftime('%Y-%m-%d')
                             ):
        """# search_by_picture_v1"""
        data = {
            "image_url": image_url,
            "zipcode": zipcode,
            "date": date,
            "sign": "",
            "filters": "",
            "sort": "",
            "offset": 0,
            "limit": 10

        }
        self.get(url='/ec/item/v3/search/pic', headers=headers, json=data)

        return self.response

    def search_by_picture_v2(self, headers, zipcode: int = 98011,
                             image_url: str = "https://img06.weeecdn.com/item/image/730/718/40666F9F7BB4B65E.jpg!c750x0.auto",
                             date: str = (datetime.date.today() + datetime.timedelta(days=1)).strftime('%Y-%m-%d')
                             ):
        """# search_by_picture_v2"""
        data = {
            "image_url": image_url,
            "zipcode": zipcode,
            "base64": "",
            "date": date,
            "sign": "",
            "filters": "",
            "sort": "",
            "offset": 0,
            "limit": 10

        }
        self.post(url='/ec/item/v3/search/pic/v2', headers=headers, json=data)

        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
