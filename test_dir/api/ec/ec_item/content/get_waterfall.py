import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath
import datetime


class GetHomeWaterfall(weeeTest.TestCase):

    def get_home_waterfall(self, headers, recommend_session, page_num: int = 1):
        """ 首页waterfall接口 """
        data = {
            # "dataobject_key": dataobject_key,
            "page_num": page_num,
            "recommend_session": recommend_session
        }
        self.get(url='/ec/item/v1/content/feed/home/<USER>', headers=headers, params=data)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
