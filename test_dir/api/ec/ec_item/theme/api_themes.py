import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath
import datetime


class SearchThemes(weeeTest.TestCase):

    # 主题（一期）
    def themes_info_v3(self, headers, tag_id: int = 1, need_i18n: bool = False):
        """# theme信息查询 - share 服务调用 """
        data = {
            "tag_id": tag_id,
            "need_i18n": need_i18n

        }
        self.get(url='/ec/item/v3/themes/' + str(tag_id) + '/info', headers=headers, params=data)

        return self.response

    def search_themes(self, headers, tag_id: int = 1, zipcode: int = 98011, lang: str = "en",
                      sort: str = "recommend",
                      date: str = (datetime.date.today() + datetime.timedelta(days=1)).strftime('%Y-%m-%d')
                      ):
        """# theme商品列表查询 """

        data = {
            "tag_id": tag_id,
            "zipcode": zipcode,
            "date": date,
            "lang": lang,
            "sort": sort,
            "offset": 0,
            "limit": 10

        }
        self.get(url='/ec/item/v3/search/themes', headers=headers, params=data)

        return self.response

    def themes_v3(self, headers, lang: str = "en", from_page: str = "theme_landing",
                  date: str = (datetime.date.today() + datetime.timedelta(days=1)).strftime('%Y-%m-%d')
                  ):
        """# 有效主题列表查询 - home页面调用 """
        data = {
            "date": date,
            "lang": lang,
            "from_page": from_page

        }
        self.get(url='/ec/item/v3/themes/simple', headers=headers, params=data)

        return self.response

    def themes_items_v3(self, headers, tag_id: int = 1, zipcode: int = 98011, lang: str = "en",
                        date: str = (datetime.date.today() + datetime.timedelta(days=1)).strftime('%Y-%m-%d')
                        ):
        """# theme页 商品聚合查询 """
        data = {
            "date": date,
            "lang": lang,
            "tag_id": tag_id,
            "zipcode": zipcode

        }
        self.get(url='/ec/item/v3/themes/items', headers=headers, params=data)
        return self.response

    def cms_page_getPage(self, headers, page_type: str = "theme", page_key: int = 1):
        """# cms_page_getPage"""
        data = {
            "page_type": page_type,
            "page_key": page_key

        }
        self.get(url='/ec/content/cms/page/getPage', headers=headers, params=data)

        return self.response

    def themes_collection_carousel_v4(self, headers, tag_id: int = 1, lang: str = "en",
                                      date: str = (datetime.datetime.now() + datetime.timedelta(days=2)).strftime(
                                          '%Y-%m-%d')
                                      ):
        """# 主题页面 横向合集：editor‘s picks"""
        data = {
            "tag_id": tag_id,
            "lang": lang,
            "date": date

        }
        self.get(url='/ec/item/v4/themes/collection/carousel', headers=headers, params=data)

        return self.response

    # 主题（二期）
    def themes_list_v4(self, headers):
        """# 查询主题聚合页列表v4"""
        data = None
        self.get(url='/ec/item/v4/themes/list', headers=headers, params=data)

        return self.response

    def themes_info_v4(self, headers, tag_id: int = 1, need_i18n: bool = False):
        """# 查询主题详情信息v4"""
        # need_i18n: 是否需要多语言
        data = {"need_i18n": need_i18n}
        self.get(url='/ec/item/v4/themes/' + str(tag_id) + '/info', headers=headers, params=data)

        return self.response

    def themes_products_v4(self, headers, data):
        """# 查询主题商品v4 """
        self.get(url='/ec/item/v4/themes/products', headers=headers, params=data)
        return self.response

    def top_themes(self, headers):
        """# 查询topics的主题 """
        self.get(url='/ec/social/topic/list', headers=headers)
        return self.response
