import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath
import datetime


class GetProductsModules(weeeTest.TestCase):

    def get_products_modules(self, headers, product_id: int = 9819, page: str = "pdp"):
        """ get_products_modules """
        data = {
            "product_id": product_id,
            "limit": 10,
            "page": page
        }
        self.get(url='/ec/item/v2/items/' + str(product_id) + '/modules', headers=headers, params=data)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
