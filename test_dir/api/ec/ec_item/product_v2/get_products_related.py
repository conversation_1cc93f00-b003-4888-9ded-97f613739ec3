import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath
import datetime


class GetProductsRelated(weeeTest.TestCase):

    def get_products_related(self, headers, product_id):
        """ get_products_related """
        data = {
            "product_id": product_id,
            "limit": 5,
            "page": 1
        }
        self.get(url='/ec/item/v2/items/' + str(product_id) + '/related', headers=headers, params=data)

        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
