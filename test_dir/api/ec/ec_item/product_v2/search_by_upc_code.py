import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath
import datetime


class SearchByUpcCode(weeeTest.TestCase):

    def search_by_upc_code(self, headers, upc_code: str = None, related_sku_limit: str = None):
        """ search_by_upc_code """
        data = {
            "upc_code": upc_code,  # 非必填
            "related_sku_limit": related_sku_limit  # 非必填

        }
        self.get(url='/ec/item/v2/items/upc_code', headers=headers, params=data)

        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
