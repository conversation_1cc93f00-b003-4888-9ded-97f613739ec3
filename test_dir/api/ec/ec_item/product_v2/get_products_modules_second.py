import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath
import datetime


class GetProductsModulesSecond(weeeTest.TestCase):

    def get_products_modules_second(self, headers, product_id, from_page: str = None):
        """ me页面/PDP页面Recently Viewed模块 """
        data = {
            "product_id": product_id,
            "from_page": from_page,
            "limit": 10,
            "page": 1
        }
        self.get(url='/ec/item/v2/items/' + str(product_id) + '/modules/second', headers=headers, params=data)
        return self.response

    def recently_view_support(self, headers, product_id, from_page):
        """ 是否支持recently view """
        data = None
        self.get(url='/ec/item/v1/customer/view/support', headers=headers, params=data)
        return self.response

    def recently_view_waterfall(self, headers, product_id,
                                recommend_session: str = "e579a1b8-0b0d-4828-bbf5-292b59ecb962"):
        """ PDP页面Recently Viewed waterfall模块 """
        data = {
            "product_id": product_id,
            "recommend_session": recommend_session,
            "limit": 20,
            "page_num": 0
        }
        self.get(url='/ec/item/v2/items/waterfall', headers=headers, params=data)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
