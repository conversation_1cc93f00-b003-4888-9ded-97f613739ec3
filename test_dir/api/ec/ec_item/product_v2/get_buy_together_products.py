import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath
import datetime


class GetBuyTogetherProducts(weeeTest.TestCase):

    def get_buy_together_products(self, headers, product_id):
        """ get_buy_together_products """
        data = {
            "product_id": product_id,
            "limit": 5,
            "page": 1
        }
        self.get(url='/ec/item/v2/items/' + str(product_id) + '/buy_together', headers=headers, params=data)

        return self.response



if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
