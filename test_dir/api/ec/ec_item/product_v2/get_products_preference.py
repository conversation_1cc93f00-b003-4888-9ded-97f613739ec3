import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath
import datetime


class GetProductsPreference(weeeTest.TestCase):

    def get_products_recommend(self, headers, product_id):
        """ pdp -get_products_preference """
        data = {
            "product_id": product_id,
            "limit": 5,
            "page": "pdp"
        }
        self.get(url='/ec/item/v2/items/' + str(product_id) + '/recommend', headers=headers, params=data)

        return self.response

    def get_products_preference(self, headers, product_id):
        """ catalogue -get_products_preference """
        data = {
            "page": "catalogue"
        }
        self.get(url='/ec/item/v2/items/' + str(product_id) + '/preference', headers=headers, params=data)

        return self.response

if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
