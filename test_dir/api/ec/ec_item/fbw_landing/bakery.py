from typing import Optional

import weeeTest


class BakeryLadingPage(weeeTest.TestCase):
    def page_cms(self, headers, lang: str, sales_org_id: int, zipcode: str,
                 store_key: Optional[str] = None,
                 mode: Optional[str] = None, page_key='bakery', page_type='8'):
        '''
        bakery landing page cms config
        '''
        data = {"page_key": page_key, "page_type": page_type,
                "lang": lang, "sales_org_id": sales_org_id, "zipcode": zipcode
                }
        if mode:
            data['mode'] = mode
        if store_key:
            data['store_key'] = store_key
        self.get(url='/ec/content/cms/page/getPage', headers=headers, params=data)
        return self.response

    def ds_product_list_new(self):
        '''
        bakery landing page  New arrival
        '''

        pass

    def ds_nav_line(self):
        """
        shop by bakery
        """
        pass

    def ds_content_feed_v2(self):
        """
        fbw  feed
        """
        pass

    def ds_product_list_buy_again(self):
        """
        fbw buy again
        :return:
        """
        pass
