import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath
import datetime


class PdpDetail(weeeTest.TestCase):

    def pdp_detail(self, headers, product_id: int = 9819, sales_org_id: int = 4, zipcode: int = 98011,
                   category: str = None):
        """ PDP detail """
        data = {
            "product_id": product_id,
            "sales_org_id": sales_org_id,  # 非必填
            "zipcode": zipcode,  # 非必填
            "category": category  # 非必填
        }
        self.get(url='/ec/item/items/detail', headers=headers, params=data)

        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
