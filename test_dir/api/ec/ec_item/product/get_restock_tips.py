import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath
import datetime


class GetRestockTips(weeeTest.TestCase):

    def get_restock_tips(self, headers, product_ids, lang: str = "en", sales_org_id: int = 4):
        """ get_restock_tips """
        data = {
            "product_ids": product_ids,
            "lang": lang,
            "sales_org_id": sales_org_id
        }
        self.post(url='/ec/item/items/restock/tips', headers=headers, json=data)

        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
