import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath
import datetime


class GetDeliveryDate(weeeTest.TestCase):

    def get_delivery_date(self, headers, product_id):
        """ Get delivery date """
        data = {
            "product_id": product_id
        }
        self.get(url='/ec/item/items/delivery_date', headers=headers, params=data)

        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
