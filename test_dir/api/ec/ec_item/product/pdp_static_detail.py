import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath
import datetime


class PdpStaticDetail(weeeTest.TestCase):

    def pdp_static_detail(self, headers, product_id, lang: str = "en"):
        """ pdp_static_detail """
        data = {
            "product_id": product_id,
            "lang": lang
        }
        self.get(url='/ec/item/items/detail/static', headers=headers, params=data)

        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
