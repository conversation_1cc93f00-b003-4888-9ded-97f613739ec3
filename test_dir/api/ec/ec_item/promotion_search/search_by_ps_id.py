import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath
import datetime


class SearchByPsId(weeeTest.TestCase):

    def search_by_ps_id(self, headers, 	ps_id: int = 7054):
        """ search_by_ps_id """
        data = {
            "	ps_id": ps_id
        }
        self.get(url='/ec/item/promotion/search', headers=headers, params=data)

        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
