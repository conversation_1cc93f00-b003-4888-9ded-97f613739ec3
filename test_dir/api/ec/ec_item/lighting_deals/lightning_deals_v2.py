import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath
import datetime


class ApiLightningDeals(weeeTest.TestCase):

    def lightning_deals_home(self, headers, zipcode: int = 98011,
                             date: str = datetime.date.today().strftime('%Y-%m-%d'),
                             from_page: str = "page_home"):
        """ 秒杀商品列表(首页) """
        data = {
            "zipcode": zipcode,
            "limit": 10,
            "date": date,
            "from_page": from_page

        }
        self.get(url="/ec/item/lightning_deals/product_list/component", headers=headers, params=data)
        return self.response

    def lightning_deals_info(self, headers, zipcode: int = 98011,
                             date: str = datetime.date.today().strftime('%Y-%m-%d'),
                             from_page: str = "page_home "):
        """ 秒杀活动信息查询 - 列表页调用 """
        data = {
            "zipcode": zipcode,
            "date": date

        }
        self.get(url="/ec/item/lightning_deals/info", headers=headers, params=data)
        return self.response

    def lightning_deals_product_list(self, headers, filter: str = None, zipcode: int | str = 98011, status: int = 0,
                                     date: str = datetime.date.today().strftime('%Y-%m-%d')
                                     ):
        """ 秒杀商品列表查询 - 列表页 """
        data = {
            "status": status,  # 0 表示未开始,1 表示已开始
            "zipcode": zipcode,
            "date": date,
            "filter": filter,
            "limit": 24,
            "offset": 0

        }
        self.get(url="/ec/item/lightning_deals/product_list", headers=headers, params=data)
        return self.response

    def lightning_deals_remind_me(self, headers, product_id, status: str = "A", ):
        """ 秒杀商品提醒 """
        data = {
            "product_id": product_id,
            "status": status
        }
        self.post(url="/ec/mkt/activity/lightning_deals/remind", headers=headers, json=data)
        return self.response

    def lightning_deals_remind_me_v2(self, headers, product_id, status: str = "A", ):
        """ 秒杀商品提醒 """
        data = {
            "product_id": product_id,
            "status": status
        }
        self.post(url="/ec/item/lightning_deals/remind", headers=headers, json=data)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
