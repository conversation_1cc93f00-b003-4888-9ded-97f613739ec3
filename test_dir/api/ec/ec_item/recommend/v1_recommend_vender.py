import weeeTest
from weeeTest import weeeConfig
from test_dir.api.ec.ec_customer.login_rest.email_login import EmailLogin


# headers = EmailLogin().email_login()
# print(headers=headers)
# vender_id = 7319
# product_id = 2006149


class V1_recommend_vender(weeeTest.TestCase):

    def v1_recommend_vender(self, headers, vender_id: int = 7054, filter_product_id: str = "1968253"):
        """  # v1_recommend_vender 商家商品详情的店铺推荐 """
        data = {"vender_id": vender_id,
                "filter_product_id": filter_product_id,
                "limit": 10}
        self.get(url='/ec/item/v1/recommend/vender', headers=headers, params=data)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
    user = V1_recommend_vender()
