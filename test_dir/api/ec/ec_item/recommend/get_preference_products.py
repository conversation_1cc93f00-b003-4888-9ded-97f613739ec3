import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath
import datetime


class GetPreferenceProducts(weeeTest.TestCase):
    def get_preference_products(self, headers):
        """ 首页 """
        data = {
            "limit": 10,  # 非必传
            "offset": 0
        }
        self.get(url='/ec/item/v1/recommend/recommend', headers=headers, params=data)

        return self.response

    def get_preference_products_cart(self, headers, limit: int = 20):
        """ 购物车猜你喜欢 """
        data = {
            "limit": limit,  # 非必传
            "offset": 0  # 非必传
        }
        self.get(url='/ec/item/v1/recommend/cart/preference', headers=headers, params=data)
        return self.response

    def get_preference_products_home(self, headers):
        """ 首页猜你喜欢 """
        data = {
            "limit": 10,  # 非必传
            "offset": 0  # 非必传
        }
        self.get(url='/ec/item/v1/recommend/home/<USER>', headers=headers, params=data)

        return self.response

    def get_preference_products_seller_cart(self, headers, seller_id):
        """ MKPL 商家购物车猜你喜欢 """
        self.get(url=f'/ec/item/v1/recommend/modules/cart/seller/{seller_id}', headers=headers, params=None)

        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
