import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath
import datetime


class GetBreakfastProducts(weeeTest.TestCase):
    def get_breakfast_products(self, headers):
        """ get_breakfast_products """
        data = None
        self.get(url='/ec/item/v1/recommend/home/<USER>', headers=headers, params=data)

        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
