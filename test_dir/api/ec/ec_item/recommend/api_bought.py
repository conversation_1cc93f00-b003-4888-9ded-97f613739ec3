import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath
import datetime


class GetBoughtProducts(weeeTest.TestCase):
    def get_bought_products(self, headers):
        """ 曾经购买simple--搜索页面、订单页面 """
        data = {
            "limit": 3  # 非必传
        }
        self.get(url='/ec/item/v1/recommend/bought/simple', headers=headers, params=data)

        return self.response

    def get_bought_products_list(self, headers, filter_sub_category: str = None):
        """ 曾经购买--mylist页面(我的清单) """
        data = {
            "filter_sub_category": filter_sub_category,  # 非必传
            "limit": 10,  # 非必传
            "offset": 0  # 非必传
        }
        self.get(url='/ec/item/v1/recommend/bought/catalogue', headers=headers, params=data)

        return self.response

    def get_bought_products_cart(self, headers):
        """ 曾经购买--购物车页面 """
        data = {
            "limit": 10,  # 非必传
            "offset": 0  # 非必传
        }
        self.get(url='/ec/item/v1/recommend/bought', headers=headers, params=data)

        return self.response

    def get_bought_products_orderlist(self, headers, exclude_product_ids: str = None,
                                      from_page: str = "order_confirmation"):
        """ 曾经购买--订单详情页面adv实验 """
        data = {
            "offset": 0,
             "limit": 12,
             "from_page": from_page,
             "exclude_product_ids": exclude_product_ids
        }
        self.get(url='/ec/item/v1/recommend/bought/v2', headers=headers, params=data)

        return self.response

    def get_bought_products_social(self, headers):
        """ 曾经购买--community定制 """
        data = {
            "limit": 10,  # 非必传
            "offset": 0  # 非必传
        }
        self.get(url='/ec/item/v1/recommend/bought/list/for_social', headers=headers, params=data)

        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
