import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath
import datetime


class RecommendVender(weeeTest.TestCase):
    def recommend_vender(self, headers, vender_id, filter_product_id):
        """ recommend_vender """
        data = {
            "vender_id": vender_id,
            "filter_product_id": filter_product_id,
            "limit": 10

        }
        self.get(url='/ec/item/v1/recommend/vender', headers=headers, params=data)

        return self.response




if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
