import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath
import datetime


class RecommendScenes(weeeTest.TestCase):
    def recommend_content(self, headers):
        """ recommend_content """
        data = None
        self.get(url='/ec/item/v1/recommend/content', headers=headers, params=data)

        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
