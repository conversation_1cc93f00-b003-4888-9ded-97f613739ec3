import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath
import datetime


class GetNewForYouProducts(weeeTest.TestCase):
    def get_new_for_you_products(self, headers):
        """ get_new_for_you_products """
        data = {
            "limit": 20  # 非必传
        }
        self.get(url='/ec/item/v1/recommend/new_for_you', headers=headers, params=data)

        return self.response

    def get_new_for_you_products_trade(self, headers):
        """ get_new_for_you_products_trade """
        data = {
            "limit": 20  # 非必传
        }
        self.get(url='/ec/item/v1/recommend/new_for_you/trade', headers=headers, params=data)

        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
