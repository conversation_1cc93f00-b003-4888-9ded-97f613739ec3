import weeeTest
from weeeTest import weeeConfig
from test_dir.api.ec.ec_customer.login_rest.email_login import EmailLogin


class CategoriesGlobal(weeeTest.TestCase):
    """ # 获取waterfall的分类 """

    def categories_global(self, headers):
        """ 获取global+首页分类列表 """
        data = None
        self.get(url='/ec/item/categories/global', headers=headers, params=data)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
    user = CategoriesGlobal()
