from typing import Optional
import json
import weeeTest
from weeeTest import weeeConfig


class SearchVender(weeeTest.TestCase):

    def v3_search_vender(self, headers, vender_id: int = 6887, biz_type: str = "mkpl",
                         from_page: str = "cms_seller_home", lang: str = "en", zipcode: str = "98011",
                         dataobject_key: str = None, sort: str = "recommend", filters: dict = None):
        data = {"vender_id": vender_id,
                "filters": filters,
                "from_page": from_page,
                "filter_sold_out": True,
                "filter_change_date": True,
                "biz_type": biz_type,
                "lang": lang,
                "dataobject_key": dataobject_key,
                "sort": sort,
                "zipcode": zipcode
                }
        self.get(url='/ec/item/v3/search/vender', headers=headers, params=data)
        return self.response

    def v3_search_vender_all(self, headers, vender_id: int, dataobject_key: str = None,
                             date: str = "2024-11-21", lang: str = "en", zipcode='98011',
                             biz_type: Optional[str] = None):
        data = {"vender_id": vender_id,
                "from_page": "cms_seller_home",
                "dataobject_key": dataobject_key,
                "biz_type": biz_type,
                "zipcode": zipcode,
                "filter_sold_out": True,
                "filter_change_date": True,
                "lang": lang,
                "date": date
                }
        self.get(url='/ec/item/v3/search/vender', headers=headers, params=data)
        return self.response

    def vender_all_tab_products(self, headers, vender_id: int,
                                date: str,
                                sign: str, offset: int = 0, limit: int = 20, filters: dict = None, sort: str = None):
        """
        seller page all product tab
        :param sort: seller 商品排序选项
        :param filters: seller 商品筛选选项
        :param headers: common header
        :param vender_id: seller id
        :param date: porder data
        :param sign: front end params md5
        :param offset:
        :param limit:
        :return: page all product content
        """
        param = {"vender_id": vender_id,
                 "date": date,
                 "lang": headers['Lang'],
                 "limit": limit,
                 "offset": offset,
                 "sign": sign,
                 "zipcode": headers['Zipcode']
                 }
        if filters:
            json_str = json.dumps(filters)
            filter_str = str(json_str)
            param["filters"] = filter_str
        if sort:
            param["sort"] = sort
        self.get(url='/ec/item/v3/search/vender', headers=headers, params=param)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
    user = SearchVender()
