import uuid
import weeeTest
from weeeTest import weeeConfig


# https://www.wetools.com/uuid
# https://docs.python.org/zh-cn/3/library/uuid.html
# recommend_session = str(uuid.uuid4())
# recommend_session = f'{uuid.uuid4()}'
# 大写uuid;id=str(uuid.uuid4()).upper()
# uuid是128位的全局唯一标识符
# uuid.uuid1()可以保证全球范围内的唯一性
# uuid.uuid2()目前python没有这个方法
# uuid.uuid3(namespace, name)　　通过计算一个命名空间和名字的md5散列值来
# uuid.uuid4()　通过伪随机数得到uuid，是有一定概率重复的
# uuid.uuid5(namespace, name)　　和uuid3基本相同，只不过采用的散列算法是sha1

class V1ContentFeedSearchGlobal(weeeTest.TestCase):
    """ # 获取waterfall的瀑布流 """

    def v1_content_feed_search_global(self, headers, recommend_session: str):
        data = {"recommend_session": recommend_session, "page_num": 1}
        self.get(url='/ec/item/v1/content/feed/search/global', headers=headers, params=data)
        return self.response

    """ # 获取waterfall的分类瀑布流 """

    def v1_content_feed_search_global_category(self, headers, recommend_session: str, category_nums: str):
        """ # 获取waterfall的分类瀑布流 v1_content_feed_search_global_category"""
        data = {"recommend_session": recommend_session, "category_nums": category_nums, "page_num": 1}
        self.get(url='/ec/item/v1/content/feed/search/global', headers=headers, params=data)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
    user = V1ContentFeedSearchGlobal()
