import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath
import datetime


class SearchHotKeywords(weeeTest.TestCase):

    def search_hot_keywords(self, headers):
        """# 获取热词"""
        data = {}
        self.get(url='/ec/item/search/hot_keywords', headers=headers, params=data)

        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
    user = SearchHotKeywords()
    user.search_hot_keywords()
