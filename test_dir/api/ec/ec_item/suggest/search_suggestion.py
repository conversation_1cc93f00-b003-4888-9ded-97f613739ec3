import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath
import datetime


class SearchSuggestion(weeeTest.TestCase):

    def search_suggestion(self, headers, term: str = ""):
        """# 根据搜索词获取推荐词"""
        data = {
            "term": term
        }
        self.get(url='/ec/item/search/suggestion', headers=headers, params=data)

        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
    user = SearchSuggestion()
    user.search_hot_keywords()
