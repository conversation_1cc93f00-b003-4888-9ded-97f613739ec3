import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath
import datetime


class ApiCuisine(weeeTest.TestCase):

    def get_cuisine_category_list(self, headers):
        """ 菜系查询(菜系商品列表页) """
        data = None
        self.get(url='/ec/item/cuisines/category', headers=headers, params=data)

        return self.response

    def get_cuisine_list_home_page(self, headers, lang: str = "en",zipcode: int = 98011,
                                   date: str = (datetime.date.today() + datetime.timedelta(days=1)).strftime('%Y-%m-%d'),
                                   ):
        """ 菜系查询(首页) """
        data = {
            "zipcode": zipcode,
            "lang": lang,
            "data": date
        }
        self.get(url='/ec/item/cuisines/home', headers=headers, params=data)

        return self.response

    def get_cuisine_list_index_page(self, headers):
        """ 菜系查询(调研页) """
        data = None
        self.get(url='/ec/item/cuisines/index', headers=headers, params=data)

        return self.response

    def merge_cuisine(self, headers):
        """# 菜系merge(内部服务-登录时调用) """
        data = None
        self.put(url='/ec/item/cuisines/merge', headers=headers, params=data)

        return self.response

    def select_cuisine(self, headers):
        """# 菜系选择(调研页调用,skip也需要调用) """
        data = {
            "selectedCuisines": [
                {
                    "cuisine_key": "chinese",
                    "device_id": "ed67ad0e2a0de6cb4bd2651d2c308519",
                    "select_idx": 1
                }
            ]
        }
        self.put(url='/ec/item/cuisines/select', headers=headers, params=data)

        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'

