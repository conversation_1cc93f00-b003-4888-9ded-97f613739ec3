import datetime

import weeeTest


class ApiInventory(weeeTest.TestCase):

    def ec_inventory_query_v5(self, headers, product_id, zipcode,sales_org_id,product_type: str = "normal",
                       date: str = (datetime.date.today() + datetime.timedelta(days=1)).strftime('%Y-%m-%d'),
                       ):
        """# 获取分类列表 """
        data = {
            "date": date,
            "product_id": product_id,
            "product_type": product_type,
            "zipcode":zipcode,
            "sales_org_id": sales_org_id
        }
        self.post(url='/ec/inventory/query/v5', headers=headers, json=data)

        return self.response