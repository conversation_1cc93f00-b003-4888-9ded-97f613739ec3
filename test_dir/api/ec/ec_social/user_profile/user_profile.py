# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@Description    :
@CreateTime     :  2023/7/13
@Software       :  PyCharm
------------------------------------
"""
import weeeTest
from weeeTest import weeeConfig
from test_dir.api.ec.ec_customer.user.user_bak import User


class UserProfile(weeeTest.TestCase):

    def share_user_info(self, userId: str, headers):
        self.get(url=f'/ec/social/user/{userId}/share', headers=headers)
        return self.response

    def other_user_get_user_profile_info(self, uid: str, headers):
        self.get(url=f'/ec/social/user/{uid}/info', headers=headers)
        return self.response

    def get_user_summary_post_info(self, uid: str, headers):
        self.get(url=f'/ec/social/user/{uid}/summary', headers=headers)
        return self.response

    def query_user_following_list(self, uid: str, headers):
        self.get(url=f'/ec/social/user/{uid}/followings', headers=headers)
        return self.response


    def query_user_following_list_page(self, uid: str, headers, page: int = 1):
        params = {'page': page}
        self.get(url=f'/ec/social/user/{uid}/followings', params=params, headers=headers)
        return self.response


    def query_user_followers_list(self, uid: str, headers):
        self.get(url=f'/ec/social/user/{uid}/followers', headers=headers)
        return self.response

    def search_user(self, headers):
        data = {
            "keyword": "",
            "author_id":"7790952",
            "page": 1
            }
        self.get(url='/ec/social/user/search', headers=headers, params=data)
        return self.response

    def sync_user_info(self, headers):
        self.post(url='/ec/social/user/sync', headers=headers)
        return self.response

    def delete_user_recommended_user(self, id: str, headers):
        self.delete(url=f'/ec/social/user/recommend/{id}', headers=headers)
        return self.response

    def query_user_post_list(self,type, headers):
        self.get(url='/ec/social/user/post?type=' + type, headers=headers)
        return self.response

    def query_user_likes_and_commented_post_list(self, headers):
        self.get(url='/ec/social/user/likes_and_comments', headers=headers)
        return self.response

    def get_user_selection_privilege_for_product(self, headers):
        self.get(url='/ec/social/user/optional', headers=headers)
        return self.response

    def query_user_liked_post_list(self, headers):
        self.get(url='/ec/social/user/likes', headers=headers)
        return self.response

    def user_self_get_user_profile_info(self, headers):
        self.get(url='/ec/social/user/info', headers=headers)
        return self.response


    def follow_user(self, headers):
        self.post(url='/ec/social/user/follow', headers=headers)
        return self.response

    def query_user_commented_post_list(self, headers):
        self.get(url='/ec/social/user/commented', headers=headers)
        return self.response

    def sync_user_avatar_info(self, headers):
        self.post(url='/ec/social/user/avatar', headers=headers)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
    user = User()
    user.anon_auth()
    # user.email_register(email='test.' + user.random_word(num=5) + '@sayweee.com', password='aa123456')
    user.email_login(email='<EMAIL>', password='1')
