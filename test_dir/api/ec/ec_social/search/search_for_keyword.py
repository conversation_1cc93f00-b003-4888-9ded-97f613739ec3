# -*- coding: utf-8 -*-
"""
<AUTHOR>  ji<PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@Description    :  
@CreateTime     :  2023/7/20
@Software       :  PyCharm
------------------------------------
"""
import weeeTest
from weeeTest import weeeConfig
from test_dir.api.ec.ec_customer.user.user_bak import User


class SearchForKeyword(weeeTest.TestCase):

    def search_for_keyword(self,keyword:str,headers):
        self.get(url=f'/ec/social/search/v2?keyword={keyword}',headers=headers)

if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
    user = User()
    user.anon_auth()
    # user.email_register(email='test.' + user.random_word(num=5) + '@sayweee.com', password='aa123456')
    # user.email_login(email='<EMAIL>', password='1')
    SearchForKeyword().search_for_keyword(headers=headers,keyword='苹果')
