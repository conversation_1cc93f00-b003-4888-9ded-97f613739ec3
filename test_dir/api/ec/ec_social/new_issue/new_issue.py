# -*- coding: utf-8 -*-
"""
<AUTHOR>  ji<PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@Description    :
@CreateTime     :  2023/7/13
@Software       :  PyCharm
------------------------------------
"""
import weeeTest
from weeeTest import weeeConfig
from test_dir.api.ec.ec_customer.user.user_bak import User

class NewIssue(weeeTest.TestCase):

    def new_issue(self,headers):
        data = {"feedback_content":"测试数据",
               "feedback_type": 0,
               "issue_type": "测试数据",
               "refer_id":"10022",
               "refer_type": "post"}
        self.post(url='/ec/social/issue',headers=headers,json=data)


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
    user = User()
    user.anon_auth()
    # user.email_register(email='test.' + user.random_word(num=5) + '@sayweee.com', password='aa123456')
    # user.email_login(email='<EMAIL>', password='1')
    # InsightRest().get_video_insights('45451')
    NewIssue().new_issue()






