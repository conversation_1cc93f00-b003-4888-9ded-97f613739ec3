# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@Description    :  
@CreateTime     :  2023/7/24
@Software       :  PyCharm
------------------------------------
"""
import datetime

import weeeTest
from weeeTest import weeeConfig
from test_dir.api.ec.ec_customer.user.user_bak import User


class ReviewInfo(weeeTest.TestCase):
    def review_share(self, reviewId: str, headers):
        self.get(url=f'/ec/social/review/{reviewId}/share', headers=headers)

    def praise_review(self, reviewId: str, status: str, headers):
        body = {"status": status}
        self.post(url=f'/ec/social/review/{reviewId}/praise', json=body, headers=headers)
        return self.response

    def query_review_liked_user_list(self, reviewId: str, headers):
        self.get(url=f'/ec/social/review/{reviewId}/like_users', headers=headers)

    def praise_review_comment(self, reviewId: str, commentId: str, headers):
        self.post(url=f'/ec/social/review/{reviewId}/comment/{commentId}/praise', headers=headers)

    def replay_user_review_comment(self, reviewId: str, commentId: str, headers):
        self.post(url=f'/ec/social/review/{reviewId}/comment/{commentId}', headers=headers)

    def delete_user_review_comment(self, reviewId: str, commentId: str, headers):
        self.delete(url=f'/ec/social/review/{reviewId}/comment/{commentId}', headers=headers)

    def query_review_comment_list(self, reviewId: str, headers):
        self.get(url=f'/ec/social/review/{reviewId}/comment', headers=headers)

    def add_new_review_comment(self, reviewId: str, headers):
        self.post(url=f'/ec/social/review/{reviewId}/comment', headers=headers)

    # def review_share(self, reviewId: str, headers):
    #     self.get(url=f'/ec/social/review/{reviewId}', headers=headers)

    def query_reviews_by_multiple_products(self, headers):
        self.get(url='/ec/social/review/products', headers=headers)

    def query_review_list(self, headers, product_id: int = 9819):
        # pdp 晒单
        data = {
            "product_id": product_id,
            "limit": 10

        }
        self.get(url='/ec/social/review', headers=headers, params=data)
        return self.response

    def query_review_list_for_pdp(self, headers, data):
        # pdp 晒单
        self.get(url='/ec/social/review', headers=headers, params=data)
        return self.response

    def query_sns_review_list(self, headers, product_id: int = 9819):
        # pdp 晒单, 获取不到第一条comment
        data = {
            "product_id": product_id,
            "limit": 6,
            "page": 1

        }
        self.get(url='/ec/sns/review', headers=headers, params=data)
        return self.response

    def review_privilege(self, headers, product_id: int = 9819):
        # 查用户能不能发晒单，返回false是列表页不展示发布icon 返回ture 代表可以发
        data = {
            "product_id": product_id
        }
        self.get(url='/ec/social/review/privilege', headers=headers, params=data)
        return self.response

    def post_ai_review(self, headers, product_id: int = 9819):
        # AI
        data = None
        self.get(url='/ec/social/review/ai/product/' + str(product_id), headers=headers, params=data)
        return self.response

    def social_review_list(self, headers, category: str = "ethnicity", simple: bool = False,
                           zipcode: str = 98011, lang: str = "en",
                           date: str = (datetime.date.today() + datetime.timedelta(days=1)).strftime('%Y-%m-%d'),
                           dataobject_key: str = "ds_post_list"):
        # pc 首页晒单
        data = {
            "category": category,
            "simple": simple,
            "zipcode": zipcode,
            "lang": lang,
            "date": date,
            "dataobject_key": dataobject_key

        }
        self.get(url='/ec/social/review', headers=headers, params=data)
        return self.response

    def review_order(self, order_ids: str, headers):
        self.get(url=f'/ec/social/review/order/{order_ids}', headers=headers)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
    user = User()
    user.anon_auth()
    # user.email_register(email='test.' + user.random_word(num=5) + '@sayweee.com', password='aa123456')
    # user.email_login(email='<EMAIL>', password='1')
