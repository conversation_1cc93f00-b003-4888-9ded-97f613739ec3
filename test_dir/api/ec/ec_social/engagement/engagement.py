# -*- coding: utf-8 -*-
"""
<AUTHOR>  Zhongyuan.Xu
@Version        :  V1.0.0
------------------------------------
@Description    :
@CreateTime     :  2023/12/6
@Software       :  PyCharm
------------------------------------
"""
import random

import requests
import weeeTest
from weeeTest import weeeConfig


class Engagement(weeeTest.TestCase):

    def buy_reward_points(self, headers, **kwargs):
        data = {
            "pointsIssueItemList": [
                {
                    "userId": kwargs.get("userId"),
                    "points": kwargs.get("points"),
                    "typeId": kwargs.get("typeId"),
                    "refId": random.randint(10 ** 7, 10 ** 8 - 1),
                    "uniqBizId": "FD8FBA2E-C28C-4B30-9ABC-A5611CBt0Et8"
                }
            ]
        }
        return self.post(url=f'/central/customer/points_new/issue', headers=headers, json=data)

    def get_me_page_url(self, headers):
        params = {
            "ws": "me_page"
        }
        return requests.get(url=f"https://tb1.sayweee.net/en/account/perks?ws=me_page", headers=headers)

    def active_reward_points(self, headers):
        return self.post(url='/ec/customer/points_new/active', headers=headers)


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
