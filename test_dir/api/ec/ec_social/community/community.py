# -*- coding: utf-8 -*-
"""
<AUTHOR>  Zhongyuan.Xu
@Version        :  V1.0.0
------------------------------------
@Description    :
@CreateTime     :  2023/12/6
@Software       :  PyCharm
------------------------------------
"""
import random

import weeeTest
from weeeTest import weeeConfig
import requests


class Community(weeeTest.TestCase):

    def get_to_reviews_from_account(self, headers):
        """
        从account页面获取to_review小红点数据
        """

        return self.get(url=f'/ec/customer/account/h5/portal', headers=headers)

    def get_to_reviews_from_detail(self, headers):
        """
        从review list页面获取to review数据
        """
        self.get(url="/ec/social/review/to_review/products/v2", headers=headers)
        return self.response

    def upload_file_v2_mix(self, headers, data, files):
        """
        分享晒单，上传文件
        """
        return self.post(url="/resource/v2/upload/mix", headers=headers, data=data, files=files)

    def post_show(self, headers, **kwargs):
        """
        发布晒单
        """
        # ref_url 不能重复，否则接口报错，show_url可以重复
        data = {
            "duration": 16,
            "source": "community",
            "title": "这个宝贝真值得买",
            "tag_ids": "1",
            "ref_url": kwargs.get("ref_url"),
            "thumbnail_create_time": 1113,
            "type": "video",
            "product_ids": kwargs.get("product_ids"),
            "show_url": r"https://img06.test.weeecdn.com/social/thumbnail/905/085/7E3F6F2D32B49315.jpeg",
            "description": "东西超值哦~~~"
        }
        return self.post(url="/ec/social/post", headers=headers, json=data)

    def social_review(self, headers, data):
        self.post(url='/ec/social/review', headers=headers, json=data)
        return self.response

    def query_user_review_list(self, headers, data):
        self.get(url='/ec/social/user/post', headers=headers, params=data)
        return self.response

    def ec_category_post(self, headers, data):
        self.get(url='/ec/social/category/post', headers=headers, params=data)
        return self.response

    def get_social_category_list(self, headers):
        self.get(url='/ec/social/category/list', headers=headers)
        return self.response

    def get_social_sub_category_list(self, headers):
        # 获取recipe一级目录
        category_object = self.get_social_category_list(headers=headers)
        sub_category = [s for c in category_object['object'] if c['key'] == 'recipe' for s in c['sub_category']]
        sub_category_dict = {}
        # 获取recipe二级目录
        for i in sub_category:
            if i.get('sub_category'):
                sub_category_dict[i.get('key')] = [j['key'] for j in i.get('sub_category')]

        return sub_category_dict


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
