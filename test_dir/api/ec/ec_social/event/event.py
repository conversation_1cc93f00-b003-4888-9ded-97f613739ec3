# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@Description    :
@CreateTime     :  2023/7/13
@Software       :  PyCharm
------------------------------------
"""
import weeeTest
from weeeTest import weeeConfig
from test_dir.api.ec.ec_customer.user.user_bak import User

class Event(weeeTest.TestCase):

    def single_event_share(self,id:str,headers):
        self.get(url=f'/ec/social/event/{id}/share',headers=headers)

    def event_linked_post(self,id:str,headers):
        self.get(url=f'/ec/social/event/{id}/post',headers=headers)

    def event_detail(self,id:str,headers):
        self.get(url=f'/ec/social/event/{id}',headers=headers)

    def update_event_info(self,id:str,headers):
        self.post(url=f'/ec/social/event/{id}',headers=headers)

    def query_suggestion_event(self,filter_id:str,headers):
        self.get(url='/ec/social/event/suggestion?filter_id='+filter_id,headers=headers)

    def event_list_share(self,headers):
        self.get(url='/ec/social/event/share',headers=headers)

    def event_remind_by_user(self,event_id:int,status:str,headers):
        data ={ "event_id":event_id,
                "status": status}
        self.post(url='/ec/social/event/remind',json=data, headers=headers)

    def query_event_list(self,headers):
        self.get(url='/ec/social/event',headers=headers)

    def create_event_info(self, headers):
        self.get(url='/ec/social/event', headers=headers)

    def social_event_channel(self, headers, data):
        self.get(url='/ec/social/event', headers=headers, params=data)
        return self.response

    def social_channel_detail_items(self, headers, activity_id, data):
        self.get(url=f'/ec/social/event/{activity_id}/post', headers=headers, params=data)
        return self.response

if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
    user = User()
    user.anon_auth()
    # user.email_register(email='test.' + user.random_word(num=5) + '@sayweee.com', password='aa123456')
    # user.email_login(email='<EMAIL>', password='1')








