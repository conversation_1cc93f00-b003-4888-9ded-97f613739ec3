import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath
import datetime


class RecommendPortal(weeeTest.TestCase):

    def recommend_portal(self, headers, date: str, lang: str = "en"):
        """# 首页热门帖子 """
        data = {
            "lang": lang,
            "date": date
        }
        self.get(url='/ec/social/recommend/portal', headers=headers, params=data)

        return self.response

    def recommend_portal_v2(self, headers, date: str, lang: str = "en", zipcode="98011"):
        """# 首页热门帖子 带zipcode """
        data = {
            "lang": lang,
            "date": date,
            "zipcode": zipcode
        }
        self.get(url='/ec/social/recommend/portal', headers=headers, params=data)

        return self.response

    def social_recommended(self, headers, recommend_session,page_num=1):
        data = {
            "recommend_session": recommend_session,
            "page_num": page_num
        }
        self.get(url='/ec/social/recommend', headers=headers, params=data)
        return self.response

    def social_home_top(self, headers, data):
        self.get(url='/ec/social/category/top', headers=headers, params=data)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
