# -*- coding: utf-8 -*-
"""
<AUTHOR>  ji<PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@Description    :  
@CreateTime     :  2023/7/21
@Software       :  PyCharm
------------------------------------
"""
import weeeTest
from weeeTest import weeeConfig
from test_dir.api.ec.ec_customer.user.user_bak import User

class Track(weeeTest.TestCase):

    def track_video_share_info(self,headers):
        content = {"post_id": "14615",
                   "post_type": "post",
                   "share_type": "facebook"}
        self.post(url='/ec/social/datatrack/share',json=content,headers=headers)

    def track_video_play_info(self,headers):
        content ={"played_to":20,
                  "post_id":"71409",
                  "total_play_time":30
}
        self.post(url='/ec/social/datatrack/video_play',json=content, headers=headers)

if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
    user = User()
    user.anon_auth()
    # user.email_register(email='test.' + user.random_word(num=5) + '@sayweee.com', password='aa123456')
    # user.email_login(email='<EMAIL>', password='1')