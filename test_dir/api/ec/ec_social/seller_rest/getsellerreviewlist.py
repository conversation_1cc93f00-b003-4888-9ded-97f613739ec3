import weeeTest
from weeeTest import weeeConfig


class GetSellerReviewList(weeeTest.TestCase):

    def seller_review(self, headers, seller_id: int = 7319, sort: str = "relevance"):
        """ # 商家主页，reviews,评论 seller_review"""
        data = {"limit": 10,
                "product_id": seller_id,
                "sort": sort,
                "page": 1}
        self.get(url=f'/ec/social/seller/{seller_id}/review', headers=headers, json=data)
        return self.response
        # data = {"limit":"10","product_id":sellerId,"sort": "relevance","page":1 }

    def seller_page_review(self, headers, seller_id, start_id=0, limit=10, sort=None):
        """ # 商家主页，review tab,评论 """
        data = {'start_id': start_id,
                'limit': limit}
        if sort in ["relevance", "recency"]:
            data["sort"] = sort
        self.get(url=f'/ec/social/seller/{seller_id}/review', headers=headers, params=data)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
    user = GetSellerReviewList()
