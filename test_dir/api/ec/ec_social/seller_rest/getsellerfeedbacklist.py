import weeeTest
from weeeTest import log, weeeConfig



class GetSellerFeedbackList(weeeTest.TestCase):

    def get_seller_feedbacklist(self, headers, sellerId: int = 7319):
        """# 商家详情评论 getsellerfeedbacklist"""
        data = {"start_id": None}
        self.get(url=f'/ec/social/seller/{sellerId}/feedback', headers=headers, params=data)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
    user = GetSellerFeedbackList()
