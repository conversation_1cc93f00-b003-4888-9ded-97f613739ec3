import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath


class Follow(weeeTest.TestCase):

    def seller_follow(self, headers, seller_id: int = 7319, status: str = "A"):
        """# 关注商家，# "A" or "C"  """
        data = {
            "seller_id": seller_id,
            "status": status}

        self.post(url='/ec/social/seller/follow', headers=headers, json=data)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
