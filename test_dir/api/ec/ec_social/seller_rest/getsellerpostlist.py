import datetime

import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath


class GetSellerPostList(weeeTest.TestCase):

    def get_seller_post_list(self, headers, seller_id: int = 7319,
                             zipcode: int = 98011, lang: str = "en",
                             date: str = (datetime.datetime.now() + datetime.timedelta(days=2)).strftime(
                                 '%Y-%m-%d')):
        """ # 商家主页，reviews,视频 """
        data = {"zipcode": zipcode,
                "lang": lang,
                "date": date,
                "start_id": 0,
                "limit": 10}
        self.get(url=f'/ec/social/seller/{seller_id}/post', headers=headers, params=data)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
    user = GetSellerPostList()
