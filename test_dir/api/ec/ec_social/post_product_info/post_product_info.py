# -*- coding: utf-8 -*-
"""
<AUTHOR>  tianmei.jiang
@Version        :  V1.0.0
------------------------------------
@Description    :
@CreateTime     :  2023/7/24
@Software       :  PyCharm
------------------------------------
"""
import weeeTest
from weeeTest import weeeConfig
from test_dir.api.ec.ec_customer.user.user_bak import User


class PostProductInfo(weeeTest.TestCase):

    def post_product_V3(self, filter_key_word, zipcode, trigger_type, headers):
        data = {
            "filter_key_word": filter_key_word,
            "zipcode": zipcode,
            "trigger_type": trigger_type
        }

        self.get(url='/ec/social/product/v3/search', params=data, headers=headers)  # 目前在使用的发布视频添加商品的接口
        return self.response

    def post_product_V2(self, filter_key_word, zipcode, trigger_type, headers):
        data = {
            "filter_key_word": filter_key_word,
            "zipcode": zipcode,
            "trigger_type": trigger_type
        }

        self.get(url='/ec/social/product/v2/search', params=data, headers=headers)  # 老接口
        return self.response

    def post_product_V1(self, filter_key_word, zipcode, trigger_type, headers):
        data = {
            "filter_key_word": filter_key_word,
            "zipcode": zipcode,
            "trigger_type": trigger_type
        }

        self.get(url='/ec/social/product/search', params=data, headers=headers)
        return self.response

    if __name__ == '__main__':
        weeeConfig.base_url = 'https://api.tb1.sayweee.net'
        user = User()
        user.anon_auth()
        # user.email_register(email='test.' + user.random_word(num=5) + '@sayweee.com', password='aa123456')
        # user.email_login(email='<EMAIL>', password='Jtm123456')
