# -*- coding: utf-8 -*-
"""
<AUTHOR>  ji<PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@Description    :
@CreateTime     :  2023/7/13
@Software       :  PyCharm
------------------------------------
"""
import weeeTest
from weeeTest import weeeConfig
from test_dir.api.ec.ec_customer.user.user_bak import User

class Message(weeeTest.TestCase):

    def message_summary (self,headers):
        self.get(url='/ec/social/message/summary',headers=headers)

    def message_stat(self,headers):
        self.get(url='/ec/social/message/stat',headers=headers)

    def  message_center(self, headers):
        self.get(url='/ec/social/message/center', headers=headers)

    def message_read_notice(self, headers):
        self.get(url='/ec/social/message/read', headers=headers)

if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
    user = User()
    user.anon_auth()
    # user.email_register(email='test.' + user.random_word(num=5) + '@sayweee.com', password='aa123456')
    # user.email_login(email='<EMAIL>', password='1')
    Message().message_summary()
    Message().message_stat()
    Message().message_center()
    Message().message_read_notice()






