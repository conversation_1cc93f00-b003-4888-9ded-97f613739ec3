# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@Description    :  
@CreateTime     :  2023/7/20
@Software       :  PyCharm
------------------------------------
"""
import weeeTest
from weeeTest import weeeConfig
from test_dir.api.ec.ec_customer.user.user_bak import User


class PostInfo(weeeTest.TestCase):

    def post_pdp_video(self, headers, product_id, type):
        data = {
            "product_id": product_id,
            "type": type,

        }
        self.get(url='/ec/social/post', headers=headers, params=data)
        return self.response

    def update_video_translate(self, headers):
        data = {
            "title": "更新视频翻译",
            "description": "接口测试数据 ，请忽略",
            "target_lang": "ja"

        }
        self.post(url='/ec/social/translate', headers=headers, json=data)

    def post_share(self, postId: str, headers):
        self.get(url=f'/ec/social/post/{postId}/share', headers=headers)

    def praise_post(self, headers, postId, status: str = "A"):
        data = {
            "status": status
        }

        self.post(url=f'/ec/social/post/{postId}/praise', headers=headers, json=data)
        return self.response

    def pinto_top(self, postId: str, headers):
        self.post(url=f'/ec/social/post/{postId}/pin', headers=headers)

    def query_post_liked_user_list(self, postId: str, headers):
        self.post(url=f'/ec/social/post/{postId}/like_users', headers=headers)

    def praise_post_comment(self, postId: str, commentId: str, headers, status: str = "A"):
        data = {
            "status": status
        }
        self.post(url=f'/ec/social/post/{postId}/comment/{commentId}/praise', json=data, headers=headers)

    def replay_user_post_comment(self, postId: str, commentId: str, headers):
        data = {
            "content": "測試數據"
        }

        self.post(url=f'/ec/social/post/{postId}/comment/{commentId}', json=data, headers=headers)

    def delete_post_comment(self, postId: str, commentId: str, headers):
        self.delete(url=f'/ec/social/post/{postId}/comment/{commentId}', headers=headers)

    def new_post_comment(self, postId: str, content, headers):
        data = {
            "content": content
        }

        self.post(url=f'/ec/social/post/{postId}/comment', headers=headers, json=data)

    def query_post_comment_list(self, postId: str, headers):
        self.get(url=f'/ec/social/post/{postId}/comment', headers=headers)

    def post_products_v3(self, id: str, headers):
        self.get(url=f'/ec/social/post/{id}/products/v3', headers=headers)

    def post_produccts(self, id: str, headers):
        self.get(url=f'/ec/social/post/{id}/products', headers=headers)

    def update_post(self, id: str, headers):
        data = {"image_urls": ["https://img06.test.weeecdn.com/social/image/941/423/238181F54945BA08.png"],
                "comment": "测试对应的订单晒单详情",
                "rating": 5}
        self.post(url=f'/ec/social/post/{id}', json=data, headers=headers)
        return self.response

    def post_detail_info(self, id: str, headers):
        self.get(url=f'/ec/social/post/{id}', headers=headers)

    def delete_post(self, id: str, headers):
        self.delete(url=f'/ec/social/post/{id}', headers=headers)

    def post_list_v2(self, headers):
        self.get(url='/ec/social/post/v2', headers=headers)

    def judge_new_follow_info(self, headers):
        self.get(url='/ec/social/post/user/new_follow', headers=headers)

    def query_user_post_list(self, userId: str, headers):
        self.get(url=f'/ec/social/post/user/{userId}', headers=headers)

    def query_user_following_post_list_v2(self, headers):
        self.get(url='/ec/social/post/user/follows/v2', headers=headers)

    def query_user_following_post_list_v1(self, headers):
        self.get(url='/ec/social/post/user/follows', headers=headers)

    def get_post_share(self, post_id, headers):
        self.get(url='/ec/social/post/share?post_id=' + post_id, headers=headers)

    def post_related_products(self, product_ids, headers):
        self.get(url='/ec/social/post/products?product_ids=' + product_ids, headers=headers)

    def user_own_post(self, headers):
        self.get(url='/ec/social/post/my_post', headers=headers)

    def tag_list(self, headers):
        self.get(url='/ec/social/post/hash_tags', headers=headers)

    def get_approval_flow_notify(self, headers):
        self.get(url='/ec/social/post/flow/notify', headers=headers)

    def post_list_v1(self, headers):
        self.get(url='/ec/social/post', headers=headers)

    def query_items_in_social(self, headers, params):
        self.get(url='/ec/social/post', headers=headers, params=params)
        return self.response


    def create_post(self, headers):
        data = {"image_urls": ["https://img06.test.weeecdn.com/social/image/941/423/238181F54945BA08.png"],
                "order_id": 34122193,
                "product_id": 36443,
                "comment": "测试对应的订单晒单详情",
                "rating": 5}
        self.get(url='/ec/social/post', json=data, headers=headers)


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
    user = User()
    user.anon_auth()
    # user.email_register(email='test.' + user.random_word(num=5) + '@sayweee.com', password='aa123456')
    # user.email_login(email='<EMAIL>', password='1')
