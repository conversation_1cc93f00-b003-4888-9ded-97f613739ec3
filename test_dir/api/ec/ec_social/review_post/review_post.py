# -*- coding: utf-8 -*-
"""
<AUTHOR>  tianmei.jiang
@Version        :  V1.0.0
------------------------------------
@Description    :
@CreateTime     :  2023/12/6
@Software       :  PyCharm
------------------------------------
"""
import weeeTest
from weeeTest import weeeConfig

from test_data.ec.simple.uploadfiles import upload_multi_files
from test_dir.api.ec.ec_customer.user.user_bak import User


class ReviewPost(weeeTest.TestCase):

    def to_review_list(self, headers):
        self.get(url='/ec/social/review/to_review/products/v2', headers=headers)

    def completed_review(self, review, all, my_review, headers):
        data = {
            "type": review,
            "status": all,
            "page_source": my_review
        }

        self.post(url='/ec/social/user/post', headers=headers, json=data)
        return self.response

    def published_review(self, review, P, my_review, headers):
        data = {
            "type": review,
            "status": P,
            "page_source": my_review
        }
        self.post(url='/ec/social/user/post', headers=headers, json=data)
        return self.response

    def in_review(self, review, A, my_review, headers):
        data = {
            "type": review,
            "status": A,
            "page_source": my_review
        }
        self.post(url='/ec/social/user/post', headers=headers, json=data)
        return self.response

    def rejected_review(self, review, R, my_review, headers):
        data = {
            "type": review,
            "status": R,
            "page_source": my_review
        }
        self.post(url='/ec/social/user/post', headers=headers, json=data)
        return self.response

    def review_upload(self, image, social, headers):
        files = [("file", ("as2", open("./test_data/ec/uploadimage/allstore2.jpg", 'rb')))]
        print("files===>", files)
        data = {
            "subType": image,
            "bizType":  social
        }

        # 不能带这个header，否则会报错
        # headers["content-type"] = r'multipart/form-data; boundary=----WebKitFormBoundaryqHNLwm7h5KMlkVEp'
        del headers["Content-Type"]    # 要将原来的Content-Type删除，否则报错
        # self.post(url='/resource/v2/upload', headers=headers, params=data, files=files)
        res = upload_multi_files("https://rs.tb1.sayweee.net/resource/v2/upload", headers, data, files)
        print("res.json===>", res.json())
        return res.json()

    def review_post(self, order_id, product_id, headers):
        data = {
            "image_urls": ["https://img06.test.weeecdn.com/social/image/941/423/238181F54945BA08.png"],
            "order_id": order_id,
            "product_id": product_id,
            "comment": "测试发布晒单新接口啦"
        }
        self.post(url='/ec/social/review', headers=headers, json=data)
        return self.response
    def review_successful_list(self, headers):

        self.get(url='/ec/social/review/to_review/products', headers=headers)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
    user = User()
    user.anon_auth()
    # user.email_register(email='test.' + user.random_word(num=5) + '@sayweee.com', password='aa123456')
    user.email_login(email='<EMAIL>', password='Jtm123456')
