# -*- coding: utf-8 -*-
"""
<AUTHOR>  tianmei.jiang
@Version        :  V1.0.0
------------------------------------
@Description    :
@CreateTime     :  2023/12/04
@Software       :  PyCharm
------------------------------------
"""
import weeeTest
from weeeTest import weeeConfig
from test_dir.api.ec import headers
from test_dir.api.ec.ec_customer.user.user_bak import User

class ReviewAuditTool(weeeTest.TestCase):
    def to_review_list(self,review,A,rec_create_time,asc,headers):
        data = {
            "type": review,
            "status": A,
            "order_column":rec_create_time,
            "order_direct":asc,

        }
        self.get(url='/central/social/audit', headers=headers, params=data)
        return self.response
    def to_review_alcohol_list(self,review_alcohol,A,rec_create_time,asc,headers):
        data = {
            "type": review_alcohol,
            "status": A,
            "order_column":rec_create_time,
            "order_direct":asc,

        }
        self.get(url='/central/social/audit', headers=headers, params=data)
        return self.response

    def to_review_rtg_list(self,review_rtg,A,rec_create_time,asc,headers):
        data = {
            "type": review_rtg,
            "status": A,
            "order_column":rec_create_time,
            "order_direct":asc,

        }
        self.get(url='/central/social/audit', headers=headers, params=data)
        return self.response

    def review_approved(self,ids,P,review,headers):
        data = {
            "ids":ids,
            "status":P,
            "type":review
        }
        self.get(url='/central/social/audit', headers=headers, params=data)
        return self.response

    def review_rtg_approved(self,ids,P,review_rtg,headers):
        data = {
            "ids": ids,
            "status": P,
            "type": review_rtg
        }
        self.get(url='/central/social/audit', headers=headers, params=data)
        return self.response