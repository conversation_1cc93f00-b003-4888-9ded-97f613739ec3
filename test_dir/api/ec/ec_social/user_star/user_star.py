# -*- coding: utf-8 -*-
"""
<AUTHOR>  tianmei.jiang
@Version        :  V1.0.0
------------------------------------
@Description    :
@CreateTime     :  2023/7/21
@Software       :  PyCharm
------------------------------------
"""
import weeeTest
from weeeTest import weeeConfig
from test_dir.api.ec.ec_customer.user.user_bak import User

class UserStar(weeeTest.TestCase):

    def oneself_profile_star(self,headers):
        self.get(url='/ec/social/star/info',headers=headers)
    def otherself_profile_star(self,uid:str,headers):
        self.get(url=f'/ec/social/star/{uid}/info',headers=headers)


    def summary_profile_star(self,headers):
        self.get(url='/ec/social/star/type/summary',headers=headers)


    def star_details(self,headers):
        self.get(url='/ec/social/star/record',headers=headers)




    if __name__ == '__main__':
        weeeConfig.base_url = 'https://api.tb1.sayweee.net'
        user = User()
        user.anon_auth()
        # user.email_register(email='test.' + user.random_word(num=5) + '@sayweee.com', password='aa123456')
        # user.email_login(email='<EMAIL>', password='Jtm123456')
