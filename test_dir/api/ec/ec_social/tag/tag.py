# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@Description    :  
@CreateTime     :  2023/7/21
@Software       :  PyCharm
------------------------------------
"""
import weeeTest
from weeeTest import weeeConfig
from test_dir.api.ec.ec_customer.user.user_bak import User

class Tag(weeeTest.TestCase):

    def single_tag_share(self,id:str,headers):
        self.get(url=f'/ec/social/tag/{id}/share',headers=headers)

    def query_tag_linked_post_list(self,id:str,headers):
        self.get(url=f'/ec/social/tag/{id}/post', headers=headers)

    def get_tag_detail(self,id:str,headers):
        self.get(url=f'/ec/social/tag/{id}', headers=headers)

    def query_trending_tag_list_with_filter(self,headers):
        self.get(url='/ec/social/tag/trending/v2',headers=headers)

    def query_trending_tag_list(self,headers):
        self.get(url='/ec/social/tag/trending',headers=headers)

    def  tag_list_share(self,headers):
        self.get(url='/ec/social/tag/share',headers=headers)

    def query_tag_list(self,headers):
        self.get(url='/ec/social/tag', headers=headers)



if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
    user = User()
    user.anon_auth()
    # user.email_register(email='test.' + user.random_word(num=5) + '@sayweee.com', password='aa123456')
    # user.email_login(email='<EMAIL>', password='1')