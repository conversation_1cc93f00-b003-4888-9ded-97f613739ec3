import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath
import datetime


class QueryVendorPromotions(weeeTest.TestCase):

    def query_vendor_promotions(self, headers, vendor_id: int = None):
        """# 获取商家的活动列表信息"""
        data = {"business_type": "seller", "delivery_mode": None}
        self.get(url=f'/ec/promotion/promotions/vendor/{vendor_id}', headers=headers, params=data)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
