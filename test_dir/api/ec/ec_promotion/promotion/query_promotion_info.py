import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath
import datetime


class QueryPromotionInfo(weeeTest.TestCase):

    def query_promotion_info(self, headers, promo_id: int = 1234, promo_type: str = "promo"):
        """# 商家活动页获取活动详情信息"""
        data = {
            "promo_id": promo_id,
            "promo_type": promo_type}
        self.get(url='/ec/promotion/promotions/info', headers=headers, params=data)
        return self.response

    def query_promotion_product(self, headers, product_id):
        """# pdp页面活动信息"""
        self.get(url="/ec/promotion/promotions/product/" + str(product_id), headers=headers)
        return self.response

    def event_promotion_product(self, headers, event_code, type_id, zipcode: int = 98011, lang: str = "en",
                                date: str = (datetime.datetime.now() + datetime.timedelta(days=2)).strftime(
                                    '%Y-%m-%d'), dataobject_key: str = None):
        """# crazy8页面活动信息"""
        data = {
            "event_code": event_code,
            "type_id": type_id,
            "zipcode": zipcode,
            "lang": lang,
            "date": date,
            "dataobject_key": dataobject_key

        }

        self.get(url="/ec/promotion/event/products", headers=headers, params=data)
        return self.response

    def promotion_landing_products(self, headers, tag_id, tag_type: str = "promotion_event",
                                   preview: bool = False, from_page: str = "use_page"):
        """# 活动landing 页商品"""
        data = {
            "tag_id": tag_id,
            "tag_type": tag_type,
            "preview": preview,
            "from_page": from_page,
            "offset": 0,
            "limit": 10
        }

        self.get(url="/ec/promotion/promotions/landing/products", headers=headers, params=data)
        return self.response

    def promotion_landing(self, headers, ps_id, preview: bool = False):
        """# 活动landing 页"""
        data = {"ps_id": ps_id,
                "preview": preview}

        self.post(url="/ec/promotion/promotions/landing", headers=headers, json=data)
        return self.response

    def promotion_mkpl_coupon_obtain(self, headers, plan_id: int = None, vendor_id: int = None):
        """
        mkpl pdp 页面领取优惠券接口
        @param headers:
        @param plan_id:
        @param vendor_id:
        @return:
        """
        data = {
            "plan_id": plan_id,
            "vendor_id": vendor_id
        }

        self.post(url='/ec/promotion/mkpl/coupon/obtain', headers=headers, json=data)
        return self.response

    def promotions_personal_homepage(self, headers, zipcode, lang, date):
        """# 首页FBW Coupon promotion"""
        data = {
            "zipcode": zipcode,
            "lang": lang,
            "date": date, }
        self.get(url='/ec/promotion/promotions/personal/homepage', headers=headers, params=data)
        return self.response

    def promotions_personal_module(self, headers, source, user_tag):
        """# 落地页FBW Coupon promotion"""
        data = {
            "source": source,
            "user_tag": user_tag
        }
        self.post(url='/ec/promotion/promotions/personal/module', headers=headers, json=data)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
