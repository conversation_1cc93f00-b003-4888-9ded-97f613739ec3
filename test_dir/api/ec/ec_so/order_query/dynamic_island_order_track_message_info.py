# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  user_bak.py
@Description    :
@CreateTime     :  2023/5/30 11:06
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/30 11:06
"""

import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath
import datetime


class DynamicIslandOrderTrackMessageInfo(weeeTest.TestCase):

    def dynamic_island_order_track_message_info(self, headers, order_id):
        """#dynamic_island_order_track_message_info_query"""
        data = {
            "order_id": order_id
        }
        self.get(url='/ec/so/order/query/order_track_push_msg', headers=headers, json=data)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
