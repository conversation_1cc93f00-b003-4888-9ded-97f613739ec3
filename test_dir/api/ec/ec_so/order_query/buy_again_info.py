# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  user_bak.py
@Description    :
@CreateTime     :  2023/5/30 11:06
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/30 11:06
"""

import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath
import datetime


class BuyAgainInfo(weeeTest.TestCase):

    def buy_again_info_v1(self, headers, order_id):
        """#buy_again_info_v1"""
        data = {
            "order_id": order_id

        }
        self.get(url='ec/so/order/query/buyAgain/info', headers=headers, params=data)
        return self.response

    def buy_again_info_v2(self, headers, order_id):
        """#buy_again_info_v2"""
        data = {
            "order_id": order_id
        }
        self.get(url='/ec/so/order/query/buyAgain/info/v2', headers=headers, params=data)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
