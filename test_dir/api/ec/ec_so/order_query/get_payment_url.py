# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  user_bak.py
@Description    :
@CreateTime     :  2023/5/30 11:06
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/30 11:06
"""

import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath
import datetime


class GetPaymentUrl(weeeTest.TestCase):

    def get_payment_url_v1(self, headers, order_id, payment_category: str = "D"):
        """#get_payment_url_v1"""
        data = {
            "braintree_device_data": "",
            "currency": "",
            "cvv_token": "",
            "order_id": order_id,
            "payment_category": payment_category,
            "platform": "",
            "radar_options": {}
        }
        self.post(url='/ec/so/order/query/category/next_url', headers=headers, json=data)
        return self.response

    def get_payment_url_v2(self, headers, order_id, payment_category: str = "D"):
        """#get_payment_url_v2"""
        data = {
            "checkout_id": order_id,
            "payment_category": payment_category,
            "platform": "h5",
            "cvv_token": "",
            "braintree_device_data": ""
        }
        self.post(url='/ec/so/order/query/category/next_url/v2', headers=headers, json=data)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
