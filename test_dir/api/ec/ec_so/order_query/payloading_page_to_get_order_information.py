# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON> <PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  user_bak.py
@Description    :
@CreateTime     :  2023/5/30 11:06
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/30 11:06
"""

import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath
import datetime


class PayLoadingPageToGetOrderInformation(weeeTest.TestCase):

    def payloading_page_to_get_order_information_v1(self, headers, order_id):
        """#payloading_page_to_get_order_information_V1 微信支付扫描页面"""
        data = {
            "order_id": order_id
        }
        self.get(url='/ec/so/order/query/payloading/info', headers=headers, json=data)
        return self.response

    def payloading_page_to_get_order_information_v2(self, headers, order_id):
        """#payloading_page_to_get_order_information_V2"""
        data = {
            "order_id": order_id
        }
        self.get(url='/ec/so/order/query/payloading/info/v2', headers=headers, json=data)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
