# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  user_bak.py
@Description    :
@CreateTime     :  2023/5/30 11:06
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/30 11:06
"""

import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath
import datetime


class ListTodayOrder(weeeTest.TestCase):

    def list_today_order(self, headers):
        """#list_today_order_v1"""
        data = None
        self.get(url='/ec/so/order/query/today_orders', headers=headers, json=data)
        return self.response

    def list_today_order_me_page(self, headers):
        """#list_today_order_me_page"""
        data = None
        self.get(url='/ec/so/order/query/me/today_orders', headers=headers, json=data)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
