# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON> <PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  user_bak.py
@Description    :
@CreateTime     :  2023/5/30 11:06
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/30 11:06
"""

import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath
import datetime


class OrderSuccessPageInformation(weeeTest.TestCase):

    def order_success_page_information_v1(self, headers, order_id):
        """#order_success_page_information_v1"""
        data = None
        self.get(url='/ec/so/order/query/success/' + order_id, headers=headers, params=data)
        return self.response

    def order_success_page_information_v2(self, headers, checkout_id):
        """#order_success_page_information_v2"""
        data = None
        self.get(url='/ec/so/order/query/success/v2/' + checkout_id, headers=headers, params=data)
        return self.response

    def order_query_purchased_product(self, headers, delivery_date, zipcode):
        """#order_success_page_information_v2"""
        data = {
            "delivery_date": delivery_date,
            "zipcode": zipcode
        }
        self.get(url='/ec/so/order/query/purchased/products', headers=headers, params=data)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
