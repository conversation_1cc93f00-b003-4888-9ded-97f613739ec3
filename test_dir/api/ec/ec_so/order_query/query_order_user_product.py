# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  user_bak.py
@Description    :
@CreateTime     :  2023/5/30 11:06
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/30 11:06
"""

import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath
import datetime


class QueryOrderUserProduct(weeeTest.TestCase):

    def query_order_user_product(self, headers, deal_id, product_ids: list):
        """#query_order_user_product"""
        data = [
            {
                "deal_id": deal_id,
                "product_ids": product_ids

            }
        ]

        self.post(url='/ec/so/order/query/userProducts', headers=headers, json=data)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
