# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  user_bak.py
@Description    :
@CreateTime     :  2023/5/30 11:06
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/30 11:06
"""

import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath
import datetime


class PendingPaymentDetails(weeeTest.TestCase):

    def pending_payment_details_v1(self, headers, order_id, browser_type):
        """#pending_payment_details_v1"""
        data = {
            "browser_type": browser_type,
            "order_id": order_id
        }
        self.post(url='/ec/so/order/query/deal/pay', headers=headers, json=data)
        return self.response

    def pending_payment_details_v2(self, headers, checkout_id):
        """#pending_payment_details_v2"""
        data = {
            "checkout_id": checkout_id
        }
        self.post(url='/ec/so/order/query/deal/pay/v2', headers=headers, json=data)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
