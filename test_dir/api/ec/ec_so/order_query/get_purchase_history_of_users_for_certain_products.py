# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  user_bak.py
@Description    :
@CreateTime     :  2023/5/30 11:06
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/30 11:06
"""

import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath
import datetime


class GetPurchaseHistoryCertainProducts(weeeTest.TestCase):

    def get_purchase_history_of_users_for_certain_products(self, headers):
        """#get_urchase_history_of_users_for_certain_products"""
        data = {
            "integers": 1
        }
        self.post(url='/ec/so/order/query/items/purchase', headers=headers, json=data)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
