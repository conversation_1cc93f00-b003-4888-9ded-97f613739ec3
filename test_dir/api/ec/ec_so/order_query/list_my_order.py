# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  user_bak.py
@Description    :
@CreateTime     :  2023/5/30 11:06
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/30 11:06
"""

import weeeTest
from weeeTest import log, weeeConfig


class ListMyOrder(weeeTest.TestCase):

    def list_my_order_v1(self, headers, filter_status: str = "all", keyword: str = "",
                         filter_date: str = None, lang: str = "en"):
        """#list_my_order_v1:PC"""
        data = {
            "filter_status": filter_status,  # ["all","1","2","3","6","4"]
            "filter_date": filter_date,  # [1,2,3,4,5,6,7,8,9]
            "keyword": keyword,  # 查询的时候才传
            "lang": lang,
            "limit": 50,
            "offset": 0
        }
        self.get(url='/ec/so/order/query/listMyOrder', headers=headers, params=data)
        return self.response

    def list_my_order_v2(self, headers, filter_status: str = "all", keyword: str = None,
                         filter_date: str = None, lang: str = "en", page_size: int = 20):
        """#list_my_order_v2:H5"""
        data = {
            "filter_status": filter_status,  # ["all","1","2","3","6","4"]
            "filter_date": filter_date,
            "keyword": keyword,  # 查询的时候才传
            "lang": lang,
            "page_no": 1,
            "page_size": page_size
        }
        self.post(url='/ec/so/order/query/listMyOrder/v2', headers=headers, json=data)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
