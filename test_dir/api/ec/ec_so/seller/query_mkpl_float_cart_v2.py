# !/usr/bin/python3
# -*- coding: utf-8 -*-

"""
<AUTHOR>  <PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  seller_cart.py
@Description    :
@CreateTime     :  2023/8/01 17:06
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/8/01 17:06
"""

import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath


class MkplFloatCartV2(weeeTest.TestCase):

    def mkpl_float_cart_v2(self, headers):
        """
        store页悬浮购物车 不指定seller
        :param headers:
        :return:
        """
        data = {
            "vendor_id": None,
            "show_product_detail": 0
        }

        self.get(url='/ec/so/seller/cart/float/v2', headers=headers, params=data)
        return self.response

    def mkpl_seller_cart_v2(self, headers, seller_id):
        """
        seller页悬浮购物车 当前seller

        :param headers:
        :param seller_id:
        :return:
        """
        data = {
            "vendor_id": seller_id,
            "show_product_detail": 1
        }

        self.get(url='/ec/so/seller/cart/float/v2', headers=headers, params=data)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
