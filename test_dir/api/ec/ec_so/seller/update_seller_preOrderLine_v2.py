# !/usr/bin/python3
# -*- coding: utf-8 -*-

"""
<AUTHOR>  <PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  seller_cart.py
@Description    :
@CreateTime     :  2023/7/24 17:06
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/7/24 17:06
"""

import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath


class SellerPreOrderV2(weeeTest.TestCase):

    def seller_preorderline_v2(self, headers, vendor_id: int, product_id: int):
        """all store-on sale页面加购"""
        data = [{"product_id": product_id, "source": "mweb_mkpl_vendor_landing-cm_mkpl_seller_line_395459-null",
                 "source_store": "cn", "refer_type": "seller", "refer_value": "7556", "delivery_date": "2023-09-06",
                 "min_order_quantity": 1, "is_pantry": False, "is_alcohol": False, "item_type": "", "is_mkpl": True,
                 "positionInfoT2": {
                     "modSecPos": {"mod_nm": "cm_mkpl_seller_line_395459", "mod_pos": 1, "sec_nm": vendor_id, "sec_pos": 0},
                     "context": {"filter_sub_category": "recommend"}, "prodPos": 1},
                 "ctx": {"filter_sub_category": "recommend"}, "vender_id": vendor_id, "quantity": 1,
                 "new_source": "{\"mod_nm\":\"cm_mkpl_seller_line_395459\",\"mod_pos\":1,\"sec_nm\":7556,"
                               "\"sec_pos\":0,\"prod_pos\":1,\"page_key\":\"mweb_mkpl_vendor_landing\","
                               "\"referer_page_key\":\"mweb_mkpl_waterfall\","
                               "\"view_id\":\"363a6d844a77446e9ba366efe7501162\"}"}]
        vendor = {"vendor_id": vendor_id}
        self.put(url='/ec/so/seller/items/v2', headers=headers, params=vendor, json=data)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
