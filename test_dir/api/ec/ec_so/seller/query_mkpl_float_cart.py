# !/usr/bin/python3
# -*- coding: utf-8 -*-

"""
<AUTHOR>  <PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  seller_cart.py
@Description    :
@CreateTime     :  2023/7/24 17:06
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/7/24 17:06
"""

import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath

class MkplFloatCart(weeeTest.TestCase):

    def mkpl_float_cart(self, headers, vendor_id: int, show_product_detail: bool):
        data = {
            "vendor_id": vendor_id,
            "show_product_detail": show_product_detail
        }

        self.get(url='/ec/so/seller/cart/float', headers=headers, params=data)
        return self.response

    def mkpl_float_cart_v2(self, headers, vendor_id: int, show_product_detail: bool):
        data = {
            "vendor_id": vendor_id,
            "show_product_detail": show_product_detail
        }

        self.get(url='/ec/so/seller/cart/float/v2', headers=headers, params=data)
        return self.response

if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'