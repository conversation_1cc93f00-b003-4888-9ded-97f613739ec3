# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  user_bak.py
@Description    :
@CreateTime     :  2023/5/30 11:06
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/30 11:06
"""

import weeeTest
from weeeTest import log, weeeConfig


class SaveForLaterItemMoveToCart(weeeTest.TestCase):

    def save_for_later_item_move_to_cart(self, headers, product_keys: list):
        """#save_for_later_item_move_to_cart"""
        data = {
            "product_keys": product_keys
        }
        self.post(url='/ec/so/save4later/to_cart', headers=headers, json=data)
        return self.response

    def save4later_v2(self, headers, offset: int = 0, page_size: int = 100):
        # 加载更多稍后再买商品
        data = {
            "offset": offset,
            "page_size": page_size
        }
        self.get(url='/ec/so/save4later/v2', headers=headers, params=data)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
