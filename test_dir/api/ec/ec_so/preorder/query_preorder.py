# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  user_bak.py
@Description    :
@CreateTime     :  2023/5/30 11:06
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/30 11:06
"""

import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath


class QueryPreOrder(weeeTest.TestCase):

    def query_mini_preorder(self, headers, data=None):
        """# query_mini_preorder"""
        self.get(url='/ec/so/porder/queryMiniPreOrder', headers=headers, json=data)
        return self.response

    def query_preorder_v2(self, headers, data=None):
        """# query_preorder_v2"""

        self.get(url='/ec/so/porder/v2', headers=headers, params=data)

        return self.response

    def query_preorder_v3(self, headers, data=None):
        """# query_preorder_v3"""
        self.get(url='/ec/so/porder/v3', headers=headers, params=data)

        return self.response

    def query_preorder_v4(self, headers, data=None):
        """# query_preorder_v4"""
        self.get(url='/ec/so/porder/v4', headers=headers, params=data)

        return self.response

    def query_preorder_v5(self, headers, cart_domain: str = "grocery"):
        """# query_preorder_v5"""
        data = {"cart_domain": cart_domain}

        self.get(url='/ec/so/porder/v5', headers=headers, params=data)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
    user = QueryPreOrder()

    # user.search_v3()
    # user.email_login(email='<EMAIL>', password='aa123456')

    # user.email_register(email='test.' + user.random_word(num=5) + '@sayweee.com', password='aa123456')
