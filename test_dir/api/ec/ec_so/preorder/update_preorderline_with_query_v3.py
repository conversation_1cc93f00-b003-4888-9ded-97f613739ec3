
import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath


class UpdatePreOrderLineWithQueryv3(weeeTest.TestCase):

    def porder_items_cart_v3(self,  headers,data=None):
        """# 加购商家活动页面商品"""
        self.get(url='/ec/so/porder/items/cart/v3', headers=headers,  json=data)
        return self.response




if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
    user = UpdatePreOrderLineWithQueryv3()