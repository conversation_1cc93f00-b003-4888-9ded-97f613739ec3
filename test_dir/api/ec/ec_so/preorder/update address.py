# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  user_bak.py
@Description    :
@CreateTime     :  2023/5/30 11:06
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/30 11:06
"""

import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath
import datetime


class UpdateAddress(weeeTest.TestCase):

    def update_address_v1(self, headers, browser_type, coupon_code,
                          delivery_pickup_date, payment_category, profile_id,
                          zipcode, zipcode_type):
        """#update_address_v1"""
        data = {
            "addr_address": "",
            "addr_apt": "",
            "addr_city": "",
            "addr_country": "",
            "addr_firstname": "",
            "addr_lastname": "",
            "addr_state": "",
            "addr_zipcode": "",
            "address": "",
            "address_id": 0,
            "comment": "",
            "date": "",
            "email": "",
            "fixed_latitude": 0,
            "fixed_longitude": 0,
            "force": True,  # 要根据实际情况填写true or false
            "free_gift_change_force": True,
            "is_hotdish": True,
            "is_pin": True,
            "phone": "",
            "pin_latitude": 0,
            "pin_longitude": 0,
            "source": "",
            "store_id": 0,
            "type": "",
            "vendor_id": 0,
            "zipcode": "",
            "zipcode_for_query": ""
        }
        self.put(url='/ec/so/porder/address', headers=headers, json=data)
        return self.response

    def update_address_v2(self, headers, browser_type, coupon_code,
                          delivery_pickup_date, payment_category, profile_id,
                          zipcode, zipcode_type):
        """#update_address_v2"""
        data = {
            "addr_address": "",
            "addr_apt": "",
            "addr_city": "",
            "addr_country": "",
            "addr_firstname": "",
            "addr_lastname": "",
            "addr_state": "",
            "addr_zipcode": "",
            "address": "",
            "address_id": 0,
            "comment": "",
            "date": "",
            "email": "",
            "fixed_latitude": 0,
            "fixed_longitude": 0,
            "force": True,
            "free_gift_change_force": True,
            "is_hotdish": True,
            "is_pin": True,
            "phone": "",
            "pin_latitude": 0,
            "pin_longitude": 0,
            "source": "",
            "store_id": 0,
            "type": "",
            "vendor_id": 0,
            "zipcode": "",
            "zipcode_for_query": ""
        }
        self.put(url='/ec/so/porder/address/v2', headers=headers, json=data)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
