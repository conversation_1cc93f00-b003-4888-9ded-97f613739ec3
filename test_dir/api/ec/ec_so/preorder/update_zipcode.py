# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  user_bak.py
@Description    :
@CreateTime     :  2023/5/30 11:06
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/30 11:06
"""

import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath
import datetime


class UpdateZipcode(weeeTest.TestCase):

    def update_zipcode_v1(self, headers, zipcode: str = "98011", address_id: int = None

                          ):
        """#update_zipcode_v1"""
        data = {
            "zipcode": str(zipcode),
            "address_id": address_id
        }
        self.put(url='/ec/so/porder/zipcode', headers=headers, json=data)
        return self.response

    def update_zipcode_v2(self, headers, address_id: int = None, zipcode: str = "98011"

                          ):
        """#update_zipcode_v2"""
        data = {
            "zipcode": str(zipcode),
            "address_id": address_id
        }
        self.put(url='/ec/so/porder/zipcode/v2', headers=headers, json=data)
        return self.response

    def update_zipcode_v3(self, headers, zipcode: str = "98011", address_id: int = None

                          ):
        """#update_zipcode_v3"""
        data = {
            "zipcode": str(zipcode),
            "address_id": address_id

        }
        self.put(url='/ec/so/porder/zipcode/v3', headers=headers, json=data)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
