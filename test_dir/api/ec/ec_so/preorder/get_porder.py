# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  user_bak.py
@Description    :
@CreateTime     :  2023/5/30 11:06
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/30 11:06
"""

import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath
import datetime


class GetPreOrder(weeeTest.TestCase):

    def get_porder(self, headers, userFlag):
        """#get_porder"""
        data = {
            "userFlag": userFlag,

        }
        self.get(url='/ec/so/porder/query', headers=headers, json=data)
        return self.response



if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
