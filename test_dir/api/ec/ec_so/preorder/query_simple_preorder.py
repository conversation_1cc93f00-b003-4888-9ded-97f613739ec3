# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  user_bak.py
@Description    :
@CreateTime     :  2023/5/30 11:06
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/30 11:06
"""
import inspect
import time

import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath
import datetime


class QuerySimplePreOrder(weeeTest.TestCase):

    def query_simple_preorder_v1(self, headers):
        """#H5-query_simple_preOrder_v1"""
        data = None
        self.get(url='/ec/so/porder/simple', headers=headers, json=data)
        if self.status_code != 200 or self.response == 'upstream request timeout':
            time.sleep(5)
            self.get(url='/ec/so/porder/simple', headers=headers, json=data)
        return self.response

    def query_simple_preorder_v2(self, headers):
        """#PC-query_simple_preOrder_v2"""
        data = None
        self.get(url='/ec/so/porder/simple/v2', headers=headers, json=data)
        return self.response

    def query_simple_preorder_v3(self, headers):
        """#query_simple_preOrder_v3"""
        data = None
        self.get(url='/ec/so/porder/simple/v3', headers=headers, json=data)
        return self.response

    def query_simple_preorder_by_user(self, headers, user_id):
        """#query_simple_preorder_by_user"""
        data = None
        self.get(url='/ec/so/porder/simple/' + str(user_id), headers=headers, params=data)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
