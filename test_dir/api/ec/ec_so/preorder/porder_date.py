"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :  porder_date.py
@Description    :  
@CreateTime     :  2023/9/5 13:38
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/9/5 13:38
"""
import weeeTest
from weeeTest import weeeConfig


class PorderDate(weeeTest.TestCase):
    def porder_date(self, headers, delivery_pickup_date):
        """购物车切换日期"""
        data = {"delivery_pickup_date": delivery_pickup_date,
                "force": 0}

        self.put(url='/ec/so/porder/date', headers=headers, json=data)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
