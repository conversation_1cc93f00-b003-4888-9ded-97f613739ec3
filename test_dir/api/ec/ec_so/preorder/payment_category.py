"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :  payment_category.py
@Description    :  
@CreateTime     :  2023/9/5 14:00
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/9/5 14:00
"""
import weeeTest
from weeeTest import weeeConfig


class PaymentCategory(weeeTest.TestCase):
    def braintree_profiles(self, headers):
        """获取profiles的值"""
        data = []
        # {"type": "address", "value": "18607 Bothell Way NE, Bothell, Washington, 98011, United States"},
        # {"type": "phone", "value": "555***5555"}
        self.post(url='/ec/payment/card/braintree/profiles', headers=headers, json=data)
        return self.response

    def payment_category(self, headers, payment_category, points=True, profile_id: str = None):
        """切换支付方式,使用积分支付"""
        data = {"payment_category": payment_category,
                "points": points,
                "profile_id": profile_id
                }

        self.put(url='/ec/so/porder/payment_category', headers=headers, json=data)
        return self.response

    def payment_category_credit_card(self, headers, data):
        self.put(url='/ec/so/porder/payment_category', headers=headers, json=data)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
