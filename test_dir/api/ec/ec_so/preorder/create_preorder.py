# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  user_bak.py
@Description    :
@CreateTime     :  2023/5/30 11:06
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/30 11:06
"""

import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath
import datetime


class CreatePreOrder(weeeTest.TestCase):

    def create_preorder(self, headers, browser_type: str = None, zipcode_type: str = None, coupon_code: str = "",
                        delivery_pickup_date: str = (datetime.datetime.now() + datetime.timedelta(days=2)).strftime(
                            '%Y-%m-%d'),
                        payment_category: str = "D", profile_id: str = None,
                        zipcode: int = 98011):
        """#create_porder"""
        data = {
            "browser_type": browser_type,
            "coupon_code": coupon_code,
            "delivery_pickup_date": delivery_pickup_date,
            "force": True,
            "payment_category": payment_category,
            "points": True,
            "profile_id": profile_id,
            "zipcode": zipcode,
            "zipcode_type": zipcode_type
        }
        self.post(url='/ec/so/porder', headers=headers, json=data)
        return self.response

    def auto_create_preorder(self, headers, zipcode: int = 98011):
        """#auto_create_preorder"""
        data = {"zipcode": zipcode}
        self.post(url='/ec/so/porder/auto', headers=headers, json=data)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
