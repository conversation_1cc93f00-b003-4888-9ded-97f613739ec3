# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  user_bak.py
@Description    :
@CreateTime     :  2023/5/30 11:06
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/30 11:06
"""

import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath
import datetime


class SetPreOrder(weeeTest.TestCase):

    def set_porder(self, headers,deal_id,user_id,
                   delivery_pickup_date: str = (datetime.datetime.now() + datetime.timedelta(days=2)).strftime('%Y-%m-%d'),
                   zipcode: int = 98011):
        """#set_porder"""
        data = {
            "addr_city": "Bothell",
            "addr_country": "2",
            "addr_firstname": "西雅图",
            "addr_lastname": "4",
            "addr_state": "84",
            "addr_zipcode": zipcode,
            "comment": "",
            "coupon_code": "",
            "deal_id": deal_id,
            "delivery_pickup_date": delivery_pickup_date,
            "email": "<EMAIL>",
            "is_point_set": 1,
            "payment_category": "P",
            "phone": "5555555555",
            "points": 20538,
            "rec_create_time": "2023-07-23 07:59:19",
            "rec_update_time": "2023-07-23 07:59:30",
            "user_id": user_id,
            "zipcode": zipcode
        }
        self.post(url='/ec/so/porder/update', headers=headers, json=data)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
