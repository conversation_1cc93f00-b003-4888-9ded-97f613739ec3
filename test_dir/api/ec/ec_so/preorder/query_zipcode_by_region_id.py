# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  user_bak.py
@Description    :
@CreateTime     :  2023/5/30 11:06
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/30 11:06
"""

import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath
import datetime


class QueryZipcodeByRegionId(weeeTest.TestCase):

    def query_zipcode_by_region_id(self, headers, zipcode, address_id: str = None):
        """# 切换zipcode，地址"""
        data = {
            "zipcode": zipcode,
            "address_id": address_id  # 非必传
        }
        self.put(url='/ec/so/porder/zipcode', headers=headers, json=data)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
