# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  user_bak.py
@Description    :
@CreateTime     :  2023/5/30 11:06
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/30 11:06
"""

import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath
import datetime


class CalculateTax(weeeTest.TestCase):

    def calculate_tax(self, headers, zipcode,catalogue_num, price, product_id, product_key, product_quantity):
        """# calculate_tax"""
        data = {
            "products": [
                {
                    "catalogue_num": catalogue_num,
                    "price": price,
                    "product_id": product_id,
                    "product_key": product_key,
                    "product_quantity": product_quantity,
                    "unit": "",
                    "volume": 0
                }
            ],
            "zipcode": zipcode
        }
        self.post(url='/ec/so/tax/calc_tax', headers=headers, json=data)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
