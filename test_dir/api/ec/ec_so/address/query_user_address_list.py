"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :  test_query_user_address_list.py
@Description    :  
@CreateTime     :  2023/7/20 16:44
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/7/20 16:44
"""
import weeeTest
from weeeTest import weeeConfig


class QueryUserAddressList(weeeTest.TestCase):
    def address_list(self, headers):
        data = None
        self.get(url="/ec/so/address/list", headers=headers, params=data)
        return self.response

    if __name__ == '__main__':
        weeeConfig.base_url = 'https://api.tb1.sayweee.net'
