"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :  get_user_address_information.py
@Description    :  
@CreateTime     :  2023/7/20 14:05
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/7/20 14:05
"""
import weeeTest
from weeeTest import weeeConfig


class GetUserAddressInformation (weeeTest.TestCase):
    def address_info(self, headers, data, address_id: int = 4307):
        data = {"address_id": address_id}
        self.post(url=f"/ec/so/address/info/{address_id}", headers=headers, json=data)
        return self.response

    if __name__ == '__main__':
        weeeConfig.base_url = 'https://api.tb1.sayweee.net'
