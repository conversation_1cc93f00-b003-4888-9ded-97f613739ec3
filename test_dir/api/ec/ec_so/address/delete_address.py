"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :  delete_address.py
@Description    :  
@CreateTime     :  2023/7/25 9:35
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/7/25 9:35
"""
import weeeTest
from weeeTest import weeeConfig
import datetime


class DeleteUserAddress (weeeTest.TestCase):
    def delete_address(self, headers, address_id: int = 4547):
        data = {
            "address_id": address_id,
        }
        self.delete(url=f"/ec/so/address/{address_id}", headers=headers, json=data)
        return self.response

    if __name__ == '__main__':
        weeeConfig.base_url = 'https://api.tb1.sayweee.net'
