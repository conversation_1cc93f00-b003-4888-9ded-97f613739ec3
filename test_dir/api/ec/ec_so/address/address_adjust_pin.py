"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :  address_adjust_pin.py
@Description    :  
@CreateTime     :  2023/7/25 10:10
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/7/25 10:10
"""
import weeeTest
from weeeTest import weeeConfig


class AddressAdjustPin (weeeTest.TestCase):
    def address_adjust_pin(self, headers, addr_firstname: str = "s722", addr_lastname: str = "2223",
                           phone: str = "21211212211"
                           , addr_address: str = "s8", addr_zipcode: int = 98011):
        """pin当前的地址信息"""
        data = {"addr_firstname": addr_firstname,
                "addr_lastname": addr_lastname,
                "phone": phone,
                "comment": "",
                "addr_address": addr_address,
                "addr_apt": "",
                "addr_city": "Bothell",
                "addr_state": "84",
                "addr_zipcode": addr_zipcode,
                "addr_country": 2}
        self.post(url="/ec/so/address/adjust/pin", headers=headers, json=data)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
