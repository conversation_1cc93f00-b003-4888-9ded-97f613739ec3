"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :  test_query_user_address_list.py
@Description    :  
@CreateTime     :  2023/7/20 16:44
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/7/20 16:44
"""
import weeeTest
from weeeTest import weeeConfig
import datetime


class ApplyUserAddressInformation(weeeTest.TestCase):
    def address_apply(self, headers, address_id: int = 4547, type: str = "normal", date: str = (datetime.date.today() + datetime.timedelta(days=1)).strftime('%Y-%m-%d')):
        data = {
              "address_id": address_id,
              "date": date,
              "force": True,
              "store_id": 0,
              "type": type,
              "vendor_id": 0
            }
        self.put(url="/ec/so/address/apply", headers=headers, json=data)
        return self.response
    def address_apply_v2(self, headers, address_id: int = 4547):
        """一起结算地址应用接口"""
        data = {
            "address_id": address_id,
            "force": False,
            "store_id": "",
            "contain_alcohol": False
            }
        self.put(url="/ec/so/address/apply/v2", headers=headers, json=data)
        return self.response

    if __name__ == '__main__':
        weeeConfig.base_url = 'https://api.tb1.sayweee.net'
