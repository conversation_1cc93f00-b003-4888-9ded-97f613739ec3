# !/usr/bin/python3
# -*- coding: utf-8 -*-


"""
<AUTHOR>  <PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  group_cart.py
@Description    :
@CreateTime     :  2024/2/29 18:06
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2024/2/29 18:06
"""

import weeeTest
from weeeTest import log,weeeConfig

class UpdateGroupShoppingCart(weeeTest.TestCase):

    def update_group_shopping_cart(self, headers, key, product_id, quantity, nick_name, user_id, source, email=None):
        #"mkpl拼单商品加购单接口"
        data = {
            "key": key,
            "products": [
                {
                    "product_id": product_id,
                    "quantity": quantity,
                    "nick_name": nick_name,
                    "user_id": user_id,
                    "email": email,
                    "source": source
                }
            ]
        }

        self.post(url='/ec/so/groupbuy/seller/items', headers=headers, json=data)
        return self.response

