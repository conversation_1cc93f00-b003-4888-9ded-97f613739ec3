# !/usr/bin/python3
# -*- coding: utf-8 -*-

"""
<AUTHOR>  <PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  group_cart.py
@Description    :
@CreateTime     :  2024/3/4 11:06
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2024/3/4 11:06
"""

import weeeTest
from weeeTest import log, weeeConfig

class QueryGroupShoppingCart(weeeTest.TestCase):

    def query_group_shopping_cart(self, headers, key: str):
        "mkpl拼单购物车查询接口"

        data = {"key": key}

        self.get('ec/so/groupbuy/seller/items', headers=headers, params=data)
        return self.response

