# !/usr/bin/python3
# -*- coding: utf-8 -*-

"""
<AUTHOR>  <PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  group_cart.py
@Description    :
@CreateTime     :  2024/3/4 11:06
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2024/3/4 11:06
"""

import weeeTest
from weeeTest import log, weeeConfig

class CancelGroupShoppingCart(weeeTest.TestCase):

    def cancel_group_shopping_cart(self, headers, key):
        "mkpl拼单取消"
        data = {"key": key}
        self.delete('/ec/so/groupbuy/seller/cancel', headers=headers, params=data)
        return self.response

