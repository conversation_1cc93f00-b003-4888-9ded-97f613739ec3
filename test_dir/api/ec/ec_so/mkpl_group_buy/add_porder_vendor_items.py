# !/usr/bin/python3
# -*- coding: utf-8 -*-

"""
<AUTHOR>  <PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  group_cart.py
@Description    :
@CreateTime     :  2024/3/4 11:06
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2024/3/4 11:06
"""

import weeeTest
from weeeTest import log, weeeConfig


class AddPorderVendorItemsToGroupOrder(weeeTest.TestCase):

    def add_porder_vendor_items_to_group_cart(self, headers, key):
        "mkpl拼单购物车同步加购接口"

        data = {"key": key}
        self.post('/ec/so/groupbuy/seller/porder/move_items', headers=headers, params=data)
        return self.response
