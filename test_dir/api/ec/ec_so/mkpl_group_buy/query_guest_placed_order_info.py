# !/usr/bin/python3
# -*- coding: utf-8 -*-

"""
<AUTHOR>  <PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  group_cart.py
@Description    :
@CreateTime     :  2024/3/4 11:06
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2024/3/4 11:06
"""

import weeeTest
from weeeTest import log, weeeConfig

class QueryGuestPlacedOrderInfo(weeeTest.TestCase):

    def query_guest_placed_order_info(self, headers,key):
        "mkpl参与者购物车"
        data = {"key": key}
        self.get('/ec/so/groupbuy/seller/guest/place', headers=headers, params=data)
        return self.response
