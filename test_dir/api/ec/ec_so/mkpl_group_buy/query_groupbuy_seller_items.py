# !/usr/bin/python3
# -*- coding: utf-8 -*-

"""
<AUTHOR>  <PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  group_cart.py
@Description    :
@CreateTime     :  2024/11/12 10:06
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2024/11/12 11:06
"""

import weeeTest
from weeeTest import log, weeeConfig

class QueryGroupbuySellerItems(weeeTest.TestCase):

    """
    Global+拼单购物车商品查询接口
    """

    def test_groupbuy_seller_items(self, headers, key: str):

        param = {
            "key": key
        }
        self.get(url='/ec/so/groupbuy/seller/items', headers=headers, params=param)
        return self.response
