# !/usr/bin/python3
# -*- coding: utf-8 -*-

"""
<AUTHOR>  <PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  seller_cart.py
@Description    :
@CreateTime     :  2023/7/25 18:06
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/7/25 18:06
"""

import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath

class MkplGroupOrderQuery(weeeTest.TestCase):

    def mkpl_group_buy(self, headers):

        """
        query mkpl group buy order ongoing

        """
        data = None

        self.get(url='/ec/so/groupbuy/seller/check/exist', headers=headers, params=data)

        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'