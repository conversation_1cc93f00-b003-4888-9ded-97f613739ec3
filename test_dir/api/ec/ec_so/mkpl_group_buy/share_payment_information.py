# !/usr/bin/python3
# -*- coding: utf-8 -*-

"""
<AUTHOR>  <PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  group_cart.py
@Description    :
@CreateTime     :  2024/3/4 11:06
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2024/3/4 11:06
"""

import weeeTest
from weeeTest import log, weeeConfig

class ShareGroupOrderPaymentInformation(weeeTest.TestCase):

    def share_group_order_payment(self, headers, order_id, guest_id):
        "mkpl拼单账单分享"

        data = {"order_id": order_id,
                "guest_id": guest_id}

        self.get('/ec/so/groupbuy/seller/host/share', headers=headers, params=data)
        return self.response

