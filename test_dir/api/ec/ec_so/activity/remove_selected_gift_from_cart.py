"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :  remove_selected_gift_from_cart.py
@Description    :  
@CreateTime     :  2023/7/20 20:41
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/7/20 20:41
"""
import weeeTest
from weeeTest import log, weeeConfig


class RemoveSelectedGiftFromCart(weeeTest.TestCase):
    def gift_cart_delete(self, headers, product_id: int = 84730, quantity: int = 1):
        """#gift_cart_delete"""

        data = [
          {
            "business_type": "",
            "gifts": [
              {
                "cart_type": "",
                "img": "",
                "product_id": product_id,
                "product_key": "",
                "quantity": quantity,
                "title": ""
              }
            ],
            "vendor_id": 0
            }
        ]
        self.delete(url="/ec/so/activity/gift/cart/delete", headers=headers, json=data)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
    RemoveSelectedGiftFromCart()
