"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :  remove_selected_gift_from_cart_v2.py
@Description    :  
@CreateTime     :  2023/7/20 20:42
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/7/20 20:42
"""
import weeeTest
from weeeTest import log, weeeConfig


class RemoveSelectedGiftFromCartV2 (weeeTest.TestCase):
    def gift_cart_delete_v2(self, headers, product_id: int = 84730, quantity: int = 1):
        """#gift_cart_delete_v2"""
        data = [{
            "cart_domain": "",
            "giftDeleteRequests": [
                {
                    "business_type": "",
                    "gifts": [
                        {
                            "cart_type": "",
                            "img": "",
                            "product_id": product_id,
                            "product_key": "",
                            "quantity": quantity,
                            "title": ""
                        }
                    ],
                    "vendor_id": 0
                }
            ]
        }]
        self.post(url="/ec/so/activity/gift/cart/delete/v2", headers=headers, json=data)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
    # a = RemoveSelectedGiftFromCartV2()
    # b = LoginHeader()
    # headers = LoginHeader().login_header(email=args[0]["login"]["email"],password= args[0]["login"]["password"])
    # a.gift_cart_delete_v2(headers, 84730, 1)
