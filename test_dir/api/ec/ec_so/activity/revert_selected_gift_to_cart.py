"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :  revert_selected_gift_to_cart.py
@Description    :  
@CreateTime     :  2023/7/20 11:06
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/7/20 11:06
"""
import weeeTest
from weeeTest import log, weeeConfig



class RevertSelectedGiftToCart(weeeTest.TestCase):
    def gift_cart_revent(self, headers, product_id, quantity):
        """#gift_cart_revent"""
        data = [
            {
                "business_type": "",
                "gifts": [
                    {
                        "cart_type": "",
                        "img": "",
                        "product_id": product_id,
                        "product_key": "",
                        "quantity": quantity,
                        "title": ""
                    }
                ],
                "vendor_id": 0
            }
        ]
        self.post(url="/ec/so/activity/gift/cart/revert", headers=headers, json=data)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
