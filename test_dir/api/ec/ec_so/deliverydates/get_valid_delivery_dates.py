"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :  get_valid_delivery_dates.py
@Description    :  
@CreateTime     :  2023/7/22 21:00
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/7/22 21:00
"""
import weeeTest
from weeeTest import weeeConfig


class GetValidDeliveryDates(weeeTest.TestCase):
    """查询可用的配送日期"""

    def so_delivery_date(self, headers):
        """so_delivery_date"""
        data = None
        self.get(url="/ec/so/delivery_date", headers=headers, params=data)
        return self.response

    def so_delivery_date_item(self, headers, product_id):
        """获取商品的可售日期"""
        data = {"product_id": product_id}
        self.get(url="/ec/so/delivery_date/item", headers=headers, params=data)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
