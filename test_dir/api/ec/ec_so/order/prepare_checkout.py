# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  user_bak.py
@Description    :
@CreateTime     :  2023/5/30 11:06
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/30 11:06
"""

import weeeTest
from weeeTest import weeeConfig


class PrepareCheckout(weeeTest.TestCase):

    def prepare_checkout(self, headers, deal_id, delivery_date, delivery_mode,
                         delivery_time_id, is_pick_up, type, vendor_id):
        """# checkout """
        data = {
            "braintree_device_data": "",
            "browser_type": "",
            "business_type": "",
            "checkoutAmount": 0,
            "checkout_type": "",
            "cvv_token": "",
            "deal_id": deal_id,
            "delivery_date": delivery_date,
            "delivery_mode": delivery_mode,
            "delivery_time_id": 0,
            "is_pick_up": is_pick_up,
            "order_note": "",
            "order_window": {
                "date": "",
                "end": 0,
                "start": 0,
                "time_desc": "",
                "time_range": ""
            },
            "page_time": 0,
            "radar_options": {},
            "referral_id": 0,
            "tip": 0,
            "type": type,
            "vendor_id": vendor_id
        }
        self.post(url='/ec/so/order/checkout/pre', headers=headers, json=data)
        return self.response

    # def prepare_checkout_v2(self, headers, deal_id, delivery_date, delivery_mode,
    #                         delivery_time_id, is_pick_up, type, vendor_id):
    #     """# checkout """
    #     data = {
    #         "braintree_device_data": "",
    #         "browser_type": "",
    #         "business_type": "",
    #         "checkoutAmount": 0,
    #         "checkout_type": "",
    #         "cvv_token": "",
    #         "deal_id": deal_id,
    #         "delivery_date": delivery_date,
    #         "delivery_mode": delivery_mode,
    #         "delivery_time_id": 0,
    #         "is_pick_up": is_pick_up,
    #         "order_note": "",
    #         "order_window": {
    #             "date": "",
    #             "end": 0,
    #             "start": 0,
    #             "time_desc": "",
    #             "time_range": ""
    #         },
    #         "page_time": 0,
    #         "radar_options": {},
    #         "referral_id": 0,
    #         "tip": 0,
    #         "type": type,
    #         "vendor_id": vendor_id
    #     }
    #     self.post(url='/ec/so/order/checkout/pre/v2', headers=headers, json=data)
    #     return self.response

    def prepare_checkout_v2(self, headers, cart_domain, index: int = 0, tip: int = 2):
        """# 预结算checkout """
        data = {"cart_domain": cart_domain, "tip_option": {"index": index, "rate": None, "tip": tip}}
        self.post(url='/ec/so/order/checkout/pre/v2', headers=headers, json=data)
        return self.response

    def prepare_checkout_wechat_or_paypal_v2(self, headers, data):
        self.post(url='/ec/so/order/checkout/pre/v2', headers=headers, json=data)
        return self.response

    def prepare_checkout_v2_marketplace(self, headers, cart_domain="grocery"):
        """marketpalce cart checkout """
        data = {"cart_domain": cart_domain}
        self.post(url='/ec/so/order/checkout/pre/v2', headers=headers, json=data)
        return self.response

    def prepare_checkout_v2_marketplace_group_order(self, headers, cart_domain="group_buy"):
        """marketpalce cart checkout """
        data = {"cart_domain": cart_domain}
        self.post(url='/ec/so/order/checkout/pre/v2', headers=headers, json=data)
        return self.response

    def mini_market_checkout_pre(self, headers, product_id: str = 30081):
        """购买mini market"""
        data = {"deal_id": 459275, "items": [{"product_id": product_id, "quantity": 1}]}
        self.post(url='/ec/so/order/mini_market/checkout/pre', headers=headers, json=data)
        return self.response

    def point_checkout_pre(self, headers, product_type: str = "point"):
        """购买积分"""
        data = {"product_type": product_type,
                "deal_id": 479518,
                "items": [{"product_id": 1959003, "quantity": 1}],
                "member_share_id": None,
                "group_invite_id": None}
        self.post(url='/ec/so/order/nocart/checkout/pre', headers=headers, json=data)
        return self.response

    def checkout_pre_no_cart(self, headers, data):
        self.post(url='/ec/so/order/nocart/checkout/pre', headers=headers, json=data)
        return self.response



if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
