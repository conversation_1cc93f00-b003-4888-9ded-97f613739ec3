# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  user_bak.py
@Description    :
@CreateTime     :  2023/5/30 11:06
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/30 11:06
"""

import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath
import datetime


class RefundOrder(weeeTest.TestCase):

    def refund_order(self, headers, order_id, refund_amount, order_product_id, product_id, product_quantity, user_max_refund_amount, is_all_return: str = True):
        """#refund_order整单申请售后"""
        data ={
            "case_category": "Received extra package",
            "case_tags": [],
            "comment": "11",
            "image_urls": [
                {
                    "url": "https://img06.test.weeecdn.com/cs/image/783/251/294302B673319184.png",
                    "file_size": 70138
                }
            ],
            "order_id": order_id,
            "percentage": 100,
            "product_quantity": 1,
            "user_max_refund_amount": user_max_refund_amount,
            "is_all_return": is_all_return,
            "products": [
                {
                    "order_product_id": order_product_id,
                    "product_id": product_id,
                    "product_quantity": product_quantity
                }
            ],
        }
        self.post(url='/ec/cs/case/create/order', headers=headers, json=data)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
