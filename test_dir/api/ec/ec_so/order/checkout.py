# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  user_bak.py
@Description    :
@CreateTime     :  2023/5/30 11:06
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/30 11:06
"""

import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath
import datetime


class Checkout(weeeTest.TestCase):

    def checkout_v1(self, headers, deal_id, delivery_date, delivery_mode):
        """# checkout_v1 """
        data = {
            "braintree_device_data": "",
            "browser_type": "",
            "business_type": "",
            "checkoutAmount": 0,
            "checkout_type": "",
            "cvv_token": "",
            "deal_id": deal_id,
            "delivery_date": delivery_date,
            "delivery_mode": delivery_mode,
            "delivery_time_id": 0,
            "is_pick_up": True,
            "order_note": "",
            "order_window": {
                "date": "",
                "end": 0,
                "start": 0,
                "time_desc": "",
                "time_range": ""
            },
            "page_time": 0,
            "radar_options": {},
            "referral_id": 0,
            "tip": 0,
            "type": "",
            "vendor_id": 0
        }
        self.post(url='/ec/so/order/checkout', headers=headers, json=data)
        return self.response

    def checkout_v3(self, headers, cart_domain, checkout_pre_id, checkoutAmount,
                    cvv_token:str=None,braintree_device_data:str=None):
        """# checkout_v3 """
        data = {
            "braintree_device_data": braintree_device_data,
            "cart_domain": cart_domain,
            "checkoutAmount": checkoutAmount,
            "checkout_pre_id": checkout_pre_id,
            "cvv_token": cvv_token,
            "order_window": {
                "date": "",
                "end": 0,
                "start": 0,
                "time_desc": "",
                "time_range": ""
            },
            "radar_options": {},
            "referral_id": 0,
            "tip_info": {
                "index": 0,
                "rate": "",
                "tip": 0
            }
        }
        self.post(url='/ec/so/order/checkout/v3', headers=headers, json=data)
        return self.response

    def checkout_wechat_or_paypal_v3(self, headers, data):
        self.post(url='/ec/so/order/checkout/v3', headers=headers, json=data)
        return self.response

    def checkout_v3_cvc(self, headers: object, cart_domain: object, checkout_pre_id: object, checkoutAmount: object,
                        cvv_token: object) -> object:
        """# checkout_v3 cvv"""
        data = {
            "braintree_device_data": "",
            "cart_domain": cart_domain,
            "checkoutAmount": checkoutAmount,
            "checkout_pre_id": checkout_pre_id,
            "cvv_token": "tokencc_bj_nqn64k_xt3x2v_g4xgpv_hfzxtx_8x3",
            "order_window": {
                "date": "",
                "end": 0,
                "start": 0,
                "time_desc": "",
                "time_range": ""
            },
            "radar_options": {},
            "referral_id": 0,
            "tip_info": {
                "index": 0,
                "rate": "",
                "tip": 0
            }
        }
        self.post(url='/ec/so/order/checkout/v3', headers=headers, json=data)
        return self.response

    def point_checkout(self, headers: object, product_type: str = "point"):
        """对积分订单进行结算"""
        data = {
            "product_type": product_type,
            "deal_id": 479518,
            "page_time": 1698992657586,
            "points": 0,
            "items": [{"product_id": 1959003, "quantity": 1}],
            "member_share_id": None,
            "payment_category": "B",
            "phone": "555***4584",
            "email": "<EMAIL>",
            "gl_tag": 314483,
            "referral_id": 0,
            "group_invite_id": None}
        self.post(url='/ec/so/order/nocart/checkout', headers=headers, json=data)
        return self.response

    def minimarket_checkout(self, headers: object, points,product_id):
        """对minimarket订单进行结算"""
        data = {
            "pick_up_point_id": 81,
            "deal_id": 459275,
            "page_time": 1698995644283,
            "points": points,
            "items": [
                {
                    "product_id": product_id,
                    "quantity": 1
                }
            ],
            "payment_category": "B",
            "phone": "857***0079",
            "email": "<EMAIL>",
            "referral_id": 0,
            "braintree_device_data": ""
        }
        self.post(url='/ec/so/order/mini_market/checkout', headers=headers, json=data)
        return self.response

    def no_cart_checkout(self, headers, data):
        self.post(url='/ec/so/order/nocart/checkout', headers=headers, json=data)
        return self.response



if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
