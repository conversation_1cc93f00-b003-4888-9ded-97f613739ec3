"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :  redeem_coupon.py
@Description    :  
@CreateTime     :  2023/7/22 19:40
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/7/22 19:40
"""
import weeeTest
from weeeTest import weeeConfig


class RedeemCoupon(weeeTest.TestCase):
    """兑换优惠券"""

    def coupons_redeem(self, headers, deal_id, amount: str, cart_domain: str = "grocery", coupon_code: str = "TESTCODE",
                       type: str = "normal", vendor_id: str = "5044",
                       ):
        """coupons_redeem"""
        data = {
            "cart_domain": cart_domain,
            "coupon_code": coupon_code,
            "amount": amount,
            "deal_id": deal_id,
            "type": type,
            "vendor_id": vendor_id
        }
        self.post(url="/ec/so/coupons/redeem", headers=headers, json=data)
        return self.response

    def coupons_redeem_v2(self, headers, coupon_code: str = "TESTCODE"):
        """coupons_redeem_v2"""
        data = {
            "coupon_code": coupon_code,
            "deal_id": 0,
            "type": "",
            "vendor_id": 0
        }
        self.post(url="/ec/so/coupons/redeem/v2", headers=headers, json=data)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
