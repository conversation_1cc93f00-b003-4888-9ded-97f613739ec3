"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :  get_shipping_free_fee_by_zipcode.py
@Description    :  
@CreateTime     :  2023/7/22 20:41
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/7/22 20:41
"""
import weeeTest
from weeeTest import weeeConfig


class GetShippingFreeFeeByZipcode (weeeTest.TestCase):
    """查询当前zipcode下运费数据"""

    def zipcode_shipping_free_fee(self, headers, zipcode: int = 98011):
        """zipcode_shipping_free_fee"""
        data = {"zipcode": zipcode}
        self.get(url="/ec/so/customer/" + str(zipcode) + "/shipping_free_fee", headers=headers, params=data)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
