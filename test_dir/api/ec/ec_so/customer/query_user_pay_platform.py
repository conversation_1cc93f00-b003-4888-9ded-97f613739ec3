"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :  query_user_pay_platform.py
@Description    :  
@CreateTime     :  2023/7/22 20:22
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/7/22 20:22
"""
import weeeTest
from weeeTest import weeeConfig


class QueryUserPayPlatform (weeeTest.TestCase):
    """查询用户支付平台"""

    def pay_platform(self, headers):
        """pay_platform"""
        data = None
        self.get(url="/ec/so/customer/pay/platform", headers=headers, params=data)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
