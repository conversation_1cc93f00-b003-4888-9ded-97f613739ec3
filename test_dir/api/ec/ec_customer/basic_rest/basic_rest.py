# -*- coding: utf-8 -*-
"""
<AUTHOR>  ji<PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@Description    :  
@CreateTime     :  2023/7/31
@Software       :  PyCharm
------------------------------------
"""
import weeeTest
from weeeTest import log, weeeConfig
from test_dir.api.ec.ec_customer.user.user_bak import User
from weeeTest.utils import jmespath

class BasicRest(weeeTest.TestCase):

    def update_session_info(self,zipcode:str,device_id:str,request_uri:str,headers):
        data= { "zipcode": zipcode,
                "device_id": device_id,
                "request_uri": request_uri}
        self.post(url='/ec/customer/basic/session',json=data,headers=headers)

    def edit_user_attributes_alias_or_username_or_email(self,type:str,value:str,headers):
        data = {"type":type,
                "value":value}
        self.post(url='/ec/customer/basic/info',json=data,headers=headers)



if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
    user = User()
    user.anon_auth()
    # user.email_login(email='<EMAIL>', password='1')