# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@Description    :  
@CreateTime     :  2023/8/4
@Software       :  PyCharm
------------------------------------
"""
import weeeTest
from weeeTest import log, weeeConfig
from test_dir.api.ec.ec_customer.user.user_bak import User


class CentralNotification(weeeTest.TestCase):

    def query_email_push(self, send_list: str, send_type: str, send_key: str, headers):
        data = {"send_list": send_list,
                "send_type": send_type,
                "send_key": send_key}
        self.post(url='/central/notification/account/notification_query', json=data, headers=headers)
        return self.response

    def central_send_message(self, headers):
        data = {
            "biz_type": "social",
            "trackId": "21321asdasda",
            "message_key": "user_comment_post",
            "from_user_id": 123,
            "to_user_id": 456,
            "extra_link": "",
            "pic_url": "",
            "language": "en",
            "params": {
                "link_url": "",
                "user_id": 123,
                "user_alias": "Jodan",
                "user_image": "",
                "post_id": 123,
                "post_title": "",
                "post_product_list": [],
                "post_user_list": [],
                "post_content": "",
                "post_tip": "",
                "extra_images": ["https://img06.weeecdn.com/item/image/831/937/1B7E03B5166A8747.png",
                                 "https://img06.weeecdn.com/item/image/233/758/428633DDC00C6822.jpg"],

            },
            "send_time": "**********"
        }
        self.post(url='/central/notification/message/send', json=data, headers=headers)

    def sms_out(self, send_phone: str, send_type: str, send_key: str, headers):
        data = {
            "send_phone": send_phone,
            "send_type": send_type,
            "send_key": send_key
        }
        self.post(url='/central/notification/account/sms/subscribe', json=data, headers=headers)

    def message_category(self, headers):
        self.get(url='/ec/customer/message/category', headers=headers)
        return self.response

    def sub_category(self, headers, category: str):
        data = {"category": category
                }

        self.get(url='/ec/customer/message/sub_category', headers=headers, params=data)
        return self.response

    def message_sub_category_list(self, headers, category_id: int = 1, start_id: int = 0, limit: int = 10):
        data = {"category_id": category_id,
                "limit": limit,
                "start_id": start_id}

        self.get(url='/ec/customer/message/list', headers=headers, params=data)
        return self.response

    def message_read(self, category_id: str, headers):
        # 设置某类消息全部已读
        data = {"category_id": category_id}
        self.post(url='/ec/customer/message/read', json=data, headers=headers)

    def message_read_list(self, id: str, headers):
        # 设置单条消息已读
        data = {"id": id}
        self.post(url='/ec/customer/message/read', json=data, headers=headers)

    def message_read_clear(self, id: str, headers):
        # 清除消息
        data = {"id": id}
        self.post(url='/ec/customer/message/clear', json=data, headers=headers)


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
    user = User()
    user.anon_auth()
    # user.email_login(email='<EMAIL>', password='1')
