# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@Description    :  
@CreateTime     :  2023/8/2
@Software       :  PyCharm
------------------------------------
"""

import weeeTest
from weeeTest import log, weeeConfig
from test_dir.api.ec.ec_customer.user.user_bak import User

class AccountEvent(weeeTest.TestCase):
    def first_time_popup(self,headers):
        self.get(url='/ec/customer/event/first_time_info',headers=headers)

    def first_time_check_email(self,value:str,headers):
        data ={"value":value}
        self.post(url='/ec/customer/event/show_message',json=data,headers=headers)

    def first_time_choose_store(self,store:str,headers):
        data ={"store":store}
        self.post(url='/ec/customer/event/choose_store',json=data,headers=headers)

    def login_without_password(self,headers):
        data ={"email": "<EMAIL>",
               "from": "first_time_use",
               "timestamp": **********,
               }
        self.post(url='/ec/customer/login/v1',json=data,headers=headers)


    def send_login_email(self,email:str,headers):
        data = {"email":email}
        self.post(url='/ec/customer/email/first_time_email',json=data,headers=headers)



if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
    user = User()
    user.anon_auth()
    # user.email_login(email='<EMAIL>', password='1')

