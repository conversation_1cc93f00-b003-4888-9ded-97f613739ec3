# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@Description    :  
@CreateTime     :  2023/8/3
@Software       :  PyCharm
------------------------------------
"""
import weeeTest
from weeeTest import log, weeeConfig
from test_dir.api.ec.ec_customer.user.user_bak import User

class AccountLiveActivity(weeeTest.TestCase):
    def account_live_activity(self,headers):
        data = {"type": "order",
                "order_id": "********",
                "uid": "7790952"}
        self.get(url='/ec/customer/account/account_live_activity',headers=headers,json=data)
        return self.response

    def update_live_activity(self,headers):
        data = {"type": "test",
                "order_id" :"1111",
                "push_token" :"testtoken"}
        self.post(url='/ec/customer/account/account_live_activity_token',json=data,headers=headers)

    def session_token_update(self,headers):
        data = {"request_uri":"https://www.sayweee.com/zh/product"}
        self.post(url='/ec/customer/basic/session',json=data,headers=headers)


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
    user = User()
    user.anon_auth()
    # user.email_login(email='<EMAIL>', password='1')
