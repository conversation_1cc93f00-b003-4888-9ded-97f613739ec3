# -*- coding: utf-8 -*-
"""
<AUTHOR>  ji<PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@Description    :  
@CreateTime     :  2023/7/27
@Software       :  PyCharm
------------------------------------
"""
import weeeTest
from weeeTest import log, weeeConfig
from test_dir.api.ec.ec_customer.user.user_bak import User

class AccountDeletion(weeeTest.TestCase):
    def account_deletion_step(self,type:str,headers):
        data = {"type":type}
        self.post(url='/ec/customer/account/account_deletion_step',json=data,headers=headers)

    def accout_deletion_loading(self,headers):
        self.get(url='/ec/customer/account/account_deletion_loading',headers=headers)


    def account_deletion_log(self,userId:int,headers):
        data ={"userId":userId}
        self.post(url='/ec/customer/account/account_deletion',json=data,headers=headers)



if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
    user = User()
    user.anon_auth()
    # user.email_login(email='<EMAIL>', password='1')
