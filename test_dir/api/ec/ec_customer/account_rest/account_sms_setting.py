# -*- coding: utf-8 -*-
"""
<AUTHOR>  ji<PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@Description    :  
@CreateTime     :  2023/8/3
@Software       :  PyCharm
------------------------------------
"""
import weeeTest
from weeeTest import log, weeeConfig
from test_dir.api.ec.ec_customer.user.user_bak import User


class AccountSmsSetting(weeeTest.TestCase):
    def account_sms_setting(self, headers):
        self.get(url='/ec/customer/account/sms_notification', headers=headers)

    def update_sms_setting(self, headers):
        data = {"type": "sms_open",
                "value": 1,
                "from": "checkout_address",
                "phone": "**********"}
        self.post(url='/ec/customer/account/sms_notification', json=data, headers=headers)


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
    user = User()
    user.anon_auth()
    # user.email_login(email='<EMAIL>', password='1')
