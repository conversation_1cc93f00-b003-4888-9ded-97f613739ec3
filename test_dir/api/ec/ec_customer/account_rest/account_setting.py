# -*- coding: utf-8 -*-
"""
<AUTHOR>  ji<PERSON><PERSON>hao
@Version        :  V1.0.0
------------------------------------
@Description    :  
@CreateTime     :  2023/8/3
@Software       :  PyCharm
------------------------------------
"""
import weeeTest
from weeeTest import log, weeeConfig
from test_dir.api.ec.ec_customer.user.user_bak import User


class AccountSetting(weeeTest.TestCase):
    def account_personal_setting(self, headers):
        self.get(url='/ec/customer/user/personal_setting', headers=headers)

    def update_account_personal_setting(self, switch_change: str, userId: str, headers):
        data = {"switch_change": switch_change,
                "userId": userId}
        self.get(url='/ec/customer/user/personal_setting', json=data, headers=headers)


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
    user = User()
    user.anon_auth()
    # user.email_login(email='<EMAIL>', password='1')
