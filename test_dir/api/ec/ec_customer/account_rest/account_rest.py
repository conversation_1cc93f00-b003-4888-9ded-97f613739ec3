# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@Description    :  
@CreateTime     :  2023/7/27
@Software       :  PyCharm
------------------------------------
"""
import weeeTest
from weeeTest import log, weeeConfig
from test_dir.api.ec.ec_customer.user.user_bak import User


class AccountRest(weeeTest.TestCase):

    def account_simple_user_info(self, headers):
        self.get(url='/ec/customer/user/info/simple', headers=headers)
        return self.response

    def select_sms_notification_setting_info(self, headers):
        self.get(url='/ec/customer/account/sms_notification', headers=headers)
        return self.response

    def edit_sms_notification_setting_info(self, headers):
        data = {"phone": "***********",
                "from": "phone_verification",
                "type": "sms_open",
                "value": 1}
        self.post(url='/ec/customer/account/sms_notification', json=data, headers=headers)
        return self.response

    def me_page_account_portal_version_v6(self, headers):
        self.get(url='/ec/customer/account/v6/portal', headers=headers)
        return self.response

    def me_page_account_portal_version_v5(self, headers):
        self.get(url='/ec/customer/account/v5/portal', headers=headers)
        return self.response

    def me_page_account_portal_version_v4(self, headers):
        self.get(url='/ec/customer/account/v4/portal', headers=headers)
        return self.response

    def me_page_account_portal_version_v3(self, headers):
        self.get(url='/ec/customer/account/v6/portal', headers=headers)
        return self.response

    def me_page_account_portal_version_v2(self, headers):
        self.get(url='/ec/customer/account/v2/portal', headers=headers)
        return self.response

    def me_page_account_portal_version_v1(self, headers):
        self.get(url='/ec/customer/account/portal', headers=headers)
        return self.response

    def get_user_profile_info(self, headers):
        self.get(url='/ec/customer/account/profile', headers=headers)
        return self.response

    def update_user_profile_info(self, headers, type: str = "description", value: str = "update the profile for test"):
        data = {"type": type,
                "value": value}
        self.post(url='/ec/customer/account/profile', json=data, headers=headers)
        return self.response

    def select_notification_setting_info(self, headers):
        self.get(url='/ec/customer/account/notification', headers=headers)
        return self.response

    def edit_notification_setting_info(self, headers):
        data = {"type": "promotion_email",
                "value": 0}
        self.post(url='/ec/customer/account/notification', json=data, headers=headers)
        return self.response

    def account_simple_info(self, headers):
        self.get(url='/ec/customer/account/info', headers=headers)
        return self.response

    def me_page_h5_account_portal(self, headers):
        self.get(url='/ec/customer/account/h5/portal', headers=headers)
        return self.response

    def me_page_pc_account_portal(self, headers):
        self.get(url='/ec/customer/account/pc/portal', headers=headers)
        return self.response

    def me_page_h5_bff_account_portal(self, headers):
        self.get(url='/ec/bff/common/account/portal', headers=headers)
        return self.response

    def me_page_pc_bff_account_portal(self, headers):
        self.get(url='/ec/bff/desktop/account/portal', headers=headers)
        return self.response

    def coupon_list_from_me_page(self, headers):
        self.get(url='/ec/customer/account/coupon', headers=headers)
        return self.response

    def get_account_rewards_level(self, headers):
        self.get(url='/ec/customer/account/rewards/level', headers=headers)
        return self.response

    def get_account_rewards_upgrade(self, headers, params):
        self.get(url='/ec/customer/account/rewards/point_upgrade', headers=headers, params=params)
        return self.response

    def rewards_point_upgrade(self, headers, type: str = "my_rewards"):
        data = {
            "type": type
        }
        self.get(url='/ec/customer/account/rewards/point_upgrade', headers=headers, params=data)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
    user = User()
    user.anon_auth()
    # user.email_login(email='<EMAIL>', password='1')
