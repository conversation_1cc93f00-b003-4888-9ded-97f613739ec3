# -*- coding: utf-8 -*-
"""
<AUTHOR>  ji<PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@Description    :  
@CreateTime     :  2023/7/31
@Software       :  PyCharm
------------------------------------
"""
import weeeTest
from weeeTest import log, weeeConfig
from test_dir.api.ec.ec_customer.user.user_bak import User
from weeeTest.utils import jmespath

class Userfaqrest(weeeTest.TestCase):

    # deprecated
    def _help_center(self,headers):
        self.get(url='/ec/customer/faq',headers=headers)
        return self.response

    def help_center(self,headers, data):
        self.post(url='/ec/cs/helpCenter/articleList',headers=headers, json=data)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
    user = User()
    user.anon_auth()
    # user.email_login(email='<EMAIL>', password='1')
    # Userfaqrest().help_center(headers=headers)