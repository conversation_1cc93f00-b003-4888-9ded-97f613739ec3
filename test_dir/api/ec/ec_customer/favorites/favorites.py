# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@Description    :  
@CreateTime     :  2023/7/26
@Software       :  PyCharm
------------------------------------
"""
import weeeTest
from weeeTest import log, weeeConfig
from test_dir.api.ec.ec_customer.user.user_bak import User
from weeeTest.utils import jmespath


class Favorites(weeeTest.TestCase):

    def update_user_head_image(self, headers):
        self.get(url='/ec/customer/user/user_image/update', headers=headers)

    def get_user_me_profile_page(self, headers):
        self.get(url='/ec/customer/user/me/profile', headers=headers)
        return self.response

    def update_user_display_language(self, headers,lan):
        self.get(url=f'/ec/customer/language/update/{lan}', headers=headers)

    def get_account_info_by_user_id(self, headers):
        data = {"api_key": "",
                "user_id":"7790952"
                }
        self.get(url='/ec/customer/user/get_account_info',params=data, headers=headers)

    def favorites_set(self, headers, target_id, type: str = "product_favorite_a"):
        """ 添加/取消 我的收藏 """
        data = {
            "type": type,  # product_favorite_a
            "target_id": target_id
        }

        self.post(url='/ec/customer/favorites/set', headers=headers, json=data)
        return self.response

    def favorites_simple(self, headers):
        """ 我的收藏商品id集合 """
        data = None

        self.get(url='/ec/customer/favorites/simple', headers=headers, json=data)
        return self.response

    def favorites_batch_batch(self, headers):
        """ 批量取消我的收藏 """
        data = None

        self.get(url='/ec/item/me/favorites/batch/batch', headers=headers, json=data)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
    user = User()
    user.anon_auth()
    # user.email_login(email='<EMAIL>', password='1')
    # user.email_register(email='test.' + user.random_word(num=5) + '@sayweee.com', password='aa123456')
