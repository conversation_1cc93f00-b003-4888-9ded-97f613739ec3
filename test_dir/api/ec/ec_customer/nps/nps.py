import weeeTest


class NPS(weeeTest.TestCase):

    def survey_info(self, headers, group):
        """ Account页面问卷调查信息 """
        data = {"group": group}  # group = a 第一轮信息, group=b 第二轮信息

        self.get(url='/ec/customer/survey/info', headers=headers, json=data)  # 返回task id

        return self.response

    def survey_answer_1(self, headers, task_id, answer, status: str = "P", ref_type: str = "account_page", ):
        """ 问卷调查第一个回答-打分 """
        data = {"task_id": task_id,
                "status": status,
                "ref_type": ref_type,
                "answer": answer
                }

        self.post(url='/ec/customer/survey/answer', headers=headers, json=data)
        return self.response

    def survey_answer_2(self, headers, task_id, status: str = "P", ref_type: str = "account_page"):
        """ 问卷调查 """
        data = {"task_id": task_id,
                "status": status,
                "ref_type": ref_type,
                "answer": {"2": {"option_ids": "1"},
                           "3": {"option_ids": "7"},
                           "4": {"option_ids": "14"},
                           "5": {"option_ids": "17"},
                           "6": {"option_ids": "24"},
                           "7": {"option_ids": "32"},
                           "8": {"option_ids": "39"},
                           "9": {"option_ids": "50"}}
                }

        self.post(url='/ec/customer/survey/answer', headers=headers, json=data)
        return self.response

    def survey_task_create_v1(self, headers, order_id, cus_survey_id):
        """ 创建调查问卷 """
        # order_id 订单id
        # cus_survey_id 需要创建哪种问卷

        data = {"order_id ": order_id,
                "cus_survey_id ": cus_survey_id,
                }

        self.post(url='/ec/customer/survey_task/create', headers=headers, json=data)
        return self.response

    def survey_task_create_v2(self, headers, invoiceId_list):
        """ 创建调查问卷 """
        # order_id 订单id
        # cus_survey_id 需要创建哪种问卷

        data = {"invoiceId_list ": invoiceId_list,
                }

        self.post(url='/ec/growth/survey/create/delivery/rating', headers=headers, json=data)
        return self.response
