# -*- coding: utf-8 -*-
"""
<AUTHOR>  sufen xu
@Version        :  V1.0.0
------------------------------------
@Description    :  
@CreateTime     :  2023/7/19
@Software       :  PyCharm
------------------------------------
"""
import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath


class User(weeeTest.TestCase):

    def anon_auth(self, headers):
        self.get(url='/ec/customer/login/token/generate')
        auth = jmespath(self.response, "object.token")
        log.info(f'匿名auth:{auth}')
        # headers["authorization"] = 'Bearer ' + auth
        return auth

    def email_register(self, headers, email: str = None, password: str = None):
        if email is None or password is None:
            raise Exception('注册的email,password不能为空')
        body = {
            "email": email,
            "password": password,
            "referral_id": 0
        }
        self.post(url='/ec/customer/login', headers=headers, json=body)
        auth = jmespath(self.response, "object.token")
        userId = jmespath(self.response, "object.user_id")
        if auth is not None and len(auth) > 0:
            log.info(f'注册成功auth:{auth}')
            headers["authorization"] = 'Bearer ' + auth
        else:
            log.info(f'注册失败,msg:{jmespath(self.response, "message")}')
        return self.response

    def email_deletion(self, headers, type: str):
        body = {
            "type": type
        }
        self.post(url='/ec/customer/account/account_deletion_step', headers=headers, json=body)
        return self.response


    def account_deletion_loading(self, headers):
        self.get(url='/ec/customer/account/account_deletion_loading', headers=headers)
        return self.response

    def account_deletion(self, UserId, headers ):
        body = {
            "userId": UserId
        }
        self.post(url='/ec/customer/account/account_deletion', headers=headers, json=body)
        return self.response

if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
    user = User()
    user.anon_auth()
    user.email_deletion(type=1)
    resp = user.email_register(email='<EMAIL>', password='aa123456')
    userId = jmespath(resp, "object.user_id")
    user.account_deletion(UserId=userId)
