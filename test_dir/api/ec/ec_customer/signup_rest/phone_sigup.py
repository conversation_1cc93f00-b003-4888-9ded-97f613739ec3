# -*- coding: utf-8 -*-
# @Time    : 2023/12/7 17:36
# <AUTHOR> name
# @Site    : 
# @File    : phone_sigup.py
import random

import weeeTest
from weeeTest import weeeConfig

class PhoneSignup(weeeTest.TestCase):

    def phone_code(self,headers,phone: str = f'{random.randint(10 ** 9, (10 ** 10) - 1)}'):
        """获取手机号验证码"""
        data = {"phone_number":phone}
        self.post(url='/ec/customer/login/phone/verify_code', headers=headers, json=data)
        return self.response

    def phone_signup(self,headers,code,phone,referral_id: int = 0):
        """手机号注册登录接口"""
        data = {
            "code":code,
            "phone_number":phone,
            "referral_id": referral_id
        }
        self.post(url='/ec/customer/login/phone', headers=headers, json=data)
        return self.response

if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'