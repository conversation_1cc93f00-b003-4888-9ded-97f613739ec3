# -*- coding: utf-8 -*-
"""
<AUTHOR>  yue.wang
@Version        :  V1.0.0
------------------------------------
@Description    :  
@CreateTime     :  2023/12/1
@Software       :  PyCharm
------------------------------------
"""
import random

import weeeTest
from weeeTest import weeeConfig

class EduSignup(weeeTest.TestCase):

    def edu_signup(self, headers, email: str = f'{random.randint(10 ** 9, (10 ** 10) - 1)}@osu.edu',
                     password: str = "A1234567", referral_id: int = 0):
        """# edu学生邮箱注册"""
        data = {
            "email": email,
            "password": password,
            "referral_id": referral_id,
        }
        self.post(url='/ec/customer/signup/student', headers=headers, json=data)
        return self.response

    def get_educode(self,headers):
        """获取验证码"""
        self.get(url='/ec/customer/email/user_student_url/send', headers=headers)
        return self.response

    def edu_verify(self,headers,key):
        data = {"key":key}
        self.post(url='/ec/customer/email/user_student_url', headers=headers,json=data)
        return self.response

    def coupon_list(self,headers,status):
        data = {"status": status}
        self.post(url='/ec/promotion/coupon/list', headers=headers, json=data)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
    user = EduSignup()
    headers = {
        "Content-Type": "application/json;charset=UTF-8",
        "authorization": "",
        "origin": "https://tb1.sayweee.net",
        "b-cookie": "446420",
        "platform": "pc",
        "lang": "en",
        "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "zipcode": "98011"}
    user.edu_signup(headers=headers, email="<EMAIL>", password="A1234567")