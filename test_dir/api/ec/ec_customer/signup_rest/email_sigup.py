# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  user_bak.py
@Description    :
@CreateTime     :  2023/5/30 11:06
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/30 11:06
"""
import random
import time

import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath


class EmailSignup(weeeTest.TestCase):

    def email_signup(self, headers, email: str = f'{random.randint(10 ** 9, (10 ** 10) - 1)}@autest.com',
                     password: str = "A1234567", referral_id: int = 0,
                     referrer_id: int = 0):
        """# email_signup"""

        data = {
            "channelFrom": "",
            "channelID": "",
            "email": email,
            "epCode": "",
            "ep_partner": "",
            "ftu": "",
            "ftu_flags": "",
            "ftu_params": "",
            "ftu_popup": False,
            "ftu_source": "",
            "ftu_url": "",
            "password": password,
            "referral_id": referral_id,
            "referrer_id": referrer_id,
            "source": ""
        }
        self.post(url='/ec/customer/signup', headers=headers, json=data)
        return self.response

    def email_signup_v1(self, headers, email: str = f'{random.randint(10 ** 9, (10 ** 10) - 1)}@autest.com',
                        password: str = "A1234567", referral_id: int = 0,
                        referrer_id: int = 0, ftu: str = "popupallb"):
        """# no password email sign up"""

        data = {
            "email": email,
            "ep_partner": "",
            "from": "first_time_use",
            "recaptcha_response": "",
            "referral_id": referrer_id,
            "source": "newFlow",
            "timestamp": "",
            "ftu": ftu,  # "popupallb","popupall"
            "ftu_flags": 48,
            "ftu_popup": True,
            "ftu_url": "https://tb1.sayweee.net/zh/",
            "ftu_params": "",
            "referrer_id": referrer_id

        }

        self.post(url='/ec/customer/signup/v1', headers=headers, json=data)
        return self.response

    def email_signup_web_v1(self, headers, data):
        """# no password email sign up by web """
        self.post(url='/ec/customer/signup/email/web/v1', headers=headers, json=data)
        return self.response

    def email_signup_app_v1(self, headers, email: str = f'{random.randint(10 ** 9, (10 ** 10) - 1)}@autest.com',
                            password: str = "A1234567", referral_id: int = 0,
                            referrer_id: int = 0, ftu: str = "popupallb"):
        """# no password email sign up by app """

        data = {
            "email": email,
            "ep_partner": "",
            "from": "first_time_use",
            "recaptcha_response": "",
            "referral_id": referrer_id,
            "source": "newFlow",
            "timestamp": "",
            "ftu": ftu,  # "popupallb","popupall"
            "ftu_flags": 48,
            "ftu_popup": True,
            "ftu_url": "https://tb1.sayweee.net/zh/",
            "ftu_params": "",
            "referrer_id": referrer_id

        }

        self.post(url='/ec/customer/signup/email/app/v1', headers=headers, json=data)
        return self.response

    def email_signup_code_v1(self, headers, email: str = f'{random.randint(10 ** 9, (10 ** 10) - 1)}@autest.com',
                             password: str = "A1234567", referral_id: int = 0,
                             referrer_id: int = 0, ftu: str = "popupallb"):
        """# no password email sign up by code """

        data = {
            "email": email,
            "ep_partner": "",
            "from": "first_time_use",
            "recaptcha_response": "",
            "referral_id": referrer_id,
            "source": "newFlow",
            "timestamp": "",
            "ftu": ftu,  # "popupallb","popupall"
            "ftu_flags": 48,
            "ftu_popup": True,
            "ftu_url": "https://tb1.sayweee.net/zh/",
            "ftu_params": "",
            "referrer_id": referrer_id

        }

        self.post(url='/ec/customer/signup/email/code/v1', headers=headers, json=data)
        return self.response

    def email_signup_student(self, headers, email: str = f'{random.randint(10 ** 9, (10 ** 10) - 1)}@autest.com',
                             password: str = "A1234567", referral_id: int = 0,
                             referrer_id: int = 0, ftu: str = "popupallb"):
        """# student email sign up"""

        data = {
            "ads": {},
            "channelFrom": "",
            "channelID": "",
            "code": "",
            "email": "<EMAIL>",
            "encryptEmailString": "",
            "encryptPhoneNumber": "",
            "ep_partner": "",
            "from": "",
            "ftu": "",
            "ftu_flags": "",
            "ftu_params": "",
            "ftu_popup": True,
            "ftu_source": "",
            "ftu_url": "",
            "key": "",
            "no_password_from": "",
            "password": "",
            "pushToken": "",
            "recaptcha_response": "",
            "referral_id": 0,
            "referrer_id": 0,
            "source": "",
            "timestamp": 0
        }

        self.post(url='/ec/customer/signup/student', headers=headers, json=data)
        return self.response

    def email_signup_event_ep(self, headers, email: str = f'{random.randint(10 ** 9, (10 ** 10) - 1)}@autest.com',
                              password: str = "A1234567", referral_id: int = 0,
                              referrer_id: int = 0, ftu: str = "popupallb"):
        """# student email sign up"""

        data = {
            "ads": {},
            "channelFrom": "",
            "channelID": "",
            "code": "",
            "email": "<EMAIL>",
            "encryptEmailString": "",
            "encryptPhoneNumber": "",
            "ep_partner": "",
            "from": "",
            "ftu": "",
            "ftu_flags": "",
            "ftu_params": "",
            "ftu_popup": True,
            "ftu_source": "",
            "ftu_url": "",
            "key": "",
            "no_password_from": "",
            "password": "",
            "pushToken": "",
            "recaptcha_response": "",
            "referral_id": 0,
            "referrer_id": 0,
            "source": "",
            "timestamp": 0
        }

        self.post(url='/ec/customer/signup/event/ep', headers=headers, json=data)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
    user = EmailSignup()

