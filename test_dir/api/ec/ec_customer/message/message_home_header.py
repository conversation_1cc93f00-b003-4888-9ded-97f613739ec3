"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :  message_home_header.py
@Description    :  
@CreateTime     :  2023/11/10 16:40
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/11/10 16:40
"""
import datetime

import weeeTest
from weeeTest import weeeConfig


class MessageHomeHeader(weeeTest.TestCase):
    """首页合单提醒"""
    def message_home_header(self, headers, version: str = "v2", lang: str = "zh", date:  str = (datetime.date.today() + datetime.timedelta(days=1)).strftime('%Y-%m-%d')):
        data = {
            "version": version,
            "lang": lang,
            "date": date}
        self.get(url="/ec/customer/messages/home_header", headers=headers, params=data)
        return self.response

    if __name__ == '__main__':
        weeeConfig.base_url = 'https://api.tb1.sayweee.net'
