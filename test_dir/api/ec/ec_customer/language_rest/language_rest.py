# -*- coding: utf-8 -*-
"""
<AUTHOR>  ji<PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@Description    :  
@CreateTime     :  2023/7/27
@Software       :  PyCharm
------------------------------------
"""
import weeeTest
from weeeTest import log, weeeConfig
from test_dir.api.ec.ec_customer.user.user_bak import User


class LanguageRest(weeeTest.TestCase):

    def update_account_language(self, headers, lang: str = "en"):
        data = None
        # print(f'/ec/customer/language/update/{lang}')
        self.get(url=f'/ec/customer/language/update/{lang}', headers=headers, params=data)
        return self.response

    def user_update_account_lang(self,headers,lang):
        self.get(url=f'/ec/customer/language/update/{lang}', headers=headers)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
    user = User()
    user.anon_auth()
    # user.email_login(email='<EMAIL>', password='1')
