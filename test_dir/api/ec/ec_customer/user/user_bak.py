# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON> chen
@Version        :  V1.0.0
------------------------------------
@File           :  user_bak.py
@Description    :
@CreateTime     :  2023/5/15 11:06
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/15 11:06
"""

import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath


class User(weeeTest.TestCase):

    def anon_auth(self):
        self.get(url='/ec/customer/login/token/generate')
        auth = jmespath(self.response, "object.token")
        log.info(f'匿名auth:{auth}')
        # headers["authorization"] = 'Bearer ' + auth
        return auth

    def email_login(self, headers, email: str = None, password: str = None):
        if email is None or password is None:
            raise Exception('登录的email,password不能为空')
        data = {
            "email": email,
            "password": password,
            "referral_id": 0
        }
        self.post(url='/ec/customer/login/email', headers=headers, json=data)
        auth = jmespath(self.response, "object.token")
        if auth is not None and len(auth) > 0:
            log.info(f'登录成功auth:{auth}')
            headers["authorization"] = 'Bearer ' + auth

        else:
            log.info(f'登录失败,msg:{jmespath(self.response, "message")}')
        return self.response

    def email_register(self, headers, email: str = None, password: str = None):
        if email is None or password is None:
            raise Exception('注册的email,password不能为空')
        data = {
            "email": email,
            "password": password,
            "referral_id": 0
        }
        self.post(url='/ec/customer/signup', headers=headers, json=data)
        auth = jmespath(self.response, "object.token")
        if auth is not None and len(auth) > 0:
            log.info(f'注册成功auth:{auth}')
            headers["authorization"] = 'Bearer ' + auth
        else:
            log.info(f'注册失败,msg:{jmespath(self.response, "message")}')
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
    user = User()
    user.anon_auth()
    # user.email_login(email='<EMAIL>', password='aa123456')

    # user.email_register(email='test.' + user.random_word(num=5) + '@sayweee.com', password='aa123456')