# -*- coding: utf-8 -*-
"""
<AUTHOR>  sufen xu
@Version        :  V1.0.0
------------------------------------
@Description    :  
@CreateTime     :  2023/7/19
@Software       :  PyCharm
------------------------------------
"""
import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath


class User(weeeTest.TestCase):

    def anon_auth(self):
        self.get(url='/ec/customer/login/token/generate')
        auth = jmespath(self.response, "object.token")
        log.info(f'匿名auth:{auth}')
        # headers["authorization"] = 'Bearer ' + auth
        return auth

    def email_exsit(self, headers, email: str = None):
        body = {
            "email": email
        }
        self.post(url='/ec/customer/event/show_message', headers=headers, json=body)
        auth = jmespath(self.response, "object.token")


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
    user = User()
    user.anon_auth()
    user.email_exsit(email='<EMAIL>')

    # user.email_register(email='test.' + user.random_word(num=5) + '@sayweee.com', password='aa123456')