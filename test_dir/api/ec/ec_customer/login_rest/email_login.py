# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  user_bak.py
@Description    :
@CreateTime     :  2023/5/30 11:06
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/30 11:06
"""
import datetime
import json
import time

import weeeTest
from weeeTest import log, weeeConfig
from test_data.ec.simple.writejenkinslog import write_debug_log_on_jenkins


class MyEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, datetime.datetime):
            print("MyEncoder-datetime.datetime")
            return obj.strftime("%Y-%m-%d %H:%M:%S")
        if isinstance(obj, bytes):
            return str(obj, encoding='utf-8')
        if isinstance(obj, int):
            return int(obj)
        elif isinstance(obj, float):
            return float(obj)
        # elif isinstance(obj, array):
        #    return obj.tolist()
        else:
            return super(MyEncoder, self).default(obj)


class EmailLogin(weeeTest.TestCase):

    def email_login(self, headers, email: str, password: str, referral_id: int = 0,
                    referrer_id: int = 0):
        """# email_login"""
        data = {
            "channelFrom": "",
            "channelID": "",
            "email": email,
            "epCode": "",
            "ep_partner": "",
            "ftu": "",
            "ftu_flags": "",
            "ftu_params": "",
            "ftu_popup": False,
            "ftu_source": "",
            "ftu_url": "",
            "password": password,
            "referral_id": referral_id,
            "referrer_id": referrer_id,
            "source": ""
        }

        self.post(url='/ec/customer/login/email', headers=headers, json=data)
        # write_debug_log_on_jenkins("./logs/login_response_request.txt", " request: " + json.dumps(dict(self.response_all.request.headers), cls=MyEncoder, indent=4) + "\n")

        return self.response

    def phone_login(self, headers, phone_number: str,  referral_id: int = 0):
        """# phone_login"""
        data = {
            "ads": {},
            "channelFrom": "",
            "channelID": "",
            "code": "",
            "encryptEmailString": "",
            "encryptPhoneNumber": "",
            "ep_partner": "",
            "ftu": "",
            "ftu_flags": "",
            "ftu_params": "",
            "ftu_popup": True,
            "ftu_source": "",
            "ftu_url": "",
            "phone_number": phone_number,
            "pushToken": "",
            "referral_id": referral_id,
            "referrer_id": referral_id,
            "source": ""
        }

        self.post(url='/ec/customer/login/phone', headers=headers, json=data)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
