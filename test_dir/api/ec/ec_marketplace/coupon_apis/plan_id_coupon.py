# !/usr/bin/python3
# -*- coding: utf-8 -*-

"""
<AUTHOR>  <PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  seller_cart.py
@Description    :
@CreateTime     :  2023/7/24 17:06
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/7/24 17:06
"""

import weeeTest
from weeeTest import  weeeConfig


class CouponsSellers(weeeTest.TestCase):

    def coupons_sellers(self, headers, vender_id: int, plan_id: int):
        """mkpl领取新人优惠券,根据商家的plan_id领取"""
        data = {"plan_id":plan_id}
        self.post(url=f'/ec/marketplace/coupons/sellers/{vender_id}', headers=headers, json=data)
        return self.response

