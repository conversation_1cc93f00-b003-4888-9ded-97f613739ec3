import weeeTest
from weeeTest import weeeConfig


class MkplWaterfallFeed(weeeTest.TestCase):
    # 获取mkpl Global+页面的waterfall数据
    def marketplace_global_feed(self, headers, recommend_session: str, key: str = "feature",
                                dataobject_key: str = "ds_content_feed_v2", from_page: str = "global",
                                page_num: int = 1):
        data = {"recommend_session": recommend_session,
                "key": key,  # "feature"
                "dataobject_key": dataobject_key,  # "ds_content_feed_v2",
                "from_page": from_page,  # "global"
                "page_num": page_num
                }

        self.get(url='/ec/marketplace/global/feed', headers=headers, params=data)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
