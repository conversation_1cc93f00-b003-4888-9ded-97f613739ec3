import weeeTest
from weeeTest import weeeConfig


class GlobalCategorySellers(weeeTest.TestCase):

    def global_category_sellers(self, headers, category_type: str, category_nums: str, zipcode: str,
                              special_category_nums: str):
        """# 获取waterfall的符合商家,category_nums和special_category_nums二传一"""
        data = {
            "category_type": category_type,
            "zipcode": zipcode,
            "category_nums": [category_nums],
            "special_category_nums": [special_category_nums],
            "excluded_seller_ids": [],
            "limit": 20
        }

        self.post(url='/ec/marketplace/global/category/sellers', headers=headers, body=data)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
