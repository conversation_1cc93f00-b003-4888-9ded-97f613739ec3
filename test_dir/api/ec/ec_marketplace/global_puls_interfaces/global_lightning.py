import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath


class GlobalLightning(weeeTest.TestCase):

    def global_lightning(self, headers, dataobject_key):
        """  # 获取waterfall的秒杀 """
        param = {"dataobject_key": dataobject_key}
        self.get(url='/ec/marketplace/lightning/carousel', headers=headers, params=param)
        return self.response

    def global_lightning_for_desktop(self, headers, dataobject_key, zipcode:int =None, date=None, lang=None):
        """  # 获取waterfall的秒杀 """
        param = {"dataobject_key": dataobject_key,
                 "zipcode": zipcode,
                 "date": date,
                 "lang": lang}
        self.get(url='/ec/marketplace/lightning/carousel', headers=headers, params=param)
        return self.response

    def global_lightning_for_mobile(self, headers, zipcode: int = None, date=None, lang=None):
        """  # 获取waterfall的秒杀 """
        param = {"zipcode": zipcode,
                 "date": date,
                 "lang": lang}
        self.get(url='/ec/marketplace/lightning/carousel', headers=headers, params=param)
        return self.response

    def global_lightning_landing(self, headers, page_no, page_size, date):
        """  # MKPL秒杀landing页 """
        param = None
        self.get(url='/ec/marketplace/lightning/landing', headers=headers, params=param)
        return self.response

    def grocery_waterfall_mkpl_lightning(self, headers):
        """  # 生鲜首页waterfall 432实验-MKPL秒杀 """
        param = None
        self.get(url='/ec/marketplace/lightning/waterfall', headers=headers, params=param)
        return self.response

if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
