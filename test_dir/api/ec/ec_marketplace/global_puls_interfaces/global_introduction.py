import weeeTest
from weeeTest import weeeConfig
from test_dir.api.ec.ec_customer.login_rest.email_login import EmailLogin


class GlobalIntroduction(weeeTest.TestCase):

    # 获取Global商家的介绍信息
    def global_introduction(self, headers):
        data=None
        self.get(url='/ec/marketplace/global/introduction', headers=headers, params=data)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'

