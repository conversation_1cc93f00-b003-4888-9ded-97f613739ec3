import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath
import datetime


class GetGlobalAnimatedLabel(weeeTest.TestCase):

    def get_global_animated_label(self, headers, zipcode: str = None):
        """# get_global_animated_label"""
        data = {'zipcode':  zipcode }
        self.get(url='/ec/marketplace/global/category/labels', headers=headers, params=data)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
