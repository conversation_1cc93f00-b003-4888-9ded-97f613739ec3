from weeeTest import weeeConfig

import weeeTest


class ListAllAvailableSellerIds(weeeTest.TestCase):

    def list_all_available_seller_ids(self, headers, zipcode: str = None):
        """ # 获取waterfall的商家列表"""

        data = {"zipcode": zipcode}
        self.get(url='/ec/marketplace/global/seller/id/waterfall', headers=headers, params=data)
        return self.response
        # seller_id=self.response["object"]
        # return seller_id

    def list_all_available_seller_ids_v2(self, headers, zipcode: str = None):
        """ # 获取 商家列表"""

        data = {"zipcode": zipcode}
        self.get(url='/ec/marketplace/global/seller/id/all', headers=headers, params=data)
        return self.response
        # seller_id=self.response["object"]
        # return seller_id


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
    user = ListAllAvailableSellerIds()
