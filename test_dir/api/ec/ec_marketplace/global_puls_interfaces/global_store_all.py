import datetime

import weeeTest
from weeeTest import weeeConfig


# page_key=list(origin.values())
# dataobject_key=Global_page_key().cms_page_getPage(headers,page_key )
# dataobject_key1=Global_page_key().cms_page_getPage1(headers,page_key )
class GlobalStore(weeeTest.TestCase):

    # 获取Global所有商家信息
    def global_store_all(self, headers, origin_ids: int, dataobject_key: str, zipcode: int = 98011,
                         pagesize: int = 10, page_no: int = 0,
                         lang: str = "en",
                         date: str = (datetime.date.today() + datetime.timedelta(days=1)).strftime('%Y-%m-%d')):
        data = {
            "origin_ids": origin_ids,
            "dataobject_key": dataobject_key,
            "lang": lang,
            "date": date,
            "zipcode": zipcode,
            "pagesize": pagesize,
            "page_no": page_no
        }

        self.get(url='/ec/marketplace/global/store/all', headers=headers, params=data)
        return self.response
        # 获取Global所有商家信息

    def global_store_all_v2(self, headers, origin_ids: int, dataobject_key: str,
                            pagesize: int = 10, page_no: int = 0):
        # 获取Global所有商家信息 v2没有date
        data = {
            "origin_ids": origin_ids,
            "zipcode": headers['Zipcode'],
            "dataobject_key": dataobject_key,
            "pagesize": pagesize,
            "page_no": page_no
        }

        self.get(url='/ec/marketplace/global/store/all', headers=headers, params=data)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
    user = GlobalStore()
