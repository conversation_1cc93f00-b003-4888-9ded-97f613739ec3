from weeeTest import weeeConfig
import weeeTest


class GetTheGlobalSellerInfo(weeeTest.TestCase):
    """  # 获取商家信息  """
    def get_the_global_seller_info(self, headers, seller_id: int, zipcode: str):

        data = {"seller_id": seller_id, "zipcode": zipcode}
        self.get(url=f'/ec/marketplace/global/store/{seller_id}/info', headers=headers, json=data)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
