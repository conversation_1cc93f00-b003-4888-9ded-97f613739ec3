import weeeTest
from weeeTest import weeeConfig


class GlobalHomeTabStatus(weeeTest.TestCase):
    # 获取global+的 组件状态
    def global_home_tab_status(self, headers, zipcode: str | None ):
        data = {
            "zipcode": zipcode

        }

        self.get(url='/ec/marketplace/global/home/<USER>/tab', headers=headers, params=data)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
