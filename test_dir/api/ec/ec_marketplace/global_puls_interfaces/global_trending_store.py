import weeeTest
from weeeTest import weeeConfig
from test_dir.api.ec.ec_customer.login_rest.email_login import EmailLogin


class GlobalTrendingStore(weeeTest.TestCase):
    # 获取global+的热销商家列表(已废弃)
    def global_trending_store(self, headers, product_ids: list, seller_id: int, source_key: str):
        data = {"sellers": [
            {
                "product_ids": product_ids,
                "seller_id": seller_id,
                "source_key": source_key
            }
        ],
            "zipcode": ""}
        self.post(url='/ec/marketplace/global/store/info', headers=headers, params=data)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
