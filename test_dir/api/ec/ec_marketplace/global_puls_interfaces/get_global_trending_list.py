import weeeTest
from weeeTest import weeeConfig


class GetGlobalTrendingList(weeeTest.TestCase):
    # 获取global+的热销商家列表
    def get_global_trending_list(self, headers, zipcode: str | None, source_key: str | None, limit: int = 10):
        data = {
            "zipcode": zipcode,
            "limit": limit,
            "source_key": source_key
        }

        self.get(url='/ec/marketplace/global/store/trending/list', headers=headers, params=data)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
