import weeeTest
from weeeTest import weeeConfig
from test_dir.api.ec.ec_customer.login_rest.email_login import EmailLogin


class GlobalNewStore(weeeTest.TestCase):
    # 获取global+的新上架商家列表 New Stores
    def global_new_store(self, headers, zipcode: str = None, source_key: str = None,
                                 dataobject_key: str = None, lang: str = "en", date: str = None):
        data = {
            "zipcode": zipcode,
            "dataobject_key": dataobject_key,
            "lang": lang,
            "date": date,
            "source_key": source_key
        }
        self.get(url='/ec/marketplace/global/store/new/list', headers=headers, params=data)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
