from weeeTest import weeeConfig
import weeeTest


class GetSellerInfos(weeeTest.TestCase):
    """  # 获取waterfall的商家卡片信息  """

    def get_seller_infos(self, headers, seller_ids: list, zipcode: str):
        # 确保seller_ids是一个列表
        data = {"seller_ids": seller_ids, "zipcode": zipcode}
        self.post(url='/ec/marketplace/global/sellers', headers=headers, json=data)
        # print(self.response)
        # print(self.response["object"][2])
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'

