import weeeTest
from weeeTest import weeeConfig


class GlobalExclusiveCarousel(weeeTest.TestCase):
    # 获取global+的exclusive组件数据
    def global_exclusive_carousel(self, headers, zipcode: str | None, offset: int | None, limit: int = 10):
        data = {
            "zipcode": zipcode,
            "limit": limit,
            "offset": offset
        }

        self.get(url='/ec/marketplace/global/exclusive/carousel', headers=headers, params=data)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
