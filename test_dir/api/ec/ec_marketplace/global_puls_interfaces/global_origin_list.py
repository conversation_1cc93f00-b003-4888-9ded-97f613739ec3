import weeeTest
from weeeTest import weeeConfig
from test_dir.api.ec.ec_customer.login_rest.email_login import EmailLogin



class Globaloriginlist(weeeTest.TestCase):

    # 获取Global的地区信息
    def global_origin_list(self, headers):
        self.get(url='/ec/marketplace/global/origin/list', headers=headers)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
    user = Globaloriginlist()

