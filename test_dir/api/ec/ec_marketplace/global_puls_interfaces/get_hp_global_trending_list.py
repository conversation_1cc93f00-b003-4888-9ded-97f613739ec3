import weeeTest
from weeeTest import weeeConfig
from test_dir.api.ec.ec_customer.login_rest.email_login import EmailLogin


class GetHpGlobalTrendingList(weeeTest.TestCase):
    # 获取global+的热销商家列表(homepage)
    def get_hp_global_trending_list(self, headers, source_key: str | None):
        data = {"source_key": source_key}
        self.get(url='/ec/marketplace/global/store/trending/home', headers=headers, params=data)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
