import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath


class Globalpromos(weeeTest.TestCase):

    def global_promos(self, headers):
        """  # 获取waterfall的Banner """
        data = None
        self.get(url='/ec/marketplace/global/promos', headers=headers, params=data)
        return self.response

    def global_promos_data(self, headers, data=None):
        """  dataobject_key 获取waterfall的Banner """
        data = None
        self.get(url='/ec/marketplace/global/promos', headers=headers, params=data)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
    user = Globalpromos()
