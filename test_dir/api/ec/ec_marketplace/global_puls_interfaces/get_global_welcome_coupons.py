# !/usr/bin/python3
# -*- coding: utf-8 -*-

"""
<AUTHOR>  <PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  seller_cart.py
@Description    :
@CreateTime     :  2023/7/24 17:06
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/7/24 17:06
"""

import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath


class GetGlobalWelcomeCoupons(weeeTest.TestCase):

    def get_global_welcome_coupons(self, headers, zipcode: int, source_key: str | None,
                                   tab_key: str | None):
        """waterfall的新人优惠券列表"""
        """tab_key(all,expiring_soon,claimed)和source_key("global_waterfall")二传一"""
        data = {
            "zipcode": zipcode,
            "source_key": source_key,
            "tab_key": tab_key}
        self.get(url='/ec/marketplace/global/welcome/coupons', headers=headers, params=data)
        return self.response

    def get_global_welcome_coupons_cms(self, headers, source_key: str, dataobject_key):
        """waterfall coupon组件详情"""

        data = {
            "source_key": source_key,
            "dataobject_key": dataobject_key}
        self.get(url='/ec/marketplace/global/welcome/coupons', headers=headers, params=data)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
