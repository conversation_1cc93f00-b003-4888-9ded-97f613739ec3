from weeeTest import weeeConfig
import weeeTest


class HomeMkplCarousel(weeeTest.TestCase):
    """  # 首页mkpl相关组件  """

    def fresh_daily_carousel(self, headers, dataobject_key: str = None, key: str = None,
                             from_page: str = "home"):
        # 首页每日现做组件
        data = {
            "from_page": from_page,
            "key": key,
            "dataobject_key": dataobject_key
        }
        self.get(url='/ec/marketplace/freshdaily/carousel/feed', headers=headers, params=data)
        return self.response

    def top_feed(self, headers, dataobject_key: str = None, key: str = None, position: int = None):
        # 首页 全球购 · 年货集惠组件
        data = {
            "position": position,
            "key": key,
            "dataobject_key": dataobject_key
        }
        self.get(url='/ec/marketplace/global/carousel/top/feed', headers=headers, params=data)
        return self.response

    def global_ranking_top_feed(self, headers, tab: str, key: str = None):
        # global 榜单页面
        data = {
            "tab": tab,
            "page_num": 1,
            "key": key,  # 分类key
            "limit": 20
        }
        self.get(url='/ec/marketplace/global/ranking/top/feed', headers=headers, params=data)
        return self.response

    def fbw_freshdaily_feed(self, headers, recommend_session, filter_sub_category: str = "bakery",
                            key: str = "feature", dataobject_key: str = "ds_content_feed_v2",
                            from_page: str = "global", page_num: int = 1):
        # /mkpl/bakery/landing
        data = {
            "filter_sub_category": filter_sub_category,
            "recommend_session": recommend_session,
            "key": key,
            "dataobject_key": dataobject_key,
            "from_page": from_page,
            "limit": 20,
            "page_num": page_num
        }
        self.get(url='/ec/item/v3/search/fbw/freshdaily/feed', headers=headers, params=data)
        return self.response

    def fbw_freshdaily_shop_list(self, headers, category_num: str = "09"):
        # /mkpl/bakery/landing
        data = {
            "category_num": category_num  # 18 餐馆卤味 09 现做面包
        }
        self.get(url='/ec/marketplace/freshdaily/shop/list', headers=headers, params=data)
        return self.response
