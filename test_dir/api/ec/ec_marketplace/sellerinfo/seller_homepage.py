# !/usr/bin/python3
# -*- coding: utf-8 -*-

"""
<AUTHOR>  <PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  seller_desc.py
@Description    :
@CreateTime     :  2023/7/19 19:06
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/7/19 19:06
"""

import weeeTest
from weeeTest import weeeConfig


class VendorHomePage(weeeTest.TestCase):

    def vendor_homepage(self, headers, vendor_id, title: str):
        data = {
            "seller_ids": vendor_id,
            "title": title
        }

        self.post(url='/ec/marketplace/vendor/seller/homepage/simple', headers=headers, json=data)
        return self.response

    def vendor_page_header(self, headers, vendor_id, biz_type=None):
        param = {}
        if biz_type is not None:
            param['biz_type'] = biz_type
        self.get(url=f'/ec/marketplace/vendor/seller/homepage/{vendor_id}', headers=headers, params=param)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
