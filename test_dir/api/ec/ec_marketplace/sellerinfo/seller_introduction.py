# !/usr/bin/python3
# -*- coding: utf-8 -*-

"""
<AUTHOR>  <PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  seller_desc.py
@Description    :
@CreateTime     :  2023/7/20 19:06
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/7/20 19:06
"""

import weeeTest
from weeeTest import log, weeeConfig
from weeeTest.utils import jmespath


class VendorIntroduction(weeeTest.TestCase):

    def vendor_introduction(self, headers, vendor_id: str):
        data = None

        self.get(url='/ec/marketplace/vendor/seller/introduction/v2/' + vendor_id, headers=headers, params=data)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
