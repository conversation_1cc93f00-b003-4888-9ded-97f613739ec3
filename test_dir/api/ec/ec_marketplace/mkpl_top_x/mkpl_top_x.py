# !/usr/bin/python3
# -*- coding: utf-8 -*-

import weeeTest
from weeeTest import weeeConfig


class MkplTopX(weeeTest.TestCase):
    """mkpl top x落地页接口"""

    def mkpl_top_ranking(self, headers, tab: str, key: str, page_num: int = 1,
                         limit: int = 20):
        param = {
            "tab": tab,
            "page_num": page_num,
            "key": key,
            "limit": limit

        }

        self.get(url='/ec/marketplace/global/ranking/top/feed', headers=headers, params=param)
        return self.response

    """mkpl top x首页接口"""

    def mkpl_top_carousel(self, headers, position: int, key: str | None, dataobject_key: str | None):
        param = {
            "position": position,
            "key": key,
            "dataobject_key": dataobject_key
        }

        self.get(url='/ec/marketplace/global/carousel/top/feed', headers=headers, params=param)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
