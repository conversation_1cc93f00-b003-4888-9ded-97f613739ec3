# !/usr/bin/python3
# -*- coding: utf-8 -*-

"""
<AUTHOR>  <PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  seller_desc.py
@Description    :
@CreateTime     :  2023/7/18 19:06
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/7/18 19:06
"""

import weeeTest
from weeeTest import weeeConfig


class MkplSearch(weeeTest.TestCase):

    def seller_search_keyword(self, headers, seller_id, date: str, lang, zipcode, filter_key_word: str, sign, force=0,
                              limit=20, offset=0, trigger_type="search_active"):
        """
        商家页搜索
        :param headers:
        :param seller_id:
        :param date:
        :param lang:
        :param zipcode:
        :param filter_key_word:
        :param sign:
        :param force:
        :param limit:
        :param offset:
        :param trigger_type:
        :return:
        """
        param = {
            "seller_id": seller_id,
            "date": date,
            "lang": lang,
            "zipcode": zipcode,
            "filter_key_word": filter_key_word,
            "sign": sign,
            "force": force,
            "limit": limit,
            "offset": offset,
            "trigger_type": trigger_type
        }

        self.get(url='/ec/item/v3/search/seller/keyword', headers=headers, params=param)
        return self.response

    def mkpl_global_search_keyword(self, headers, seller_id, date: str, lang, zipcode, filter_key_word: str, sign,
                                   force=0,
                                   limit=20, offset=0, trigger_type="search_active", from_page="mkpl_global_search"):
        """
        商家页搜索
        :param from_page:
        :param headers:
        :param seller_id:
        :param date:
        :param lang:
        :param zipcode:
        :param filter_key_word:
        :param sign:
        :param force:
        :param limit:
        :param offset:
        :param trigger_type:
        :return:
        """
        param = {
            "seller_id": seller_id,
            "date": date,
            "lang": lang,
            "zipcode": zipcode,
            "filter_key_word": filter_key_word,
            "sign": sign,
            "force": force,
            "limit": limit,
            "offset": offset,
            "trigger_type": trigger_type,
            "from_page": from_page
        }

        self.get(url='/ec/item/v3/search/seller/keyword', headers=headers, params=param)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
